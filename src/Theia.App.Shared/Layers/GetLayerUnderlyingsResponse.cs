namespace Theia.App.Shared.Layers;

public record GetLayerUnderlyingsResponse
{
    public required GetUnderlyingsResponseItem[] Items { get; init; } = [];
}

public record GetUnderlyingsResponseItem
{
    public required string Layer { get; init; }
    public required decimal Limit { get; init; }
    public required decimal? Excess { get; init; }
    public required decimal Premium { get; init; }
    public required UnderlyingStatus Status { get; init; }
    public required string Insurer { get; init; }
    public required decimal? RPM { get; init; }
    public required decimal? ILF { get; init; }
    public required GetUnderlyingsResponsePart[] Parts { get; init; } = [];
    public required bool IsWholesale { get; init; }
}

public record GetUnderlyingsResponsePart
{
    public required int Part { get; init; }
    public required decimal LineSize { get; init; }
    public required decimal Premium { get; init; }
    public required string Insurer { get; init; }
    public required bool IsQuotaShare { get; init; }
}
