using Microsoft.Data.SqlClient;
using Theia.App.Hangfire.Interfaces;
using Theia.App.Hangfire.Services;
using Theia.App.Hangfire.Workflows;
using Theia.Application.Services.Excel;
using Theia.Application.Services.Notifications;
using Theia.Application.UseCases.Account;
using Theia.Infrastructure.Interceptors;

namespace Theia.Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration, bool isTestingEnvironment)
    {
        services.AddScoped<SessionContextInterceptor>();
        
        services.AddDbContext<ApplicationDbContext>((sp, options) =>
        {
            options.UseSqlServer(configuration.GetConnectionString("DefaultConnection"))
                .AddInterceptors(sp.GetRequiredService<SessionContextInterceptor>());
            options.UseProjectables();
        });

        if (!isTestingEnvironment)
        {
            services.AddHangfire(globalConfiguration => globalConfiguration
                .SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
                .UseSimpleAssemblyNameTypeSerializer()
                .UseRecommendedSerializerSettings()
                .UseSqlServerStorage(
                    () => new SqlConnection(configuration.GetConnectionString("HangfireConnection")),
                    new SqlServerStorageOptions
                    {
                        CommandBatchMaxTimeout = TimeSpan.FromMinutes(5),
                        SlidingInvisibilityTimeout = TimeSpan.FromMinutes(5),
                        QueuePollInterval = TimeSpan.Zero,
                        UseRecommendedIsolationLevel = true,
                        DisableGlobalLocks = true
                    }));

            services.AddHangfireServer();
        }


        services.AddScoped<IApplicationDbContext, ApplicationDbContext>();

        services.Replace(ServiceDescriptor.Scoped<IUserValidator<ApplicationUser>, MultiTenantUserValidator>());
        services.Replace(ServiceDescriptor.Scoped<IRoleValidator<ApplicationRole>, MultiTenantRoleValidator>());

        //services.Configure<PasswordHasherOptions>(options =>
        //{
        //    options.IterationCount = 10000;
        //});

        //services.AddScoped<IPasswordHasher<ApplicationUser>, BCryptPasswordHasher<ApplicationUser>>();
        //services.Configure<BCryptPasswordHasherOptions>(options =>
        //{
        //    options.WorkFactor = 10;
        //    options.EnhancedEntropy = false;
        //});

        // X-CSRF-Token
        services.AddAntiforgery(options =>
        {
            options.HeaderName = "X-XSRF-Token";
            options.SuppressXFrameOptionsHeader = false;
        });

        services.AddHttpContextAccessor();
        services.AddAppSettings(configuration);

        services.AddScoped<IdentityErrorDescriber, LocalizedIdentityErrorDescriber>();
        services.AddScoped<IStorageProvider, StorageProvider>();
        services.AddScoped<IStorageFactory, StorageFactory>();
        services.AddScoped<ITenantResolverService, TenantResolverService>();
        services.AddScoped<IAppSeederService, AppSeederService>();
        services.AddScoped<IJobService, JobService>();
        services.AddScoped<IAnalysisService, AnalysisService>();
        services.AddScoped<ExcelProcessingService>();
        services.AddScoped<IFileStorageService, AzureStorageService>();
        services.AddScoped<IConfigReaderService, ConfigReaderService>();
        services.AddScoped<IManageUseCase, ManageUseCase>();
        services.AddScoped<IRoleUseCase, RoleUseCase>();
        services.AddScoped<IPermissionUseCase, PermissionUseCase>();
        services.AddScoped<IUserUseCase, UserUseCase>();
        services.AddScoped<ITenantUseCase, TenantUseCase>();
        services.AddScoped<IAppSettingsUseCase, AppSettingsUseCase>();
        services.AddScoped<IRegionUseCase, RegionUseCase>();
        services.AddScoped<IIndustryUseCase, IndustryUseCase>();
        services.AddScoped<ILossTypeUseCase, LossTypeUseCase>();
        services.AddScoped<IControlFrameworkCategoryUseCase, ControlFrameworkCategoryUseCase>();
        services.AddScoped<IControlFrameworkCategoryClauseUseCase, ControlFrameworkCategoryClauseUseCase>();
        services.AddScoped<IApplicationFormsUseCase, ApplicationFormUseCase>();
        services.AddScoped<NotificationService>();

        // Hangfire Jobs
        services.AddScoped<OrganisationSubmissionWorkflow>();
        services.AddScoped<SupplierSubmissionWorkflow>();
        services.AddScoped<IndicationRequestWorkflow>();

        return services;
    }
}