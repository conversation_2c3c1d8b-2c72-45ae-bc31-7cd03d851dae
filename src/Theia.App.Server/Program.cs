using Hangfire;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Hosting;
using Refit;
using Theia.App.Server;
using Theia.App.Server.Services;
using Theia.Application.Common.Interfaces.Services.IdentityServices;
using Theia.App.Server.Services.IdentityServices;
using Theia.App.Server.Services.Webhooks;
using Theia.App.Shared;
using Theia.App.Shared.Admin;
using Theia.App.Shared.Broking;
using Theia.App.Shared.Converters;
using Theia.App.Shared.Login;
using Theia.App.Shared.Organisation;
using Theia.App.Shared.Underwriting;
using Theia.Application.Common.Managers;
using Theia.Application.Factories;
using Theia.Application.Interfaces;
using Theia.Application.Services;
using Theia.Application.Services.Endorsements;
using Theia.Application.Services.Indications;
using Theia.Application.Services.Layers;
using Theia.Application.Services.Layers.LayerPlacement;
using Theia.Application.Services.Organisations.Submissions;
using Theia.Application.Services.QuotaShares;
using Theia.Application.Services.Subjectivities;
using Theia.Application.Services.Suppliers;
using Theia.Application.Services.TenantHostname;
using Theia.Email.SendGrid;
using Theia.Infrastructure.Common.Interfaces;
using Theia.Infrastructure.Common.Permissions;
using Theia.Infrastructure.Common.Services;
using TheiaAP.Infrastructure.Middleware;
using Theia.Identity.Auth0;
using Theia.Infrastructure.Interfaces;
using Theia.Infrastructure.Services;
using Theia.Infrastructure.Services.EncryptionServices;

WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

bool isTestingEnvironment = builder.Environment.IsEnvironment("Testing");

builder.WebHost.ConfigureKestrel(options => 
        options.Limits.MaxRequestBodySize = int.MaxValue);

builder.Services.AddApplication();

if (!builder.Environment.IsDevelopment() && !isTestingEnvironment)
{
    builder.Services.AddApplicationInsightsTelemetry(config =>
        config.ConnectionString = builder.Configuration.GetConnectionString("APPLICATIONINSIGHTS_CONNECTION_STRING"));
}

builder.Services.AddHealthChecks();

builder.Services.AddAppLocalization();

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
}).AddJwtBearer(options =>
{
    options.Authority = builder.Configuration["Auth0Api:Authority"];
    options.Audience = builder.Configuration["Auth0Api:ApiIdOrAudience"];
});

builder.Services.AddControllers(options =>
    {
        options.SuppressImplicitRequiredAttributeForNonNullableReferenceTypes = true;
    })
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
        options.JsonSerializerOptions.PropertyNamingPolicy = null;
        options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
        options.JsonSerializerOptions.Converters.Add(new JsonWebSafeGuidConverter());
    });

builder.Services
    .AddFluentValidationAutoValidation()
    .AddFluentValidationClientsideAdapters()
    .AddValidatorsFromAssemblyContaining<IApplicationDbContext>()
    .AddValidatorsFromAssemblyContaining<TheiaWebApiSharedAssemblyMarker>()
    .AddValidatorsFromAssemblyContaining<TheiaAppSharedBrokingAssemblyMarker>()
    .AddValidatorsFromAssemblyContaining<TheiaAppSharedLoginAssemblyMarker>()
    .AddValidatorsFromAssemblyContaining<TheiaAppSharedOrganisationAssemblyMarker>()
    .AddValidatorsFromAssemblyContaining<TheiaAppSharedAssemblyMarker>()
    .AddValidatorsFromAssemblyContaining<ServerAssemblyMarker>()
    .AddValidatorsFromAssemblyContaining<TheiaAppSharedUnderwritingMarker>();

builder.Services.Configure<ApiBehaviorOptions>(options => { options.SuppressModelStateInvalidFilter = true; });

builder.Services.Configure<FormOptions>(x =>
{
    x.ValueLengthLimit = **********;
    x.MultipartBodyLengthLimit = **********; // In case of multipart
});

builder.Services.AddSwaggerApi(builder.Configuration);

builder.Services.AddCors(options =>
{
    options.AddPolicy("CorsPolicy", policyBuilder => policyBuilder.AllowAnyOrigin()
        .AllowAnyMethod()
        .AllowAnyHeader());
});

builder.Services.AddSignalR();

builder.Services.AddSingleton<SupplierSubmissionRequestExpirationService>();
builder.Services.AddSingleton<CurrenciesService>();

builder.Services.AddScoped<ISignalRContextProvider, SignalRContextProvider>();
builder.Services.AddScoped<IPermissionScannerService, PermissionScannerService>();
builder.Services.AddScoped<ICryptographyService, CryptographyService>();
builder.Services.AddScoped<IEncryptionService, EncryptionService>();
builder.Services.AddScoped<ICommonServices, CommonServices>();
builder.Services.AddScoped<ICommonQueryFactory, CommonQueryFactory>();
builder.Services.AddScoped<SubmissionQuestionPermissionService>();
builder.Services.AddScoped<ISubmissionPermissionService, SubmissionPermissionService>();
builder.Services.AddScoped<BrokingHousePermissionService>();
builder.Services.AddScoped<UrlService>();
builder.Services.AddScoped<SubmissionsService>();
builder.Services.AddScoped<IRoleService, RoleService>();
builder.Services.AddScoped<ScoreService>();
builder.Services.AddScoped<SupplierFormSnapshotPermissionService>();
builder.Services.AddScoped<ApplicationUserManager>();
builder.Services.AddScoped<OrganisationPermissionService>();
builder.Services.AddScoped<SupplierFilePermissionService>();
builder.Services.AddScoped<TenantHostnameService>();
builder.Services.AddScoped<RoleSynchronizer>();
builder.Services.AddScoped<SuppliersService>();
builder.Services.AddScoped<LayerPermissionService>();
builder.Services.AddScoped<IndicationsPermissionService>();
builder.Services.AddScoped<QuotaSharePermissionService>();
builder.Services.AddScoped<QuotaShareService>();
builder.Services.AddScoped<EndorsementsPermissionService>();
builder.Services.AddScoped<SubjectivitiesPermissionService>();
builder.Services.AddScoped<PlacementService>();

builder.Services.AddHttpContextAccessor();

builder.Services.AddInfrastructure(builder.Configuration, isTestingEnvironment);

builder.Services.Configure<IdentityOptions>(options =>
{
    options.User.RequireUniqueEmail = true;
    options.Password.RequiredLength =
        builder.Configuration.GetValue<int>($"{AppPasswordOptions.Section}:RequiredLength");
    options.Password.RequiredUniqueChars =
        builder.Configuration.GetValue<int>($"{AppPasswordOptions.Section}:RequiredUniqueChars");
    options.Password.RequireNonAlphanumeric =
        builder.Configuration.GetValue<bool>($"{AppPasswordOptions.Section}:RequireNonAlphanumeric");
    options.Password.RequireLowercase =
        builder.Configuration.GetValue<bool>($"{AppPasswordOptions.Section}:RequireLowercase");
    options.Password.RequireUppercase =
        builder.Configuration.GetValue<bool>($"{AppPasswordOptions.Section}:RequireUppercase");
    options.Password.RequireDigit = builder.Configuration.GetValue<bool>($"{AppPasswordOptions.Section}:RequireDigit");
    options.Lockout.AllowedForNewUsers =
        builder.Configuration.GetValue<bool>($"{AppLockoutOptions.Section}:AllowedForNewUsers");
    options.Lockout.MaxFailedAccessAttempts =
        builder.Configuration.GetValue<int>($"{AppLockoutOptions.Section}:MaxFailedAccessAttempts");
    options.Lockout.DefaultLockoutTimeSpan =
        TimeSpan.FromMinutes(
            builder.Configuration.GetValue<double>($"{AppLockoutOptions.Section}:DefaultLockoutTimeSpan"));
    options.SignIn.RequireConfirmedAccount = true;
});

builder.Services.AddSendGridEmailService(builder.Configuration);

if (!isTestingEnvironment)
{
    builder.Services.AddHangfireServer();
}

builder.Services.AddAuthorization(options =>
{
    foreach (PermissionTypes permissionType in Enum.GetValues<PermissionTypes>())
    {
        if (Permissions.GetPermission(permissionType) is not { } permission)
        {
            continue;
        }
        
        string permissionName = permission.BuildPermissionName();
        options.AddPolicy(permissionName,
            builder =>
            {
                builder.RequireClaim("permissions", permissionName);
            });
    } 
});

if (!isTestingEnvironment)
{
    builder.Services.AddAuth0Apis();
}

AddWebhooks(builder);

WebApplication app = builder.Build();

using (IServiceScope scope = app.Services.CreateScope())
{
    IServiceProvider services = scope.ServiceProvider;

    try
    {
        ApplicationDbContext context = services.GetRequiredService<ApplicationDbContext>();
        await context.Database.MigrateAsync().ConfigureAwait(true);

        await context.Database.ExecuteSqlRawAsync(SqlScripts.DeletionLogTriggers);
        
        IPermissionScannerService permissionScannerService = services.GetRequiredService<IPermissionScannerService>();
        IAppSeederService identitySeeder = services.GetRequiredService<IAppSeederService>();
        await ApplicationDbContextSeeder.SeedAsync(permissionScannerService, identitySeeder).ConfigureAwait(true);
    }
    catch (Exception ex)
    {
        ILogger<Program> logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

        logger.LogError(ex,
            $"An error occurred while migrating or seeding the database.| {ex.InnerException?.ToString() ?? ex.Message}");
    }
}

ServiceActivator.Configure(app.Services.CreateScope().ServiceProvider);

app.UseHealthChecks("/health");

//app.UseHttpsRedirection();

app.UseStaticFiles();

app.UseRouting();

app.UseCors("CorsPolicy");

app.UseAppLocalization();

app.UseSwaggerApi();

app.UseApiResponseAndExceptionWrapper(new AutoWrapperOptions
{
    IsApiOnly = false,
    UseApiProblemDetailsException = true,
    ShowStatusCode = true,
    WrapWhenApiPathStartsWith = "/api",
    BypassHTMLValidation = true,
    ExcludePaths =
    [
        new AutoWrapperExcludePath("/DashboardHub/.*|/DashboardHub", ExcludeMode.Regex),
        new AutoWrapperExcludePath("/api/submissions/files/download/.*", ExcludeMode.Regex),
        new AutoWrapperExcludePath("/api/suppliers/files/.*/download", ExcludeMode.Regex),
        new AutoWrapperExcludePath("/api/policy-forms/.*/file", ExcludeMode.Regex)
    ]
});

app.UseAuth();

//app.UseHangfireDashboard(); // If you want to access the hangfire dashboard from outside the localhost, please refer to this link. https://docs.hangfire.io/en/latest/configuration/using-dashboard.html

app.UseMultiTenancy(options =>
{
    options.TenantMode = GetTenantMode();
});

app.MapControllers();

app.MapHub<DashboardHub>("Hubs/DashboardHub");

app.Run();

TenantMode GetTenantMode()
{
    return (TenantMode)Enum.Parse(typeof(TenantMode), builder.Configuration.GetValue<string>(TenantModeOptions.Section)
                                                      ?? throw new InvalidOperationException(
                                                          "TenantMode section is missing from the appsettings.json file."));
}

void AddWebhooks(WebApplicationBuilder webApplicationBuilder)
{
    string? webhooksOptions = 
        webApplicationBuilder.Configuration
            .GetValue<string>($"{WebhooksOptions.SectionName}:{nameof(WebhooksOptions.TheiaAdminsUrl)}");
    
    if (webhooksOptions is null)
    {
        throw new InvalidOperationException("No webhooks configuration");
    }

    webApplicationBuilder.Services
        .AddRefitClient<IWebhookHttpService>()
        .ConfigureHttpClient(httpClient => httpClient.BaseAddress = new Uri(webhooksOptions));

    webApplicationBuilder.Services.AddScoped<IWebhookService, WebhookService>();
}