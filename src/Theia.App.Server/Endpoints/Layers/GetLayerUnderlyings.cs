using Ardalis.ApiEndpoints;
using Microsoft.EntityFrameworkCore;
using Theia.App.Shared.Layers;
using Theia.App.Shared.Submissions.ProgramStack.GetSubmissionProgramStack;
using Theia.Application.Services;
using Theia.Application.Services.IndicationRequests;
using Theia.Application.Services.Layers;
using Theia.Application.Services.Layers.LayerPlacement;
using Theia.Application.Services.Organisations.Submissions;
using Theia.Infrastructure.Common.Constants;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Endpoints.Layers;

[ApiAuthorize(PermissionType = PermissionTypes.GetDashboardData)]
public class GetUnderlyings(
    IApplicationDbContext dbContext,
    IRoleService roleService,
    ITenantResolverService tenantResolverService,
    IUserService userService,
    LayerPermissionService layerPermissionService,
    ISubmissionPermissionService submissionPermissionService,
    PlacementService placementService) 
    : EndpointBaseAsync.WithRequest<GetLayerUnderlyingsRequest>.WithResult<GetLayerUnderlyingsResponse>
{
    [HttpGet("api/layers/{LayerId:guid:required}/underlyings")]
    [HttpGet("api/layers/{LayerId:guid:required}/quote-requests/{QuoteRequestId:guid}/underlyings")]
    [HttpGet("api/wholesale-submission/{WholesaleSubmissionId:guid}/underlyings")]
    public override async Task<GetLayerUnderlyingsResponse> HandleAsync(
        [FromRoute] GetLayerUnderlyingsRequest request, 
        CancellationToken cancellationToken = new())
    {
        Guid? layerId = request.LayerId;
        if (request.WholesaleSubmissionId is not null)
        {
            await submissionPermissionService.CheckReadAccessOrThrowAsync(cancellationToken, submissionId: request.WholesaleSubmissionId);
            var layerInformation = await dbContext.WholesaleSubmissions
                .Where(x => x.Id == request.WholesaleSubmissionId)
                .Select(x => new
                {
                    x.IndicationRequest.LayerId,
                    x.IndicationRequest.ShowUnderlying
                })
                .SingleOrDefaultAsync(cancellationToken);
            
            if (layerInformation is null)
            {
                throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
            }

            if (!layerInformation.ShowUnderlying)
            {
                throw new ApiProblemDetailsException(Resource.You_are_not_allowed_to_see_underlyings, StatusCodes.Status403Forbidden);
            }
            
            layerId = layerInformation.LayerId;
        }
        else
        {
            if (roleService.IsUserInInsurerRole && request.QuoteRequestId.HasValue)
            {
                Guid insurerId = tenantResolverService.EnsureTenantId();
                string userId = userService.EnsureAuthId();
            
                bool? showUnderlying =
                    await dbContext.BrokerSubmissions
                        .Where(x => 
                            x.Id == request.QuoteRequestId 
                            && IndicationRequestPermissionService.CheckUnderwriterReadAccess(x, insurerId, userId, roleService.IsUserInUnderwriterAdminRole))
                        .Select(x => (bool?)x.ShowUnderlying)
                        .SingleOrDefaultAsync(cancellationToken)
                        .ConfigureAwait(false);

                if (!showUnderlying.HasValue)
                {
                    throw new ApiProblemDetailsException(Resource.Not_found, StatusCodes.Status404NotFound);
                }

                if (!showUnderlying.Value)
                {
                    throw new ApiProblemDetailsException(Resource.You_are_not_allowed_to_see_underlyings, StatusCodes.Status403Forbidden);
                }
            }
            else
            {
                await layerPermissionService.CheckOrThrowAsync(layerId.Value, cancellationToken).ConfigureAwait(false);
            }
        }

        LayerProjection[] previousLayers =
            await dbContext.Layers
                .Where(l =>
                    l.SubmissionId == dbContext.Layers
                        .Where(l1 => l1.Id == layerId)
                        .Select(l1 => l1.SubmissionId)
                        .Single()
                    && EF.Property<DateTimeOffset>(l, ShadowProperties.CreatedOn) <
                    dbContext.Layers.Where(current => current.Id == layerId)
                        .Select(current => EF.Property<DateTimeOffset>(current, ShadowProperties.CreatedOn))
                        .Single())
                .OrderBy(l => EF.Property<DateTimeOffset>(l, ShadowProperties.CreatedOn))
                .Select(l => new LayerProjection
                {
                    Id = l.Id,
                    Limit = l.Limit,
                    CreatedOn = EF.Property<DateTimeOffset>(l, ShadowProperties.CreatedOn),
                    Excess = l.Excess ?? 0,
                    SubmissionType = l.Submission.SubmissionType,
                    IndicationRequests = l.IndicationRequests!.Select(ir => new IndicationRequestProjection
                    {
                        Id = ir.Id,
                        WholesaleSubmissions = ir.WholesaleSubmissions.Select(ws => new WholesaleSubmissionProjection
                        {
                            Layers = ws.Layers.Select(wsl => new LayerProjection
                            {
                                Id = wsl.Id,
                                Limit = wsl.Limit,
                                Excess = wsl.Excess ?? 0,
                                SubmissionType = wsl.Submission.SubmissionType,
                                CreatedOn = EF.Property<DateTimeOffset>(wsl, ShadowProperties.CreatedOn),
                                IndicationRequests = wsl.IndicationRequests.Select(wslir => new IndicationRequestProjection
                                {
                                    Id = wslir.Id,
                                    WholesaleSubmissions = Array.Empty<WholesaleSubmissionProjection>(),
                                    Indications = wslir.Indications.Select(wsli => new IndicationProjection
                                    {
                                        IndicationOptions = wsli.IndicationOptions.Select(wslio => new IndicationOptionProjection
                                        {
                                            IsSelected = wslio.IsSelected,
                                            Premium = wslio.Premium,
                                            InsurerName = wslio.Indication.Insurer!.Name,
                                            InsurerLine = wslio.InsurerLine,
                                            HasQuotaShareFollowers = wslio.QuotaShare != null && wslio.QuotaShare.Followers!.Any()
                                        }).ToArray()
                                    }).ToArray()
                                }).ToArray()
                            }).ToArray()
                        }).ToArray(),
                        Indications = ir.Indications.Select(i => new IndicationProjection
                        {
                            IndicationOptions = i.IndicationOptions.Select(io => new IndicationOptionProjection
                            {
                                IsSelected = io.IsSelected,
                                Premium = io.Premium,
                                InsurerName = io.Indication.Insurer!.Name,
                                InsurerLine = io.InsurerLine,
                                HasQuotaShareFollowers = io.QuotaShare != null && io.QuotaShare.Followers!.Any()
                            }).ToArray()
                        }).ToArray()
                    }).ToArray()
                })
                .ToArrayAsync(cancellationToken)
                .ConfigureAwait(false);

        IEnumerable<GetUnderlyingsResponseItem> items =
            previousLayers.Select((layer, index) =>
            {
                BrokerSubmissionData[] brokerSubmissions;

                IndicationRequestProjection? wholesaleIndicationRequest = layer.IndicationRequests
                    .FirstOrDefault(qr =>
                        qr.WholesaleSubmissions is not null
                        && qr.WholesaleSubmissions.Any()
                        && qr.WholesaleSubmissions.Any(ws
                            => ws.Layers.Any(l
                                => l.IndicationRequests.Any(ir =>
                                    ir.Indications.Any()))));
                if (wholesaleIndicationRequest is not null)
                {
                    brokerSubmissions = [new BrokerSubmissionData
                    {
                        Id = wholesaleIndicationRequest.Value.Id,
                        SelectedOptions =
                            [wholesaleIndicationRequest.Value.WholesaleSubmissions!
                                .Select(ws =>
                                    (ws.Layers
                                        .OrderBy(l => l.CreatedOn)
                                        .FirstOrDefault()
                                        .IndicationRequests)
                                    .SelectMany(ir => ir.Indications)
                                    .SelectMany(i => i.IndicationOptions)
                                    .Where(o => o.IsSelected)
                                    .Select(o => new SelectedOptionData
                                    {
                                        Premium = o.Premium,
                                        InsurerLine = o.InsurerLine,
                                        InsurerName = o.InsurerName,
                                        IsQuotaShare = o.HasQuotaShareFollowers
                                    })
                                    .FirstOrDefault())
                                .FirstOrDefault()!]
                    }];
                    
                    if (brokerSubmissions.First().SelectedOptions.First() is null)
                    {
                        brokerSubmissions = [];
                    }
                    
                    brokerSubmissions = brokerSubmissions.Concat(GetBrokerSubmissionsForLayer(layer)).ToArray();
                }
                else
                {
                    brokerSubmissions = GetBrokerSubmissionsForLayer(layer).ToArray();
                }

                GetPlacementResponse layerPlacement = placementService.GetLayerPlacementAsync(layer.Id, cancellationToken).Result;
                decimal totalPremium = brokerSubmissions.Sum(bs => bs.SelectedOptions.Sum(o => o.Premium));

                bool hasSingleInsurer = brokerSubmissions is [{SelectedOptions.Count: 1}];
                string insurerDisplay = hasSingleInsurer ? brokerSubmissions[0].SelectedOptions[0].InsurerName : Resource.Part_Placement;
                GetUnderlyingsResponsePart[] parts = CreatePartsFromBrokerSubmissions(brokerSubmissions);

                decimal? rpm = null;
                decimal? ilf = null;
                if (layer.Limit > 0)
                {
                    rpm = layer.Limit > 0 ? totalPremium / layer.Limit * 1000 : 0;

                    if (index > 0)
                    {
                        ilf = CalculateIlf(layer, previousLayers[index - 1], rpm!.Value);
                    }
                }

                return new GetUnderlyingsResponseItem
                {
                    Layer = index == 0 ? Resource.Primary : index.ToString(),
                    Limit = layer.Limit,
                    Excess = layer.Excess,
                    Premium = totalPremium,
                    Status = layerPlacement.Layers is { Length: > 0 } && layerPlacement.Layers.All(x => x.IsFinished) ? UnderlyingStatus.Completed : UnderlyingStatus.Incomplete,
                    Insurer = insurerDisplay,
                    ILF = ilf,
                    RPM = rpm,
                    Parts = parts,
                    IsWholesale = layer.SubmissionType == SubmissionType.WholesaleBrokerSubmission
                };
            });

        return new GetLayerUnderlyingsResponse { Items = items.ToArray() };
    }

    private decimal? CalculateIlf(in LayerProjection currentLayer, in LayerProjection previousLayer, decimal rpm)
    {
        decimal? ilf = null;
        if (currentLayer.Limit <= 0 || previousLayer.Limit <= 0)
        {
            return ilf;
        }

        decimal previousLayerPremium = GetLayerTotalPremium(previousLayer);
        decimal previousRpm = previousLayerPremium / previousLayer.Limit * 1000;

        if (previousRpm > 0)
        {
            ilf = rpm / previousRpm * 10;
        }

        return ilf;
    }

    private static IEnumerable<BrokerSubmissionData> GetBrokerSubmissionsForLayer(in LayerProjection layer)
    {
        return layer.IndicationRequests?
            .Select(bs => new BrokerSubmissionData
            {
                Id = bs.Id,
                SelectedOptions =
                    bs.Indications!
                        .Where(i => i.IndicationOptions!.Any(o => o.IsSelected))
                        .SelectMany(i => i.IndicationOptions!)
                        .Where(o => o.IsSelected)
                        .Select(o => new SelectedOptionData
                        {
                            Premium = o.Premium,
                            InsurerLine = o.InsurerLine,
                            InsurerName = o.InsurerName,
                            IsQuotaShare = o.HasQuotaShareFollowers
                        })
                        .ToList()
            }) ?? [];
    }

    private decimal GetLayerTotalPremium(in LayerProjection layer)
    {
        return layer.IndicationRequests?
            .SelectMany(bs => bs.Indications ?? [])
            .SelectMany(i => i.IndicationOptions ?? [])
            .Where(o => o.IsSelected)
            .Sum(o => o.Premium) ?? 0;
    }

    private static GetUnderlyingsResponsePart[] CreatePartsFromBrokerSubmissions(ICollection<BrokerSubmissionData> brokerSubmissions)
    {
        List<GetUnderlyingsResponsePart> parts = [];
        int partIndex = 1;

        foreach (BrokerSubmissionData brokerSubmission in brokerSubmissions)
        {
            IEnumerable<GetUnderlyingsResponsePart> options =
                brokerSubmission
                    .SelectedOptions
                    .Select(option => new GetUnderlyingsResponsePart
                    {
                        Part = partIndex++,
                        LineSize = option.InsurerLine, 
                        Premium = option.Premium,
                        Insurer = option.InsurerName,
                        IsQuotaShare = option.IsQuotaShare
                    });
            
            parts.AddRange(options);
        }

        return parts.ToArray();
    }

    private readonly struct LayerProjection
    {
        public required Guid Id { get; init; }
        public required decimal Limit { get; init; }
        public required decimal Excess { get; init; }
        public required SubmissionType SubmissionType { get; init; }
        public required DateTimeOffset CreatedOn { get; init; }
        public required IndicationRequestProjection[] IndicationRequests { get; init; }
    }

    private readonly struct IndicationRequestProjection
    {
        public required Guid Id { get; init; }
        public required WholesaleSubmissionProjection[] WholesaleSubmissions { get; init; }
        public required IndicationProjection[] Indications { get; init; }
    }

    private readonly struct WholesaleSubmissionProjection
    {
        public required LayerProjection[] Layers { get; init; }
    }

    private readonly struct IndicationProjection
    {
        public required IndicationOptionProjection[] IndicationOptions { get; init; }
    }

    private readonly struct IndicationOptionProjection
    {
        public required bool IsSelected { get; init; }
        public required decimal Premium { get; init; }
        public required decimal InsurerLine { get; init; }
        public required bool HasQuotaShareFollowers { get; init; }
        public required string InsurerName { get; init; }
    }

    private class BrokerSubmissionData
    {
        public required Guid Id { get; init; }
        public required List<SelectedOptionData> SelectedOptions { get; init; }
    }

    private class SelectedOptionData
    {
        public required decimal Premium { get; init; }
        public required decimal InsurerLine { get; init; }
        public required string InsurerName { get; init; }
        public required bool IsQuotaShare { get; init; }
    }
}
