{"ConnectionStrings": {"DefaultConnection": "[Secret]", "HangfireConnection": "[Secret]", "AzureStorageConnection": "[Secret]", "AzuriteLocalConnection": "[Secret]"}, "Logging": {"LogLevel": {"Default": "Information", "Hangfire": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ClientApp": {"SingleTenantHostName": "https://localhost:7001", "MultiTenantHostName": "https://{0}.localhost:7001", "ConfirmEmailChangeUrl": "account/manage/ConfirmEmailChange?userId={0}&email={1}&code={2}", "ConfirmEmailUrlWithReturnUrl": "account/ConfirmEmail?userId={0}&code={1}&returnUrl={2}", "ConfirmEmailUrl": "account/ConfirmEmail?userId={0}&code={1}", "ResetPasswordUrl": "account/ResetPassword?code={0}", "TheiaLensSupportEmail": "<EMAIL>"}, "Jwt": {"SecurityKey": "[Secret]", "Issuer": "https://localhost:5001", "Audience": "https://localhost:5001", "TokenControlSalt": "TheiaLensTokenControlSalt", "TokenControlPassKey": "TheiaLensTokenControlPassKey", "ApplicationFormSignatureKey": "TheiaLensApplicationFormSignatureKey", "UrlDataKey": "TheiaLensUrlDataKey"}, "AppOptions": {"AppIdentityOptions": {"AppUserOptions": {"allowedUserNameCharacters": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+", "NewUsersActiveByDefault": true, "requireUniqueEmail": true}, "AppPasswordOptions": {"requiredLength": 5, "requiredUniqueChars": 5, "requireNonAlphanumeric": false, "requireLowercase": false, "requireUppercase": false, "requireDigit": false}, "AppLockoutOptions": {"allowedForNewUsers": true, "maxFailedAccessAttempts": 5, "defaultLockoutTimeSpan": 5}, "AppSignInOptions": {"requireConfirmedAccount": true}}, "AppTokenOptions": {"accessTokenUoT": 1, "accessTokenTimeSpan": 5, "refreshTokenUoT": 1, "refreshTokenTimeSpan": 60}, "AppFileStorageOptions": {"storageType": 1}, "AppClientsOptions": {"AdminAppSubdomain": "admin", "DynamicDomain": null, "StaticDomain": null}, "TenantModeOptions": 2}, "SendGridEmailConfiguration": {"EmailFrom": "<EMAIL>", "SendToDeveloperEmailAddress": "rafal.k<PERSON><PERSON>@theialens.com", "InvitationEmailTemplateId": "d-a50c0ad8f16e4c7da78d0d3c016c7873", "ForgotPasswordEmailTemplateId": "d-5fbc89cd17c54fb4b2a467fbeba7bb8a", "TheiaAdminOrgAdminNewAccountInvitation": "d-47b271aba30e4935a4e448b96060cf63", "BrokingAdminUserNewAccountInvitation": "d-7a5a41739d9947e7b46fb85f7aa1b3fd", "TheiaAdminBrokingAdminNewAccountInvitation": "d-3b89106a03bf4d5e9a873a8323c56ce2", "TheiaAdminOrgAdminNewAssociation": "d-c924a789f0f24699b08195596055a741", "TheiaAdminBrokingHouseAdminNewAssociation": "d-69a015a4731d4e4e9f56f0aec14d9cb9", "TheialensBrokerBrokerApproved": "d-7cbf90d65a3b4189a22eeef83653cbb3", "TheialensBrokingAdminBrokerApproved": "d-a69f6b328f51489e8795373442cdf9f5", "TheialensBrokerBrokerUnapproved": "d-9626f7138b144c0b9f5aff06dad6b2cd", "TheialensBrokingAdminBrokerUnapproved": "d-747b5e1ab4834dbfa01ff726146e7b3a", "TheiaBrokerCreateSubmissionNotifyOrganisationAdmin": "d-6a4bd28990a74ed5a2eb204acc60d24c", "TheiaOrganisationSubmitsSubmissionNotification": "d-d0d584e905e34234b572f82713619b7d", "TheiaBrokerVoidSubmissionNotifyOrganisationAdmin": "d-060623432702487da49277512ee00992", "TheiaBrokerRemoveApplicationFormNotifyOrganisationAdmin": "d-545fe25e116449d3bbc1d17899ca0c31", "TheialensEmailChange": "d-9da5e91a1baa4811a251746630f3e937", "TheiaLensSupplierInviteUser": "d-aab99a5a361d4909b4351e7236a2f301", "TheiaLensSupplierInviteAdmin": "d-dda712b70e6d4313aa3b379c6b28d449", "TheiaLensInsurerInviteUnderwriterAdmin": "d-734489e8d7fc4721b967e46ee0560517", "TheiaLensInsurerInviteUnderwriter": "d-897b3ef3f48f4eaa9705f963b1ca8a01", "TheiaLensInviteNewSupplierTemplate": "d-a326cef5145e4e878adb344b792029df", "TheiaLensConfirmEmailTemplate": "d-56352f68dece48a8abad48f71c1d40e9", "PasswordChangedTemplate": "d-b6f7b0f39e204810b816fd6325b984d1"}, "HealthChecksUI": {"HealthChecks": [{"Name": "HTTP-Api-Basic", "Uri": "https://localhost:5001/health"}], "EvaluationTimeOnSeconds": 10, "MinimumSecondsBetweenFailureNotifications": 60}, "TheiaUrls": {"TheiaLocalDomain": "https://localhost:5001/"}, "Auth0Api": {"AuthUri": "https://dev-theialens.uk.auth0.com/api/v2/", "Authority": "https://auth.theialens.xyz/", "AuthClientId": "udNNLterJDmpNHHEtEdoQ98KCvZGmjrD", "AuthClientSecret": "AOcekELP92_5E5_9bdsOgmHTMVaUYK7FgqJKIvnHb6pJlpUMVlIKEalqorXR7Qve", "ApiIdOrAudience": "theia_api", "Connection": {"Id": "con_A3UyEu4AHyfakx27", "Name": "Username-Password-Authentication"}, "Organisations": [{"Id": "org_7s5PSImgSSIXAoOP", "Type": 1}, {"Id": "org_v6ujr80XsHsAI0yk", "Type": 2}, {"Id": "org_ck3FRh72wvdm3IWR", "Type": 3}, {"Id": "org_NV8Ukiq8i6nz3NO9", "Type": 4}, {"Id": "org_Em6GVr56irg7d0O1", "Type": 5}]}, "Webhooks": {"TheiaAdminsUrl": ""}}