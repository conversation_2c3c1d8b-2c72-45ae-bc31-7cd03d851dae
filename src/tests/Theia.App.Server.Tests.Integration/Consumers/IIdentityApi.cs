using Refit;
using Theia.App.Shared;
using Theia.App.Shared.Admin.Identity.Users;
using Theia.App.Shared.Admin.Roles;
using Theia.Application.Features.Identity.Users.Commands.CreateUser;

namespace Theia.App.Server.Tests.Integration.Consumers;

public interface IIdentityApi
{
    [Get("/api/identity/roles/userCreation")]
    Task<IApiResponse<SuccessResult<GetRolesForCreatingUserResponse>>> GetRolesForCreatingUser();
    
    [Post("/api/identity/roles/sync")]
    Task<IApiResponse> SyncRoles();

    [Post("/api/identity/Users/<USER>")]
    Task<IApiResponse<SuccessResult<CreateUserResponse>>> CreateUser([Body] CreateUserCommand command);
    
    [Post("/api/identity/users/GetUsers")]
    Task<IApiResponse<SuccessResult<DataEnvelope<UserItem>>>> GetUsers([Body] GetUsersRequest request, CancellationToken cancellationToken = new());
}