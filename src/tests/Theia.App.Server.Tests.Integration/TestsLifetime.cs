using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using System.Data.Common;
using Testcontainers.MsSql;
using Theia.App.Server.Services.Webhooks;
using Theia.App.Server.Tests.Integration.Helpers;
using Theia.App.Server.Tests.Integration.Mocks;
using Theia.Application.Common.Interfaces.Services.StorageServices;
using Theia.Domain.Common.Enums;
using Theia.Domain.Entities;
using Theia.Domain.Entities.Identity;
using Theia.Domain.Entities.Settings;
using Theia.Email.Contract;
using Theia.Identity.Interface;
using Theia.Infrastructure.Persistence;

namespace Theia.App.Server.Tests.Integration;

public sealed class TestsLifetime : IAsyncLifetime
{
    private readonly MsSqlContainer msSqlContainer = new MsSqlBuilder().Build();

    public Task InitializeAsync()
    {
        return msSqlContainer.StartAsync();
    }

    public Task DisposeAsync()
    {
        return msSqlContainer.DisposeAsync().AsTask();
    }

    internal sealed class CustomWebApplicationFactory(TestsLifetime fixture) : WebApplicationFactory<Program>
    {
        private readonly string connectionString = fixture.msSqlContainer.GetConnectionString();

        protected override void ConfigureWebHost(IWebHostBuilder builder)
        {
            builder.ConfigureTestServices(services =>
            {
                services.Remove(services.SingleOrDefault(service => typeof(DbContextOptions<ApplicationDbContext>) == service.ServiceType));
                services.Remove(services.SingleOrDefault(service => typeof(DbConnection) == service.ServiceType));
                services.AddDbContext<ApplicationDbContext>((_, option) => option.UseSqlServer(connectionString));

                services.Replace(ServiceDescriptor.Scoped<IEmailService, EmailServiceMock>());
                services.Replace(ServiceDescriptor.Scoped<IWebhookService, WebhookServiceMock>());
                services.Replace(ServiceDescriptor.Scoped<IFileStorageService, StorageServiceMock>());
                services.Replace(ServiceDescriptor.Scoped<IAuthIdentityApi, AuthIdentityApiMock>());

                services.AddAuthentication(options =>
                    {
                        options.DefaultAuthenticateScheme = MockAuthenticationHandler.AuthenticationType;
                        options.DefaultChallengeScheme = MockAuthenticationHandler.AuthenticationType;
                    })
                    .AddScheme<AuthenticationSchemeOptions, MockAuthenticationHandler>(MockAuthenticationHandler.AuthenticationType, _ => { });
            });

            builder.UseEnvironment("Testing");
        }

        public void EnsureSuperAdminExists()
        {
            using IServiceScope scope = Services.CreateScope();
            ApplicationDbContext ctx = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            bool superAdminExists = ctx.Users.Any(x => x.Id == UsersConfiguration.SuperAdmin.Id);
            if (superAdminExists)
            {
                return;
            }

            Tenant tenant = ctx.Tenants.First(t => t.Type == TenantType.Admin);
            ApplicationUser user = new() {Id = UsersConfiguration.SuperAdmin.Id, Email = UsersConfiguration.SuperAdmin.Email};

            ctx.UserTenantControls.Add(new UserTenantControl {TenantId = default, Tenant = tenant, UserId = null, ApplicationUser = user});

            ctx.SaveChanges();
        }

        public void EnsureRolesExists()
        {
            using IServiceScope scope = Services.CreateScope();
            ApplicationDbContext ctx = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            if (!ctx.Roles.Any())
            {
                IEnumerable<ApplicationRole> roles = 
                    RolesConfiguration.AllRoles.Select(x => new ApplicationRole {Id = x.Id, AuthId = x.AuthId, Name = x.Name});
                ctx.Roles.AddRange(roles);

                ctx.SaveChanges();
            }
        }
    }
}