using Bogus;
using Theia.Infrastructure.Common.Constants;

namespace Theia.App.Server.Tests.Integration.Helpers;

public class TestUser
{
    public required string Id { get; init; }
    public required string Email { get; init; }
    public required string Role { get; init; }
    public required string Tenant { get; init; }
    public required string Name { get; init; }
}

public static class UsersConfiguration
{
    private const string EmailAddressTemplate = "theialensdev+{0}@gmail.com";
    private static string CreateEmail(string plusTag) => string.Format(EmailAddressTemplate, plusTag);
    
    internal static readonly Faker<TestUser> Faker = 
        new Faker<TestUser>()
            .RuleFor(u => u.Id, f => f.Random.Guid().ToString())
            .RuleFor(u => u.Email, f => CreateEmail(f.Person.UserName))
            .RuleFor(u => u.Name, f => f.Person.FirstName);
    
    public static readonly TestUser SuperAdmin = new()
    {
        Id = "superadmin",
        Email = CreateEmail("superadmin01"),
        Role = RolesConstant.TheiaSuperAdmin,
        Tenant = TenantsConfiguration.AdminTenant,
        Name = "Super Admin 01"
    };

    public static readonly TestUser Admin = new()
    {
        Id = "admin01",
        Email = CreateEmail("admin01"),
        Role = RolesConstant.TheiaAdmin,
        Tenant = TenantsConfiguration.AdminTenant,
        Name = "Admin 01"
    };
}