namespace Theia.App.Server.Tests.Integration.Helpers;

public static class HttpClientExtensions
{
    public static HttpClient AsUser(this HttpClient client, TestUser testUser)
    {
        client.DefaultRequestHeaders.Remove(HeadersConfiguration.Tenant);
        client.DefaultRequestHeaders.Add(HeadersConfiguration.Tenant, testUser.Tenant);
        
        client.DefaultRequestHeaders.Remove(HeadersConfiguration.Email);
        client.DefaultRequestHeaders.Add(HeadersConfiguration.Email, testUser.Email);
        
        client.DefaultRequestHeaders.Remove(HeadersConfiguration.Role);
        client.DefaultRequestHeaders.Add(HeadersConfiguration.Role, testUser.Role);
        
        client.DefaultRequestHeaders.Remove(HeadersConfiguration.Id);
        client.DefaultRequestHeaders.Add(HeadersConfiguration.Id, testUser.Id);
        
        return client;
    }
}