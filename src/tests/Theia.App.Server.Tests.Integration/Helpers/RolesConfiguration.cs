using Theia.Infrastructure.Common.Constants;
using Theia.Infrastructure.Common.Permissions;

namespace Theia.App.Server.Tests.Integration.Helpers;

internal static class RolesConfiguration
{
    public static HashSet<TestRole> AllRoles => 
        RolesPermissions.Mapping.Select(x => new TestRole {Name = x.Key}).ToHashSet();
    
    public static readonly TestRole SuperAdmin = new()
    {
        Name = RolesConstant.TheiaSuperAdmin
    };

    public static readonly TestRole Admin = new()
    {
        Name = RolesConstant.TheiaAdmin
    };
}

internal record TestRole
{
    public string Id { get; init; } = Guid.NewGuid().ToString();
    public string AuthId { get; init; } = Guid.NewGuid().ToString();
    public required string Name { get; init; }
}