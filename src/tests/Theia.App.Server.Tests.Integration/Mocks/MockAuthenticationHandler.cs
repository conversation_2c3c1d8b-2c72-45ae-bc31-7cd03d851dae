using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Primitives;
using System.Security.Claims;
using System.Text.Encodings.Web;
using Theia.App.Server.Tests.Integration.Helpers;
using Theia.Infrastructure.Common.Constants;

namespace Theia.App.Server.Tests.Integration.Mocks;

public class MockAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
{
    public const string AuthenticationType = "Test";
    
    public MockAuthenticationHandler(
        IOptionsMonitor<AuthenticationSchemeOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder,
        ISystemClock clock
    )
        : base(options, logger, encoder, clock)
    {
    }

    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        Request.Headers.TryGetValue(HeadersConfiguration.Email, out StringValues email);
        Request.Headers.TryGetValue(HeadersConfiguration.Id, out StringValues id);
        Request.Headers.TryGetValue(HeadersConfiguration.Role, out StringValues role);
        
        Claim[] claims =
        [
            new(ClaimTypes.NameIdentifier, id.ToString()), 
            new(ClaimTypes.Name, email.ToString()), 
            // new("sub", id.ToString()),
            new(ClaimTypes.Role, role.ToString())
        ];
        
        ClaimsIdentity identity = new(claims, AuthenticationType);
        ClaimsPrincipal principal = new(identity);
        AuthenticationTicket ticket = new(principal, AuthenticationType);

        return Task.FromResult(AuthenticateResult.Success(ticket));
    }
}