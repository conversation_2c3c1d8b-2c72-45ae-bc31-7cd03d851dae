using Microsoft.AspNetCore.Http;
using Theia.Application.Common.Interfaces.Services.StorageServices;
using Theia.Application.Common.Models;
using Theia.Domain.Common.Enums;

namespace Theia.App.Server.Tests.Integration.Mocks;

public sealed class StorageServiceMock : IFileStorageService
{
    public Task<UploadFileResult> UploadFileAsync(IFormFile formFile, string containerName, string blobName, CancellationToken ct, Dictionary<string, string> metadata = null)
    {
        return Task.FromResult(new UploadFileResult("test-uri", "test-file", "test-container"));
    }

    public Task<List<FileMetaData>> UploadMultipleFiles(IList<IFormFile> formFiles, string containerName, string fileNamePrefix, int defaultFileIndex = 0, string subContainerName = "attachments")
    {
        return Task.FromResult(new List<FileMetaData>());
    }

    public Task<UploadFileResult> EditFile(IFormFile formFile, string containerName, string oldFileUri, CancellationToken ct)
    {
        return Task.FromResult(new UploadFileResult("test-uri", "test-file", "test-container"));
    }

    public Task DeleteFileIfExists(string fileUri)
    {
        return Task.CompletedTask;
    }

    public Task DeleteFileIfExists(string blobName, string containerName)
    {
        return Task.CompletedTask;
    }

    public Task DeleteContainer(string containerName, string subContainerName)
    {
        return Task.CompletedTask;
    }

    public FileStatus GetFileState(IFormFile formFile, string oldUrl)
    {
        return FileStatus.Unchanged;
    }

    public Task<DownloadStreamResponse> DownloadStreamAsync(string blobName, string containerName, CancellationToken ct)
    {
        return Task.FromResult(new DownloadStreamResponse(Stream.Null, "test-content", "test-file"));
    }
}