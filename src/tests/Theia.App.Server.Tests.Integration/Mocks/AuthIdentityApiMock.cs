using Theia.App.Server.Tests.Integration.Helpers;
using Theia.Identity.Interface;
using Theia.Identity.Interface.CreateRole;
using Theia.Identity.Interface.CreateUser;
using Theia.Identity.Interface.Roles;
using Theia.Identity.Interface.UpdateUser;
using Theia.Infrastructure.Common.Constants;

namespace Theia.App.Server.Tests.Integration.Mocks;

public sealed class AuthIdentityApiMock : IAuthIdentityApi
{
    public Task<CreateUserResponse> CreateUserAsync(CreateUserRequest request, CancellationToken ct)
    {
        return Task.FromResult(new CreateUserResponse(Guid.NewGuid().ToString()));
    }

    public Task<bool> UpdateUserAsync(UpdateUserRequest request, CancellationToken ct)
    {
        return Task.FromResult(true);
    }

    public Task SendChangePasswordInvitationEmail(string email, CancellationToken ct)
    {
        return Task.CompletedTask;
    }

    public Task UpdateUsersNameAsync(UpdateUsersNameRequest request, CancellationToken ct)
    {
        return Task.CompletedTask;
    }

    public Task UpdateUsersEmailAsync(UpdateUsersEmailRequest request, CancellationToken ct)
    {
        return Task.CompletedTask;
    }

    public Task ConfirmUsersEmail(ConfirmUsersEmailRequest request, CancellationToken ct)
    {
        return Task.CompletedTask;
    }

    public Task SendChangePasswordEmailAsync(ChangeUsersPasswordRequest request, CancellationToken ct)
    {
        return Task.CompletedTask;
    }

    public Task RemoveUsersMfaAsync(string userId, CancellationToken ct)
    {
        return Task.CompletedTask;
    }

    public Task ChangeRolesAsync(string userId, string[] rolesToRemoveId, string[] newRolesId, CancellationToken ct)
    {
        return Task.CompletedTask;
    }

    public Task BlockUserAsync(string userId, CancellationToken ct)
    {
        return Task.CompletedTask;
    }

    public Task ResendEmailConfirmation(string userId, CancellationToken ct)
    {
        return Task.CompletedTask;
    }

    public Task<GetAllRolesResponse> GetAllRolesAsync(CancellationToken ct)
    {
        return Task.FromResult(new GetAllRolesResponse
        {
            Roles = RolesConfiguration.AllRoles.Select(x => new GetAllRolesResponse.Role
            {
                AuthId = x.AuthId,
                Name = x.Name
            }).ToArray()
        });
    }

    public Task<IEnumerable<IdentityPermission>> GetAllApiPermissionsAsync(CancellationToken ct)
    {
        return Task.FromResult<IEnumerable<IdentityPermission>>([]);
    }

    public Task ReplaceApiPermissionsAsync(IReadOnlyCollection<IdentityPermission> permissions, CancellationToken ct)
    {
        return Task.CompletedTask;
    }

    public Task<CreateRoleResponse> CreateRoleAsync(IdentityRole role, CancellationToken ct)
    {
        return Task.FromResult(new CreateRoleResponse
        {
            AuthId = Guid.NewGuid().ToString(),
            Name = Guid.NewGuid().ToString() 
        });
    }

    public Task<ICollection<IdentityPermission>> GetAllRolesPermissionsAsync(string roleAuthId, CancellationToken ct)
    {
        return Task.FromResult<ICollection<IdentityPermission>>([]);
    }

    public Task AssignRolePermissionsAsync(string roleAuthId, IReadOnlyCollection<IdentityPermission> permissions, CancellationToken ct)
    {
        return Task.CompletedTask;
    }

    public Task RemovePermissionsFromRoleAsync(string roleId, IReadOnlyCollection<IdentityPermission> permissionName, CancellationToken ct)
    {
        return Task.CompletedTask;
    }

    public Task UpdateRoleNameAsync(string authId, string roleName, CancellationToken ct)
    {
        return Task.CompletedTask;
    }
}