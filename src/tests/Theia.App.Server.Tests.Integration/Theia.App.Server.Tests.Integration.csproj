<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>

        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Bogus" Version="35.6.5" />
        <PackageReference Include="coverlet.collector" Version="6.0.0"/>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.21" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.21" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0"/>
        <PackageReference Include="Refit" Version="8.0.0" />
        <PackageReference Include="Testcontainers.MsSql" Version="4.8.1" />
        <PackageReference Include="xunit" Version="2.5.3"/>
        <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3"/>
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\Theia.App.Server\Theia.App.Server.csproj" />
      <ProjectReference Include="..\..\Theia.Infrastructure\Theia.Infrastructure.csproj" />
    </ItemGroup>

</Project>
