using Bogus;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.VisualStudio.TestPlatform.CommunicationUtilities.Resources;
using Refit;
using System.Net;
using System.Reflection;
using System.Text.Json;
using Telerik.DataSource;
using Theia.App.Server.Tests.Integration.Consumers;
using Theia.App.Server.Tests.Integration.Helpers;
using Theia.App.Shared;
using Theia.App.Shared.Admin.Identity.Users;
using Theia.App.Shared.Admin.Roles;
using Theia.Application.Features.Identity.Users.Commands.CreateUser;
using Theia.Infrastructure.Common.Constants;

namespace Theia.App.Server.Tests.Integration;

public class SuperAdminTests : IClassFixture<TestsLifetime>, IDisposable
{
    private readonly TestsLifetime.CustomWebApplicationFactory factory;
    private readonly HttpClient httpClient;
    private IBrokingHouseApi brokingHouseApi;
    private IIdentityApi identityApi;

    public SuperAdminTests(TestsLifetime fixture)
    {
        WebApplicationFactoryClientOptions clientOptions = new() {AllowAutoRedirect = false};

        factory = new TestsLifetime.CustomWebApplicationFactory(fixture);
        factory.EnsureSuperAdminExists();
        factory.EnsureRolesExists();
        
        httpClient = factory.CreateClient(clientOptions);
        httpClient.AsUser(UsersConfiguration.SuperAdmin);
        
        brokingHouseApi = RestService.For<IBrokingHouseApi>(httpClient);
        identityApi = RestService.For<IIdentityApi>(httpClient);
    }

    public void Dispose()
    {
        factory.Dispose();
    }

    [Fact]
    public async Task GetRolesForCreatingUser_ReturnsCorrectRoles()
    {
        IApiResponse<SuccessResult<GetRolesForCreatingUserResponse>> response = await identityApi.GetRolesForCreatingUser();
        GetRolesForCreatingUserResponseItem[]? roles = response.Content?.Result.Roles;
        
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        Assert.NotNull(roles);
        Assert.Equal(2, roles.Length);
        Assert.Contains(roles, role => role.Name == RolesConstant.TheiaAdmin);
        Assert.Contains(roles, role => role.Name == RolesConstant.TheiaSuperAdmin);
    }

    [Fact]
    public async Task CreateUser_TheiaAdmin_Succeeds()
    {
        IApiResponse<SuccessResult<GetRolesForCreatingUserResponse>> response = await identityApi.GetRolesForCreatingUser();
        GetRolesForCreatingUserResponseItem[]? roles = response.Content?.Result.Roles;
        
        Assert.True(response.IsSuccessStatusCode);
        Assert.NotNull(roles);
        Assert.Contains(roles, role => role.Name == RolesConstant.TheiaAdmin);

        (Guid roleId, string _) = roles.Single(x => x.Name == RolesConstant.TheiaAdmin);

        TestUser fakeUser = UsersConfiguration.Faker.Generate();
        
        CreateUserCommand createUserCommand = new()
        {
            Email = fakeUser.Email,
            Name = fakeUser.Name,
            AssignedRoleId = roleId.ToString()
        };

        IApiResponse<SuccessResult<CreateUserResponse>> createUserResponse = await identityApi.CreateUser(createUserCommand);
        
        Assert.True(createUserResponse.IsSuccessStatusCode);

        GetUsersRequest getUsersRequest = new()
        {
            DataSourceRequest = new DataSourceRequest()
        };

        IApiResponse<SuccessResult<DataEnvelope<UserItem>>> getUsersResponse = await identityApi.GetUsers(getUsersRequest);
        
        Assert.True(getUsersResponse.IsSuccessStatusCode);
        Assert.NotNull(getUsersResponse.Content?.Result.Data);
        Assert.Contains(getUsersResponse.Content.Result.Data, item => item.Email == createUserCommand.Email);
    }
    
    [Fact]
    public async Task CreateUser_TheiaAdmin_RandomRole_NotFound()
    {
        TestUser fakeUser = UsersConfiguration.Faker.Generate();
        
        CreateUserCommand createUserCommand = new()
        {
            Email = fakeUser.Email,
            Name = fakeUser.Name,
            AssignedRoleId = Guid.NewGuid().ToString()
        };

        IApiResponse<SuccessResult<CreateUserResponse>> createUserResponse = await identityApi.CreateUser(createUserCommand);
        ExceptionResult? exceptionResult = await createUserResponse.Error!.GetContentAsAsync<ExceptionResult>();

        Assert.Equal(HttpStatusCode.NotFound, createUserResponse.StatusCode);
        Assert.NotNull(exceptionResult);
        Assert.Equal(BackendResources.Resource.Role_not_found, exceptionResult.Title);
        
        GetUsersRequest getUsersRequest = new()
        {
            DataSourceRequest = new DataSourceRequest()
        };

        IApiResponse<SuccessResult<DataEnvelope<UserItem>>> getUsersResponse = await identityApi.GetUsers(getUsersRequest);
        
        Assert.True(getUsersResponse.IsSuccessStatusCode);
        Assert.NotNull(getUsersResponse.Content?.Result.Data);
        Assert.DoesNotContain(getUsersResponse.Content.Result.Data, item => item.Email == createUserCommand.Email);
    }
    
    [Fact]
    public async Task CreateUser_TheiaAdmin_NoEmail_BadRequest()
    {
        TestUser fakeUser = UsersConfiguration.Faker.Generate();
        
        CreateUserCommand createUserCommand = new()
        {
            Name = fakeUser.Name,
            AssignedRoleId = RolesConfiguration.Admin.Id
        };

        IApiResponse<SuccessResult<CreateUserResponse>> createUserResponse = await identityApi.CreateUser(createUserCommand);
        ExceptionResult? exceptionResult = await createUserResponse.Error!.GetContentAsAsync<ExceptionResult>();

        Assert.Equal(HttpStatusCode.InternalServerError, createUserResponse.StatusCode);
        Assert.NotNull(exceptionResult);
        
        GetUsersRequest getUsersRequest = new()
        {
            DataSourceRequest = new DataSourceRequest()
        };

        IApiResponse<SuccessResult<DataEnvelope<UserItem>>> getUsersResponse = await identityApi.GetUsers(getUsersRequest);
        
        Assert.True(getUsersResponse.IsSuccessStatusCode);
        Assert.NotNull(getUsersResponse.Content?.Result.Data);
        Assert.DoesNotContain(getUsersResponse.Content.Result.Data, item => item.Email == createUserCommand.Email);
    }
}
