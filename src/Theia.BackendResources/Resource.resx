<?xml version="1.0" encoding="utf-8"?>
<root>
    <!-- 
      Microsoft ResX Schema 
      
      Version 2.0
      
      The primary goals of this format is to allow a simple XML format 
      that is mostly human readable. The generation and parsing of the 
      various data types are done through the TypeConverter classes 
      associated with the data types.
      
      Example:
      
      ... ado.net/XML headers & schema ...
      <resheader name="resmimetype">text/microsoft-resx</resheader>
      <resheader name="version">2.0</resheader>
      <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
      <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
      <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
      <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
      <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
          <value>[base64 mime encoded serialized .NET Framework object]</value>
      </data>
      <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
          <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
          <comment>This is a comment</comment>
      </data>
                  
      There are any number of "resheader" rows that contain simple 
      name/value pairs.
      
      Each data row contains a name, and value. The row also contains a 
      type or mimetype. Type corresponds to a .NET class that support 
      text/value conversion through the TypeConverter architecture. 
      Classes that don't support this are serialized and stored with the 
      mimetype set.
      
      The mimetype is used for serialized objects, and tells the 
      ResXResourceReader how to depersist the object. This is currently not 
      extensible. For a given mimetype the value must be set accordingly:
      
      Note - application/x-microsoft.net.object.binary.base64 is the format 
      that the ResXResourceWriter will generate, however the reader can 
      read any of the formats listed below.
      
      mimetype: application/x-microsoft.net.object.binary.base64
      value   : The object must be serialized with 
              : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
              : and then encoded with base64 encoding.
      
      mimetype: application/x-microsoft.net.object.soap.base64
      value   : The object must be serialized with 
              : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
              : and then encoded with base64 encoding.
  
      mimetype: application/x-microsoft.net.object.bytearray.base64
      value   : The object must be serialized into a byte array 
              : using a System.ComponentModel.TypeConverter
              : and then encoded with base64 encoding.
      -->
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:import namespace="http://www.w3.org/XML/1998/namespace"/>
        <xsd:element name="root" msdata:IsDataSet="true">
            <xsd:complexType>
                <xsd:choice maxOccurs="unbounded">
                    <xsd:element name="metadata">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" use="required" type="xsd:string"/>
                            <xsd:attribute name="type" type="xsd:string"/>
                            <xsd:attribute name="mimetype" type="xsd:string"/>
                            <xsd:attribute ref="xml:space"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="assembly">
                        <xsd:complexType>
                            <xsd:attribute name="alias" type="xsd:string"/>
                            <xsd:attribute name="name" type="xsd:string"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="data">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"/>
                                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1"/>
                            <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3"/>
                            <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4"/>
                            <xsd:attribute ref="xml:space"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="resheader">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" type="xsd:string" use="required"/>
                        </xsd:complexType>
                    </xsd:element>
                </xsd:choice>
            </xsd:complexType>
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>2.0</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <data name="Access_token_timespan_cannot_be_null" xml:space="preserve">
    <value>Access token timespan cannot be null.</value>
  </data>
    <data name="Access_token_timespan_is_required" xml:space="preserve">
    <value>Access token timespan is required.</value>
  </data>
    <data name="Allowed_username_characters_are_required" xml:space="preserve">
    <value>Allowed username characters are required.</value>
  </data>
    <data name="An_unknown_failure_has_occurred" xml:space="preserve">
    <value>An unknown failure has occurred.</value>
  </data>
    <data name="Applicant_has_been_created_successfully" xml:space="preserve">
    <value>Applicant has been created successfully</value>
  </data>
    <data name="Applicant_has_been_deleted_successfully" xml:space="preserve">
    <value>Applicant has been deleted successfully.</value>
  </data>
    <data name="Applicant_has_been_updated_successfully" xml:space="preserve">
    <value>Applicant has been updated successfully.</value>
  </data>
    <data name="A_user_with_this_login_already_exists" xml:space="preserve">
    <value>A user with this login already exists.</value>
  </data>
  <data name="Failed_to_create_user" xml:space="preserve">
    <value>Failed to create user</value>
  </data>
    <data name="Cannot_disable_2FA_for_user_with_Id" xml:space="preserve">
    <value>Cannot disable Two-Factor Authentication for user with ID '{0}' as it's not currently enabled.</value>
  </data>
    <data name="Cannot_generate_recovery_codes" xml:space="preserve">
    <value>Cannot generate recovery codes for username '{0}' as they do not have Two-Factor Authentication enabled.</value>
  </data>
    <data name="Code_is_required" xml:space="preserve">
    <value>Code is required.</value>
  </data>
    <data name="Confirmation_link_to_change_email_has_been_sent" xml:space="preserve">
    <value>Confirmation link to change email has been sent. Please check your email.</value>
  </data>
    <data name="Confirm_password_is_required" xml:space="preserve">
    <value>Confirm Password is required.</value>
  </data>
    <data name="Confirm_your_email" xml:space="preserve">
    <value>Confirm your email</value>
  </data>
    <data name="Date_of_Birth_is_required" xml:space="preserve">
    <value>Date of Birth is required</value>
  </data>
    <data name="Deletion_of_entity_failed" xml:space="preserve">
    <value>Deletion of entity "{0}" ({1}) failed. {2}.</value>
  </data>
    <data name="Email_is_already_taken" xml:space="preserve">
    <value>Email '{0}' is already taken.</value>
  </data>
    <data name="Email_is_invalid" xml:space="preserve">
    <value>Email '{0}' is invalid.</value>
  </data>
    <data name="Entity_with_key_was_not_found" xml:space="preserve">
    <value>Entity "{0}" with key "{1}" was not found.</value>
  </data>
  <data name="Failed_to_update_user" xml:space="preserve">
    <value>Failed to update user</value>
  </data>
    <data name="Error_changing_user_name" xml:space="preserve">
    <value>Error changing user name.</value>
  </data>
    <data name="Error_changing_your_user_name" xml:space="preserve">
    <value>Error changing your user name.</value>
  </data>
    <data name="File_has_not_been_uploaded" xml:space="preserve">
    <value>File has not been uploaded.</value>
  </data>
    <data name="File_is_empty" xml:space="preserve">
    <value>File is empty.</value>
  </data>
    <data name="File_storage_settings_have_been_updated_successfully" xml:space="preserve">
    <value>File storage settings have been updated successfully.</value>
  </data>
    <data name="First_name_is_required" xml:space="preserve">
    <value>First name is required.</value>
  </data>
    <data name="Height_is_required" xml:space="preserve">
    <value>Height is required.</value>
  </data>
    <data name="Identity_settings_have_been_updated_successfully" xml:space="preserve">
    <value>Identity settings have been updated successfully.</value>
  </data>
    <data name="Incorrect_password" xml:space="preserve">
    <value>Incorrect password.</value>
  </data>
    <data name="Invalid_applicant_Id" xml:space="preserve">
    <value>Invalid applicant ID.</value>
  </data>
    <data name="Invalid_authenticator_code_entered" xml:space="preserve">
    <value>Invalid authenticator code entered.</value>
  </data>
  <data name="Invalid_data_found" xml:space="preserve">
    <value>Invalid data found</value>
  </data>
    <data name="Invalid_client_request" xml:space="preserve">
    <value>Invalid client request.</value>
  </data>
    <data name="Invalid_login_attempt" xml:space="preserve">
    <value>Invalid login attempt.</value>
  </data>
    <data name="Invalid_recovery_code_entered" xml:space="preserve">
    <value>Invalid recovery code entered.</value>
  </data>
    <data name="Invalid_role_Id" xml:space="preserve">
    <value>Invalid role ID.</value>
  </data>
    <data name="Invalid_tenant_name" xml:space="preserve">
    <value>Invalid tenant name.</value>
  </data>
    <data name="Invalid_token" xml:space="preserve">
    <value>Invalid token.</value>
  </data>
    <data name="Invalid_user_Id" xml:space="preserve">
    <value>Invalid user ID.</value>
  </data>
    <data name="Job_title_is_required" xml:space="preserve">
    <value>Job title is required.</value>
  </data>
    <data name="Last_name_is_required" xml:space="preserve">
    <value>Last name is required</value>
  </data>
    <data name="Lockout_is_not_enabled_for_this_user" xml:space="preserve">
    <value>Lockout is not enabled for this user.</value>
  </data>
    <data name="Maximum_allowed_number_of_attachments_must_not_exceed_3" xml:space="preserve">
    <value>Maximum allowed number of attachments must not exceed 3.</value>
  </data>
    <data name="Method_cannot_be_null" xml:space="preserve">
    <value>Method cannot be null.</value>
  </data>
    <data name="Minimum_allowed_number_of_attachments_must_be_at_least_1" xml:space="preserve">
    <value>Minimum allowed number of attachments must be at least 1.</value>
  </data>
    <data name="New_password_is_required" xml:space="preserve">
    <value>New password is required.</value>
  </data>
    <data name="New_password_must_be_at_least_6_characters" xml:space="preserve">
    <value>New password must be at least 6 characters.</value>
  </data>
    <data name="New_password_must_not_exceed_200_characters" xml:space="preserve">
    <value>New password must not exceed 200 characters.</value>
  </data>
    <data name="Old_password_is_required" xml:space="preserve">
    <value>Old password is required.</value>
  </data>
    <data name="Only_those_between_the_ages_of_18_and_28_are_allowed_for_enlisting" xml:space="preserve">
    <value>Only those between the ages of 18 and 28 are allowed for enlisting.</value>
  </data>
    <data name="Only_those_whose_BMI_between_18_5_and_24_9_are_allowed_for_enlisting" xml:space="preserve">
    <value>Only those whose BMI between 18.5 and 24.9 are allowed for enlisting.</value>
  </data>
    <data name="Only_those_whose_heights_between_100_and_250_with_normal_BMI_are_allowed_for_enlisting"
          xml:space="preserve">
    <value>Only those whose heights between 100 and 250 with normal BMI are allowed for enlisting.</value>
  </data>
    <data name="Only_those_who_weigh_between_50_and_200_with_normal_BMI_are_allowed_for_enlisting" xml:space="preserve">
    <value>Only those who weigh between 50 and 200 with normal BMI are allowed for enlisting.</value>
  </data>
    <data name="Optimistic_concurrency_failure" xml:space="preserve">
    <value>Optimistic concurrency failure, object has been modified.</value>
  </data>
    <data name="Passwords_must_be_at_least_length_characters" xml:space="preserve">
    <value>Passwords must be at least {0} characters.</value>
  </data>
    <data name="Passwords_must_have_at_least_one_digit" xml:space="preserve">
    <value>Passwords must have at least one digit ('0'-'9').</value>
  </data>
    <data name="Passwords_must_have_at_least_one_lowercase" xml:space="preserve">
    <value>Passwords must have at least one lowercase ('a'-'z').</value>
  </data>
    <data name="Passwords_must_have_at_least_one_non_alphanumeric_character" xml:space="preserve">
    <value>Passwords must have at least one non alphanumeric character.</value>
  </data>
    <data name="Passwords_must_have_at_least_one_uppercase" xml:space="preserve">
    <value>Passwords must have at least one uppercase ('A'-'Z').</value>
  </data>
    <data name="Password_cannot_contain_password" xml:space="preserve">
    <value>Password cannot contain 'password'.</value>
  </data>
    <data name="Password_cannot_contain_username" xml:space="preserve">
    <value>Password cannot contain username.</value>
  </data>
    <data name="Password_is_required" xml:space="preserve">
    <value>Password is required.</value>
  </data>
    <data name="Password_must_be_at_least_6_characters" xml:space="preserve">
    <value>Password must be at least 6 characters.</value>
  </data>
    <data name="Password_must_not_exceed_200_characters" xml:space="preserve">
    <value>Password must not exceed 200 characters.</value>
  </data>
    <data name="Password_reset_link_was_sent" xml:space="preserve">
    <value>Password reset link was sent. Please check your email.</value>
  </data>
    <data name="Please_confirm_your_account_by_clicking_here" xml:space="preserve">
    <value>Please confirm your account by &lt;a href='{0}'&gt;clicking here&lt;/a&gt;.</value>
  </data>
    <data name="Please_confirm_your_email" xml:space="preserve">
    <value>Please confirm your email.</value>
  </data>
    <data name="Please_reset_your_password_by_clicking_here" xml:space="preserve">
    <value>Please reset your password by &lt;a href='{0}'&gt;clicking here&lt;/a&gt;.</value>
  </data>
    <data name="Please_specify_the_application_tenant_mode" xml:space="preserve">
    <value>Please specify the application tenant mode.</value>
  </data>
    <data name="Profile_picture_is_required" xml:space="preserve">
    <value>Profile picture is required.</value>
  </data>
    <data name="Recovery_Code_is_required" xml:space="preserve">
    <value>Recovery Code is required.</value>
  </data>
    <data name="Recovery_Code_Redemption_Failed" xml:space="preserve">
    <value>Recovery Code Redemption Failed</value>
  </data>
    <data name="Refresh_token_timespan_cannot_be_null" xml:space="preserve">
    <value>Refresh token timespan cannot be null.</value>
  </data>
    <data name="Refresh_token_timespan_is_required" xml:space="preserve">
    <value>Refresh token timespan is required.</value>
  </data>
    <data name="Refresh_token_timespan_must_be_greater_than_access_token_expiry_time" xml:space="preserve">
    <value>Refresh token timespan must be greater than access token expiry time.</value>
  </data>
    <data name="Repeated_Ones_are_not_valid_Social_security_number" xml:space="preserve">
    <value>111111111 is not a valid Social security number.</value>
  </data>
    <data name="Repeated_Threes_are_not_valid_Social_security_number" xml:space="preserve">
    <value>333333333 is not a valid Social security number.</value>
  </data>
    <data name="Reset_your_password" xml:space="preserve">
    <value>Reset your password</value>
  </data>
    <data name="Role_has_been_created_successfully" xml:space="preserve">
    <value>Role has been created successfully.</value>
  </data>
    <data name="Role_has_been_deleted_successfully" xml:space="preserve">
    <value>Role has been deleted successfully.</value>
  </data>
    <data name="Role_has_been_updated_successfully" xml:space="preserve">
    <value>Role has been updated successfully.</value>
  </data>
    <data name="Role_name_is_already_taken" xml:space="preserve">
    <value>Role name '{0}' is already taken.</value>
  </data>
    <data name="Role_name_is_invalid" xml:space="preserve">
    <value>Role name '{0}' is invalid.</value>
  </data>
    <data name="Role_name_is_required" xml:space="preserve">
    <value>Role name is required.</value>
  </data>
    <data name="Social_security_number_is_required" xml:space="preserve">
    <value>Social security number is required</value>
  </data>
    <data name="Social_security_number_must_contain_only_9_digits" xml:space="preserve">
    <value>Social security number must contain only 9-digits.</value>
  </data>
    <data name="Social_security_number_must_not_contain_consecutive_digits" xml:space="preserve">
    <value>Social security number must not contain consecutive digits.</value>
  </data>
    <data name="Surname_is_required" xml:space="preserve">
    <value>Surname is required.</value>
  </data>
    <data name="Tenant_has_been_created_successfully" xml:space="preserve">
    <value>Tenant has been created successfully.</value>
  </data>
    <data name="The_applicant_is_not_found" xml:space="preserve">
    <value>The applicant is not found.</value>
  </data>
    <data name="The_password_and_confirmation_password_do_not_match" xml:space="preserve">
    <value>The password and confirmation password do not match.</value>
  </data>
    <data name="The_specified_role_is_already_registered" xml:space="preserve">
    <value>The specified role is already registered.</value>
  </data>
    <data name="The_specified_role_is_already_registered_in_the_given_tenant" xml:space="preserve">
    <value>The specified role is already registered in the given tenant.</value>
  </data>
    <data name="The_specified_username_and_email_are_already_registered" xml:space="preserve">
    <value>The specified username and email are already registered.</value>
  </data>
    <data name="The_specified_username_and_email_are_already_registered_in_the_given_tenant" xml:space="preserve">
    <value>The specified username and email are already registered in the given tenant.</value>
  </data>
    <data name="Token_settings_have_been_updated_successfully" xml:space="preserve">
    <value>Token settings have been updated successfully.</value>
  </data>
    <data name="Two_factor_authentication_code_is_required" xml:space="preserve">
    <value>Two factor authentication code is required.</value>
  </data>
    <data name="Two_factor_authentication_code_must_be_at_least_6_character_long" xml:space="preserve">
    <value>Two factor authentication code must be at least 6 character long.</value>
  </data>
    <data name="Two_factor_authentication_code_must_not_exceed_7_characters" xml:space="preserve">
    <value>Two factor authentication code must not exceed 7 characters.</value>
  </data>
    <data name="Two_factor_authentication_has_been_disabled" xml:space="preserve">
    <value>2Fa has been disabled. You can reenable Two-Factor Authentication when you setup an authenticator app.</value>
  </data>
    <data name="Two_factor_authentication_required" xml:space="preserve">
    <value>Two-factor authentication required.</value>
  </data>
    <data name="Unable_to_create_new_tenant_in_single_tenant_mode" xml:space="preserve">
    <value>Unable to create new tenant in single tenant mode.</value>
  </data>
    <data name="Unable_to_delete_static_role" xml:space="preserve">
    <value>Unable to delete static role.</value>
  </data>
    <data name="Unable_to_delete_static_user" xml:space="preserve">
    <value>Unable to delete static user.</value>
  </data>
    <data name="Unable_to_load_applicant" xml:space="preserve">
    <value>Unable to load applicant.</value>
  </data>
    <data name="Unable_to_load_role" xml:space="preserve">
    <value>Unable to load role.</value>
  </data>
    <data name="Unable_to_load_user" xml:space="preserve">
    <value>Unable to load user.</value>
  </data>
    <data name="Unable_to_update_static_user" xml:space="preserve">
    <value>Unable to update static user.</value>
  </data>
    <data name="Unexpected_error_occurred_deleting_user_with_Id" xml:space="preserve">
    <value>Unexpected error occurred deleting user with ID '{0}'.</value>
  </data>
    <data name="Unexpected_error_occurred_disabling_2FA" xml:space="preserve">
    <value>Unexpected error occurred disabling Two-Factor Authentication for user with ID '{0}'.</value>
  </data>
    <data name="Username_has_been_confirmed_successfully" xml:space="preserve">
    <value>Username has been confirmed successfully.</value>
  </data>
    <data name="Username_is_already_taken" xml:space="preserve">
    <value>Username '{0}' is already taken.</value>
  </data>
    <data name="Username_is_invalid" xml:space="preserve">
    <value>Username '{0}' is invalid, can only contain letters or digits.</value>
  </data>
    <data name="Username_is_required" xml:space="preserve">
    <value>Username is required.</value>
  </data>
    <data name="Username_must_be_at_least_6_characters" xml:space="preserve">
    <value>Username must be at least 6 characters.</value>
  </data>
    <data name="Username_must_not_exceed_200_characters" xml:space="preserve">
    <value>Username must not exceed 200 characters.</value>
  </data>
    <data name="User_already_has_a_password_set" xml:space="preserve">
    <value>User already has a password set.</value>
  </data>
    <data name="User_already_in_role" xml:space="preserve">
    <value>User already in role '{0}'.</value>
  </data>
    <data name="User_has_been_added_without_files" xml:space="preserve">
    <value>User has been added without files.</value>
  </data>
    <data name="User_has_been_created_successfully" xml:space="preserve">
    <value>User has been created successfully.</value>
  </data>
    <data name="User_has_been_deleted_successfully" xml:space="preserve">
    <value>User has been deleted successfully.</value>
  </data>
    <data name="User_has_been_updated_successfully" xml:space="preserve">
    <value>User has been updated successfully.</value>
  </data>
    <data name="User_has_been_updated_successfully_without_updating_his_her_roles_as_the_user_is_static_type"
          xml:space="preserve">
    <value>User has been updated successfully without updating his/her roles as the user is static type.</value>
  </data>
    <data name="User_Id_is_required" xml:space="preserve">
    <value>User Id is required.</value>
  </data>
    <data name="User_permissions_have_been_updated_successfully" xml:space="preserve">
    <value>User permissions have been updated successfully.</value>
  </data>
    <data name="User_with_Id_deleted" xml:space="preserve">
    <value>User with ID '{0}' has been deleted.</value>
  </data>
    <data name="User_with_Id_UserId_logged_in_with_2Fa" xml:space="preserve">
    <value>User with ID '{0}' logged in with Two-Factor Authentication.</value>
  </data>
  <data name="value_cannot_be_null" xml:space="preserve">
    <value>{0} cannot be null</value>
  </data>
  <data name="Product_in_use" xml:space="preserve">
    <value>Product in use and cannot be deleted</value>
  </data>
  <data name="Supplier_applicationn_form_version_not_found" xml:space="preserve">
    <value>Could not find Supplier Application Form</value>
  </data>
  <data name="Vendor_type_not_found" xml:space="preserve">
    <value>Vendor Type not found</value>
  </data>
  <data name="Bottom_50_percent" xml:space="preserve">
    <value>Bottom 50%</value>
  </data>
  <data name="Top_1_percent" xml:space="preserve">
    <value>Top 1%</value>
  </data>
    <data name="Verification_code_is_invalid" xml:space="preserve">
    <value>Verification code is invalid.</value>
  </data>
    <data name="Verification_email_has_been_sent" xml:space="preserve">
    <value>Verification email has been sent. Please check your email.</value>
  </data>
    <data name="Weight_is_required" xml:space="preserve">
    <value>Weight is required.</value>
  </data>
    <data name="Your_account_is_deactivated_Please_contact_your_administrator" xml:space="preserve">
    <value>Your account is deactivated. Please contact your administrator.</value>
  </data>
    <data name="Your_authenticator_app_has_been_verified" xml:space="preserve">
    <value>Your authenticator app has been verified.</value>
  </data>
    <data name="Your_authenticator_app_key_has_been_reset" xml:space="preserve">
    <value>Your authenticator app key has been reset, you will need to configure your authenticator app using the new key.</value>
  </data>
    <data name="Your_email_has_been_changed_successfully" xml:space="preserve">
    <value>Your email has been changed successfully.</value>
  </data>
    <data name="Your_email_has_been_successfully_changed" xml:space="preserve">
    <value>Your email has been successfully changed.</value>
  </data>
    <data name="Your_email_is_unchanged" xml:space="preserve">
    <value>Your email is unchanged.</value>
  </data>
    <data name="Your_password_has_been_changed" xml:space="preserve">
    <value>Your password has been changed.</value>
  </data>
    <data name="Your_password_has_been_reset" xml:space="preserve">
    <value>Your password has been reset.</value>
  </data>
    <data name="Your_password_has_been_set" xml:space="preserve">
    <value>Your password has been set.</value>
  </data>
    <data name="You_are_forbidden" xml:space="preserve">
    <value>You are forbidden to access {0}.</value>
  </data>
    <data name="You_are_locked_out" xml:space="preserve">
    <value>You're locked out.</value>
  </data>
    <data name="You_are_not_authorized" xml:space="preserve">
    <value>You are not authorized to access {0}.</value>
  </data>
    <data name="You_have_generated_new_recovery_codes" xml:space="preserve">
    <value>You have generated new recovery codes.</value>
  </data>
    <data name="You_have_successfully_created_a_new_account" xml:space="preserve">
    <value>You have successfully created a new account.</value>
  </data>
    <data name="Reference_name_is_required" xml:space="preserve">
    <value>Reference name is required.</value>
  </data>
    <data name="Phone_number_is_required" xml:space="preserve">
    <value>Phone number is_required.</value>
  </data>
  <data name="Invalid_url_payload" xml:space="preserve">
    <value>Provided URL is invalid</value>
  </data>
  <data name="Url_has_expired" xml:space="preserve">
    <value>This URL has expired and cannot be re-used</value>
  </data>
    <data name="Invalid_report_Id" xml:space="preserve">
    <value>Invalid report Id.</value>
  </data>
    <data name="Unable_to_load_report" xml:space="preserve">
    <value>Unable to load report.</value>
  </data>
    <data name="N_A" xml:space="preserve">
    <value>N/A</value>
  </data>
    <data name="Unable_to_update_static_role" xml:space="preserve">
    <value>Unable to update static role</value>
  </data>
    <data name="Storage_type_is_required" xml:space="preserve">
    <value>Storage type is required.</value>
  </data>
    <data name="Default_lockout_time_Span_is_required" xml:space="preserve">
    <value>Default lockout time Span is required</value>
  </data>
    <data name="Max_failed_access_attempt_is_required" xml:space="preserve">
    <value>Max failed access attempt is required.</value>
  </data>
    <data name="Required_length_is_required" xml:space="preserve">
    <value>Required length is required.</value>
  </data>
    <data name="Required_unique_characters_is_required" xml:space="preserve">
    <value>Required unique characters is required.</value>
  </data>
    <data name="Invalid_file_storage_Id" xml:space="preserve">
    <value>Invalid file storage Id.</value>
  </data>
    <data name="Invalid_token_settings_Id" xml:space="preserve">
    <value>Invalid token settings Id.</value>
  </data>
    <data name="Invalid_user_settings_Id" xml:space="preserve">
    <value>Invalid user settings Id</value>
  </data>
    <data name="Invalid_password_settings_Id" xml:space="preserve">
    <value>Invalid password settings Id.</value>
  </data>
    <data name="Invalid_lockout_settings_Id" xml:space="preserve">
    <value>Invalid lockout settings Id.</value>
  </data>
    <data name="Invalid_sign_in_settings_Id" xml:space="preserve">
    <value>Invalid sign in settings Id.</value>
  </data>
    <data name="Unable_to_add_static_role" xml:space="preserve">
    <value>Unable to add static role</value>
  </data>
    <data name="Tenant_name_is_required" xml:space="preserve">
    <value>Tenant name is required.</value>
  </data>
    <data name="Tenant_has_been_updated_successfully" xml:space="preserve">
    <value>Tenant has been updated successfully.</value>
  </data>
    <data name="Tenant_has_been_deleted_successfully" xml:space="preserve">
    <value>Tenant has been deleted successfully.</value>
  </data>
    <data name="The_user_has_no_2FA_enabled" xml:space="preserve">
    <value>The user has no 2FA enabled.</value>
  </data>
    <data name="Unable_to_enable_2FA" xml:space="preserve">
    <value>Unable to enable 2FA.</value>
  </data>
    <data name="Unable_to_reset_authenticator_keys" xml:space="preserve">
    <value>Unable to reset authenticator keys.</value>
  </data>
    <data name="Country_has_been_created_successfully" xml:space="preserve">
    <value>Country has been created successfully</value>
  </data>
    <data name="Country_has_been_updated_successfully" xml:space="preserve">
    <value>Country has been updated successfully</value>
  </data>
    <data name="Country_name_is_required" xml:space="preserve">
    <value>Country name is required</value>
  </data>
    <data name="Invalid_country_Id" xml:space="preserve">
    <value>Invalid country Id</value>
  </data>
    <data name="Invalid_region_Id" xml:space="preserve">
    <value>Invalid region Id</value>
  </data>
    <data name="Region_has_been_created_successfully" xml:space="preserve">
    <value>Region has been created successfully</value>
  </data>
    <data name="Region_has_been_updated_successfully" xml:space="preserve">
    <value>Region has been updated successfully</value>
  </data>
    <data name="Region_name_is_required" xml:space="preserve">
    <value>Region name is required</value>
  </data>
    <data name="Unable_to_load_country" xml:space="preserve">
    <value>Unable to load country</value>
  </data>
    <data name="Unable_to_load_region" xml:space="preserve">
    <value>Unable to load region</value>
  </data>
    <data name="Region_exposure_level_is_required" xml:space="preserve">
    <value>Region exposure level is required</value>
  </data>
    <data name="Country_exposure_level_is_required" xml:space="preserve">
    <value>Country exposure level is required</value>
  </data>
    <data name="Country_already_exist" xml:space="preserve">
    <value>Country already exist</value>
  </data>
    <data name="Region_already_exist" xml:space="preserve">
    <value>Region already exist</value>
  </data>
    <data name="Industry_already_exist" xml:space="preserve">
    <value>Industry already exist</value>
  </data>
    <data name="Industry_exposure_level_is_required" xml:space="preserve">
    <value>Industry exposure level is required</value>
  </data>
    <data name="Industry_has_been_created_successfully" xml:space="preserve">
    <value>Industry has been created successfully</value>
  </data>
    <data name="Industry_has_been_updated_successfully" xml:space="preserve">
    <value>Industry has been updated successfully</value>
  </data>
    <data name="Industry_name_is_required" xml:space="preserve">
    <value>Industry name is required</value>
  </data>
    <data name="Invalid_industry_Id" xml:space="preserve">
    <value>Invalid industry Id</value>
  </data>
    <data name="Invalid_loss_type_Id" xml:space="preserve">
    <value>Invalid loss type Id</value>
  </data>
    <data name="Loss_type_already_exist" xml:space="preserve">
    <value>Loss type already exist</value>
  </data>
    <data name="Loss_type_has_been_created_successfully" xml:space="preserve">
    <value>Loss type has been created successfully</value>
  </data>
    <data name="Loss_type_has_been_updated_successfully" xml:space="preserve">
    <value>Loss type has been updated successfully</value>
  </data>
    <data name="Loss_type_is_required" xml:space="preserve">
    <value>Loss type is required</value>
  </data>
    <data name="Unable_to_load_industry" xml:space="preserve">
    <value>Unable to load industry</value>
  </data>
    <data name="Unable_to_load_loss_type" xml:space="preserve">
    <value>Unable to load loss type</value>
  </data>
    <data name="Control_Framework_already_exist" xml:space="preserve">
    <value>Control Framework already exist</value>
  </data>
    <data name="Control_Framework_Category_already_exist" xml:space="preserve">
    <value>Control Framework Category already exist</value>
  </data>
    <data name="Control_Framework_Category_Clause_has_been_created_successfully" xml:space="preserve">
    <value>Control Framework Category Clause has been created successfully</value>
  </data>
    <data name="Control_Framework_Category_Clause_has_been_updated_successfully" xml:space="preserve">
    <value>Control Framework Category Clause has been updated successfully</value>
  </data>
    <data name="Control_Framework_Category_Clause_is_required" xml:space="preserve">
    <value>Control Framework Category Clause is required</value>
  </data>
    <data name="Control_Framework_Category_has_been_created_successfully" xml:space="preserve">
    <value>Control Framework Category has been created successfully</value>
  </data>
    <data name="Control_Framework_Category_has_been_updated_successfully" xml:space="preserve">
    <value>Control Framework Category has been updated successfully</value>
  </data>
    <data name="Control_Framework_Category_Id_is_required" xml:space="preserve">
    <value>Control Framework Category Id is required</value>
  </data>
    <data name="Control_Framework_Category_Name_already_exist" xml:space="preserve">
    <value>Control Framework Category Name already exist</value>
  </data>
    <data name="Control_Framework_Category_name_is_required" xml:space="preserve">
    <value>Control Framework Category name is required</value>
  </data>
    <data name="Control_Framework_has_been_created_successfully" xml:space="preserve">
    <value>Control Framework has been created successfully</value>
  </data>
    <data name="Control_Framework_has_been_updated_successfully" xml:space="preserve">
    <value>Control Framework has been updated successfully</value>
  </data>
    <data name="Control_Framework_Id_is_required" xml:space="preserve">
    <value>Control Framework Id is required</value>
  </data>
    <data name="Control_Framework_name_is_required" xml:space="preserve">
    <value>Control Framework name is required</value>
  </data>
    <data name="Invalid_Control_Framework_Category_Clause_Id" xml:space="preserve">
    <value>Invalid Control Framework Category Clause Id</value>
  </data>
    <data name="Invalid_Control_Framework_Category_Id" xml:space="preserve">
    <value>Invalid Control Framework Category Id</value>
  </data>
    <data name="Invalid_Control_Framework_Id" xml:space="preserve">
    <value>Invalid Control Framework Id</value>
  </data>
    <data name="Region_Id_is_required" xml:space="preserve">
    <value>Region Id is required</value>
  </data>
    <data name="Unable_to_load_Control_Framework" xml:space="preserve">
    <value>Unable to load Control Framework</value>
  </data>
    <data name="Unable_to_load_Control_Framework_Category" xml:space="preserve">
    <value>Unable to load Control Framework Category</value>
  </data>
    <data name="Unable_to_load_Control_Framework_Category_Clause" xml:space="preserve">
    <value>Unable to load Control Framework Category Clause</value>
  </data>
    <data name="Choice_has_been_created_successfully" xml:space="preserve">
    <value>Choice has been created successfully</value>
  </data>
    <data name="Choice_has_been_deleted_successfully" xml:space="preserve">
    <value>Choice has been deleted successfully</value>
  </data>
    <data name="Choice_has_been_updated_successfully" xml:space="preserve">
    <value>Choice has been updated successfully</value>
  </data>
    <data name="Choice_is_required" xml:space="preserve">
    <value>Choice is required</value>
  </data>
    <data name="Invalid_Choice_Id" xml:space="preserve">
    <value>Invalid Choice Id</value>
  </data>
    <data name="Invalid_ApplicationForm_Id" xml:space="preserve">
    <value>Invalid Application Form Id</value>
  </data>
    <data name="Invalid_ApplicationForm_Page_Id" xml:space="preserve">
    <value>Invalid Application Form Page Id</value>
  </data>
    <data name="Invalid_Question_Id" xml:space="preserve">
    <value>Invalid Question Id</value>
  </data>
    <data name="Application_form_has_been_created_successfully" xml:space="preserve">
    <value>Application form has been created successfully</value>
  </data>
    <data name="Form_has_been_updated_successfully" xml:space="preserve">
    <value>Form has been updated successfully</value>
  </data>
    <data name="ApplicationForm_Id_is_required" xml:space="preserve">
    <value>Application Form Id is required</value>
  </data>
    <data name="ApplicationForm_name_already_exist" xml:space="preserve">
    <value>Application Form name already exist</value>
  </data>
    <data name="ApplicationForm_Page_has_been_created_successfully" xml:space="preserve">
    <value>Application Form Page has been created successfully</value>
  </data>
    <data name="ApplicationForm_Page_has_been_deleted_successfully" xml:space="preserve">
    <value>Application Form Page has been deleted successfully</value>
  </data>
    <data name="ApplicationForm_Page_has_been_updated_successfully" xml:space="preserve">
    <value>Application Form Page has been updated successfully</value>
  </data>
    <data name="ApplicationForm_page_title_is_required" xml:space="preserve">
    <value>Application Form page title is required</value>
  </data>
    <data name="ApplicationForm_name_is_required" xml:space="preserve">
    <value>Application Form name is required</value>
  </data>
    <data name="Question_has_been_created_successfully" xml:space="preserve">
    <value>Question has been created successfully</value>
  </data>
    <data name="Question_has_been_deleted_successfully" xml:space="preserve">
    <value>Question has been deleted successfully</value>
  </data>
    <data name="Question_has_been_updated_successfully" xml:space="preserve">
    <value>Question has been updated successfully</value>
  </data>
    <data name="Question_Id_is_required" xml:space="preserve">
    <value>Question Id is required</value>
  </data>
    <data name="Question_is_required" xml:space="preserve">
    <value>Question is required</value>
  </data>
    <data name="Question_Page_Id_is_required" xml:space="preserve">
    <value>Question page Id is required</value>
  </data>
    <data name="Question_title_is_required" xml:space="preserve">
    <value>Question title is required</value>
  </data>
    <data name="The_Choice_was_not_found" xml:space="preserve">
    <value>The Choice was not found</value>
  </data>
    <data name="The_ApplicationForm_Page_was_not_found" xml:space="preserve">
    <value>The Application Form Page was not found</value>
  </data>
    <data name="The_Question_was_not_found" xml:space="preserve">
    <value>The Question was not found</value>
  </data>
    <data name="Unable_to_load_Choice" xml:space="preserve">
    <value>Unable to load Choice</value>
  </data>
    <data name="Unable_to_load_Question" xml:space="preserve">
    <value>Unable to load Question</value>
  </data>
    <data name="Unable_to_load_form" xml:space="preserve">
    <value>Unable to load form</value>
  </data>
    <data name="Unable_to_load_ApplicationForm_Page" xml:space="preserve">
    <value>Unable to load Application Form Page</value>
  </data>
    <data name="Invalid_tenant_Id" xml:space="preserve">
    <value>Invalid Tenant Id</value>
  </data>
    <data name="The_tenant_was_not_found" xml:space="preserve">
    <value>The Tenant was not found</value>
  </data>
    <data name="Unable_to_load_tenant" xml:space="preserve">
    <value>Unable to load Tenant</value>
  </data>
  <data name="Internal_error_with_an_email_service_occured" xml:space="preserve">
    <value>Internal error with an email service occured</value>
  </data>
  <data name="Invalid_role" xml:space="preserve">
      <value>Invalid role</value>
    </data>
  <data name="Cannot_create_roll" xml:space="preserve">
      <value>Role '{0}' failed to create</value>
    </data>
  <data name="Cannot_insert_user" xml:space="preserve">
      <value>Cannot insert user '{0}'</value>
    </data>
  <data name="Cannot_bind_user_to_role" xml:space="preserve">
    <value>Failed to bind user '{0}' to role '{1}'</value>
  </data>
  <data name="Organisation_already_exists" xml:space="preserve">
    <value>Organisation already exists</value>
  </data>
  <data name="Brokers_are_invalid" xml:space="preserve">
    <value>Brokers are invalid</value>
  </data>
  <data name="Invalid_command_parameters" xml:space="preserve">
    <value>The passed parameters have not been validated</value>
  </data>
    <data name="The_organisation_already_has_a_tenant" xml:space="preserve">
      <value>The organisation already has a tenant</value>
    </data>
    <data name="Tenant_with_this_subdomain_already_exists" xml:space="preserve">
      <value>Tenant with this subdomain already exists</value>
    </data>
    <data name="Request_doesn_t_exist" xml:space="preserve">
      <value>Request doesn't exist</value>
    </data>
    <data name="Control_framework_not_found" xml:space="preserve">
      <value>The Control Framework was not found</value>
    </data>
    <data name="Organisation_not_found" xml:space="preserve">
      <value>Organisation not found</value>
    </data>
    <data name="No_available_brokers_found" xml:space="preserve">
      <value>No available brokers found</value>
    </data>
    <data name="User_is_invalid" xml:space="preserve">
      <value>The specified user could not be found</value>
    </data>
    <data name="Invalid_Status" xml:space="preserve">
      <value>Invalid Status</value>
    </data>
    <data name="Currency_not_found" xml:space="preserve">
      <value>Currency not found</value>
    </data>
    <data name="Country_not_found" xml:space="preserve">
      <value>Country not found</value>
    </data>
    <data name="Organisation_Name" xml:space="preserve">
      <value>Organisation Name</value>
    </data>
    <data name="Currency_not_foud" xml:space="preserve">
      <value>Currency not foud</value>
    </data>
    <data name="Industry_not_found" xml:space="preserve">
      <value>Industry not found</value>
    </data>
    <data name="Only_5_contracts_are_required" xml:space="preserve">
      <value>Only 5 contracts are required</value>
    </data>
    <data name="Contract_not_found" xml:space="preserve">
      <value>Contract not found</value>
    </data>
    <data name="Country_revenue_not_found" xml:space="preserve">
      <value>Country revenue not found</value>
    </data>
    <data name="Industry_revenue_not_found" xml:space="preserve">
      <value>Industry revenue not found</value>
    </data>
    <data name="Data_centre_not_found" xml:space="preserve">
      <value>Data centre not found</value>
    </data>
    <data name="No_changes_made" xml:space="preserve">
      <value>No changes were made</value>
    </data>
    <data name="Version" xml:space="preserve">
      <value>Version</value>
    </data>
    <data name="Application_form_version_already_exist" xml:space="preserve">
      <value>Application form version already exists</value>
    </data>
    <data name="Version_not_found" xml:space="preserve">
      <value>Version not found</value>
    </data>
    <data name="Cannot_access_tenant" xml:space="preserve">
      <value>Cannot access this tenant</value>
    </data>
    <data name="Service_is_not_nullable" xml:space="preserve">
      <value>Service is not nullable</value>
    </data>
    <data name="Request_originated_from_an_invalid_application" xml:space="preserve">
      <value>Request originated from an invalid application</value>
    </data>
    <data name="No_tenant_id_provided" xml:space="preserve">
      <value>No tenant id provided</value>
    </data>
    <data name="Association_can_not_be_null" xml:space="preserve">
      <value>Association cannot be empty</value>
    </data>
    <data name="Survey_data_missing" xml:space="preserve">
      <value>Survey data is missing</value>
    </data>
    <data name="Application_form_version_not_found" xml:space="preserve">
      <value>Application form version not found</value>
    </data>
    <data name="Application_form_version_data_malformed" xml:space="preserve">
      <value>Application Form Version data malformed</value>
    </data>
    <data name="Form_already_signed" xml:space="preserve">
      <value>The form has already been signed</value>
    </data>
    <data name="Could_not_sort_data" xml:space="preserve">
      <value>Failed to sort data in the request</value>
    </data>
    <data name="Survey_answers_malformed" xml:space="preserve">
      <value>Survey answers malformed and cannot deseralise</value>
    </data>
    <data name="Submission_and_form_not_linked" xml:space="preserve">
      <value>The submitted application form is not linked to a submission</value>
    </data>
    <data name="Internal" xml:space="preserve">
      <value>Internal</value>
    </data>
    <data name="Report_failed" xml:space="preserve">
      <value>Reporting failed as value is null</value>
    </data>
    <data name="Application_Form_code_already_exist" xml:space="preserve">
      <value>Application Form code already exists</value>
    </data>
    <data name="Application_form_with_the_same_name_and_code_already_exists" xml:space="preserve">
      <value>Application form with the same name and code already exists</value>
    </data>
    <data name="Organisation_request_not_found" xml:space="preserve">
      <value>Organisation request not found</value>
    </data>
    <data name="Report_cannot_be_null" xml:space="preserve">
      <value>Report cannot be null for analysis</value>
    </data>
    <data name="Control_framework_category_clause_cannot_be_null" xml:space="preserve">
      <value>Control Framework Category Clause cannot be null</value>
    </data>
    <data name="Control_Framework_analysis_error" xml:space="preserve">
      <value>An error occured during the analysis</value>
    </data>
    <data name="Control_framework_category_cannot_be_null" xml:space="preserve">
      <value>Control Framework Category cannot be null</value>
    </data>
    <data name="Submission_cannot_be_null" xml:space="preserve">
      <value>Submission cannot be null</value>
    </data>
    <data name="Cannot_submit_already_submitted_form" xml:space="preserve">
      <value>Cannot submit already submitted form</value>
    </data>
    <data name="Application_form_answer_not_found" xml:space="preserve">
      <value>Application Form answers could not be found</value>
    </data>
    <data name="Question_not_found" xml:space="preserve">
      <value>Question not found</value>
    </data>
    <data name="Question_must_be_approved_in_order_to_add_an_answer" xml:space="preserve">
      <value>Question must be approved in order to add an answer</value>
    </data>
    <data name="You_are_forbidden_to_access_this_resource" xml:space="preserve">
      <value>You are forbidden to access this resource</value>
    </data>
    <data name="Submission_not_found" xml:space="preserve">
      <value>Submission not found</value>
    </data>
    <data name="Message_not_found" xml:space="preserve">
      <value>Message not found</value>
    </data>
    <data name="No_form_data_found" xml:space="preserve">
      <value>No form data found</value>
    </data>
    <data name="Value_cannot_be_empty" xml:space="preserve">
      <value>Value cannot be empty</value>
    </data>
    <data name="File_not_found" xml:space="preserve">
      <value>File not found</value>
    </data>
    <data name="Theia_json_cannot_be_null" xml:space="preserve">
      <value>Theia Json Format is not valid</value>
    </data>
    <data name="Yes" xml:space="preserve">
      <value>Yes</value>
    </data>
    <data name="No" xml:space="preserve">
      <value>No</value>
    </data>
    <data name="No_Data" xml:space="preserve">
      <value>No Data</value>
    </data>
    <data name="No_Name_Available" xml:space="preserve">
      <value>No Name Available</value>
    </data>
    <data name="Cannot_remove_file" xml:space="preserve">
      <value>Cannot remove file</value>
    </data>
    <data name="A_broking_house_user_can_be_added_only_to_a_broking_house" xml:space="preserve">
      <value>A broking house user can be added only to a broking house</value>
    </data>
    <data name="Underwriters" xml:space="preserve">
      <value>Underwriters</value>
    </data>
    <data name="The_code_is_invalid" xml:space="preserve">
      <value>The code is invalid</value>
    </data>
    <data name="Invalid_permission_used" xml:space="preserve">
      <value>Invalid permission used</value>
    </data>
    <data name="Broking_houses_unavailable_for_association_were_selected" xml:space="preserve">
      <value>Broking houses unavailable for association were selected</value>
    </data>
    <data name="Currency_code_cannot_be_null_or_empty" xml:space="preserve">
      <value>Currency code cannot be null or empty</value>
    </data>
    <data name="Quota_Share_Follower_must_be_in_Accepted_status" xml:space="preserve">
      <value>Quota Share Follower must be in Accepted status</value>
    </data>
    <data name="Line_Size_exceeds_broker_limit" xml:space="preserve">
      <value>Line Size exceeds broker limit</value>
    </data>
    <data name="Line_Size_exceeds_underwriter_limit" xml:space="preserve">
      <value>Line Size exceeds underwriter limit</value>
    </data>
    <data name="2FA_is_not_configured" xml:space="preserve">
      <value>2FA is not configured</value>
    </data>
    <data name="Invalid_username_or_password" xml:space="preserve">
      <value>Invalid username or password</value>
    </data>
    <data name="Cannot_edit_a_version_in_use" xml:space="preserve">
      <value>Cannot edit a version in use</value>
    </data>
    <data name="Invalid_suppliers" xml:space="preserve">
      <value>Invalid suppliers</value>
    </data>
    <data name="Supplier_is_not_found" xml:space="preserve">
      <value>Supplier is Not Found</value>
    </data>
    <data name="Supplier_not_found" xml:space="preserve">
      <value>Supplier not found</value>
    </data>
    <data name="Vendor_is_already_associated_with_the_organisation" xml:space="preserve">
      <value>Vendor is already associated with the organisation</value>
    </data>
    <data name="Could_not_find_encryption_key" xml:space="preserve">
      <value>Could not find the specified encryption key</value>
    </data>
    <data name="Tenant_header_needs_value" xml:space="preserve">
      <value>Tenant header has to have a value</value>
    </data>
    <data name="Unexpected_error_occured_when_downloading_the_file" xml:space="preserve">
      <value>Unexpected error occured when downloading the file</value>
    </data>
    <data name="Invalid_file_extension" xml:space="preserve">
      <value>Invalid file extension</value>
    </data>
    <data name="File_is_invalid" xml:space="preserve">
      <value>File is invalid</value>
    </data>
    <data name="File_is_too_large" xml:space="preserve">
      <value>File is too large</value>
    </data>
    <data name="Invalid_file_signature" xml:space="preserve">
      <value>Invalid file signature</value>
    </data>
    <data name="Submission_Request_Not_Found" xml:space="preserve">
      <value>Submission request not found</value>
    </data>
    <data name="Submission_request_cant_be_finished" xml:space="preserve">
      <value>Finishing submission request requires all forms to be completed</value>
    </data>
    <data name="No_answer_scores_found" xml:space="preserve">
      <value>No answers scores found</value>
    </data>
    <data name="No_question_choices" xml:space="preserve">
      <value>No question choices located, this WILL affect the score continuining on</value>
    </data>
    <data name="No_score_over_zero_found" xml:space="preserve">
      <value>No score over 0 was found</value>
    </data>
    <data name="Invalid_application_forms" xml:space="preserve">
      <value>Invalid application forms</value>
    </data>
    <data name="Not_found" xml:space="preserve">
      <value>Not found</value>
    </data>
    <data name="Incorrect_services_requested" xml:space="preserve">
      <value>Incorrect services requested</value>
    </data>
    <data name="Role_not_found" xml:space="preserve">
      <value>Role not found</value>
    </data>
    <data name="You_do_not_have_permission_to_edit_this_user" xml:space="preserve">
      <value>You do not have permission to edit this user</value>
    </data>
    <data name="Tooltip_open_analysis" xml:space="preserve">
      <value>Open analysis</value>
    </data>
    <data name="Top" xml:space="preserve">
      <value>Top</value>
    </data>
    <data name="Percent" xml:space="preserve">
      <value>Percent</value>
    </data>
    <data name="Endpoint_is_currently_unavailable" xml:space="preserve">
      <value>Endpoint is currently unavailable</value>
    </data>
    <data name="Couldn_t_find_latest_supplier_submission_request" xml:space="preserve">
      <value>Couldn't find latest supplier submission request</value>
    </data>
    <data name="Data_is_not_processable" xml:space="preserve">
      <value>Data is not processable</value>
    </data>
    <data name="User_does_not_belong_to_any_supplier" xml:space="preserve">
      <value>User does not belong to any supplier</value>
    </data>
    <data name="Supplier_not_found_or_your_organisation_is_not_associated_with_it" xml:space="preserve">
      <value>Supplier not found or your organisation is not associated with it</value>
    </data>
    <data name="One_of_permission_types_is_not_mapped_to_a_permission" xml:space="preserve">
      <value>One of permission types is not mapped to a permission</value>
    </data>
    <data name="Product_already_exists" xml:space="preserve">
      <value>Product already exists</value>
    </data>
    <data name="Theia_Admin" xml:space="preserve">
      <value>Theia Admin</value>
    </data>
    <data name="Can_t_create_a_new_assessment_because_the_supplier_association_is_inactive" xml:space="preserve">
      <value>Can't create a new assessment because the supplier association is inactive</value>
    </data>
    <data name="One_of_the_selected_users_can_t_be_assigned" xml:space="preserve">
      <value>One of the selected users can't be assigned</value>
    </data>
    <data name="A_head_of_cover_with_the_same_name_already_exists_in_the_policy" xml:space="preserve">
      <value>A head of cover with the same name already exists in the policy</value>
    </data>
    <data name="A_policy_form_with_the_same_name_already_exists" xml:space="preserve">
      <value>A policy form with the same name already exists</value>
    </data>
    <data name="Invalid_currency" xml:space="preserve">
      <value>Invalid currency</value>
    </data>
    <data name="Primary" xml:space="preserve">
      <value>Primary</value>
    </data>
    <data name="Status_change_is_invalid" xml:space="preserve">
      <value>Status change is invalid</value>
    </data>
    <data name="Endorsement_with_the_same_name_already_exists_" xml:space="preserve">
      <value>Endorsement with the same name already exists.</value>
    </data>
    <data name="Endorsement_with_the_same_name_already_exists" xml:space="preserve">
      <value>Endorsement with the same name already exists</value>
    </data>
    <data name="Subjectivity_with_the_same_name_already_exists" xml:space="preserve">
      <value>Subjectivity with the same name already exists</value>
    </data>
    <data name="Option_not_found" xml:space="preserve">
      <value>Option not found</value>
    </data>
    <data name="Quote_has_a_status_that_does_not_allow_option_selection" xml:space="preserve">
      <value>Quote has a status that does not allow option selection</value>
    </data>
    <data name="No_agreed_option_found_for_this_layer" xml:space="preserve">
      <value>No agreed option found for this layer</value>
    </data>
    <data name="Create_quote_request" xml:space="preserve">
      <value>Create Quote Request</value>
    </data>
    <data name="Quota_share_is_in_a_state_that_prevents_from_accepting_or_declining_it" xml:space="preserve">
      <value>Quota share is in a state that prevents from accepting or declining it</value>
    </data>
    <data name="Follow_market_cant_unfollow" xml:space="preserve">
      <value>Follow market is in a state that doesn't allow unfollowing</value>
    </data>
    <data name="Part_Placement" xml:space="preserve">
      <value>Part Placement</value>
    </data>
    <data name="You_are_not_allowed_to_see_underlyings" xml:space="preserve">
      <value>You are not allowed to see underlyings</value>
    </data>
    <data name="Wholesale_submission_not_found" xml:space="preserve">
      <value>Wholesale Submission Not Found</value>
    </data>
    <data name="Not_covered" xml:space="preserve">
      <value>Not Covered</value>
    </data>
    <data name="days" xml:space="preserve">
      <value>days</value>
    </data>
    <data name="TBC" xml:space="preserve">
      <value>TBC</value>
    </data>
    <data name="Neither_wholesale_or_organisation" xml:space="preserve">
      <value>The provided Submission is neither Wholesale or Organisation</value>
    </data>
    <data name="Unable_to_change_visibility_of_a_wholesale_submission_s_layer" xml:space="preserve">
      <value>Unable to change visibility of a wholesale submission's layer</value>
    </data>
    <data name="Layer_not_found" xml:space="preserve">
      <value>Layer not found</value>
    </data>
    <data name="Hrs" xml:space="preserve">
      <value>Hrs</value>
    </data>
    <data name="Inception" xml:space="preserve">
      <value>Inception</value>
    </data>
    <data name="Expected_date_not_found" xml:space="preserve">
      <value>Expected date not found</value>
    </data>
    <data name="The_worksheet_was_not_found" xml:space="preserve">
      <value>The worksheet was not found</value>
    </data>
    <data name="Not_all_required_fields_provided" xml:space="preserve">
      <value>Not all required fields are provided</value>
    </data>
    <data name="Invalid_supplier_details" xml:space="preserve">
      <value>Invalid supplier details</value>
    </data>
    <data name="Supplier_already_exists" xml:space="preserve">
      <value>Supplier already exists</value>
    </data>
    <data name="UpdateQuotaShareFollowerStatus_HandleAsync_Quota_share_is_incomplete__so_max_line_size_is_unknown" xml:space="preserve">
      <value>Quota share is incomplete, so max line size is unknown</value>
    </data>
    <data name="Line_size_exceeds_maximum_line_size" xml:space="preserve">
      <value>Line size exceeds maximum line size</value>
    </data>
    <data name="Notification_not_found" xml:space="preserve">
      <value>Notification not found</value>
    </data>
    <data name="Cant_share_wholesale_to_retail" xml:space="preserve">
      <value>You can't send a submission to broker if it doesn't have any options selected</value>
    </data>
    <data name="Head_of_cover_limit_bigger_than_quote_request" xml:space="preserve">
      <value>The coverage limit for the head of cover cannot exceed the limit specified in the quote request</value>
    </data>
    <data name="Validation_failed" xml:space="preserve">
      <value>Validation failed</value>
    </data>
</root>