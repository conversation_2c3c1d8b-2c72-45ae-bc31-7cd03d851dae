<?xml version="1.0" encoding="utf-8"?>
<root>
    <!-- 
      Microsoft ResX Schema 
      
      Version 2.0
      
      The primary goals of this format is to allow a simple XML format 
      that is mostly human readable. The generation and parsing of the 
      various data types are done through the TypeConverter classes 
      associated with the data types.
      
      Example:
      
      ... ado.net/XML headers & schema ...
      <resheader name="resmimetype">text/microsoft-resx</resheader>
      <resheader name="version">2.0</resheader>
      <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
      <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
      <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
      <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
      <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
          <value>[base64 mime encoded serialized .NET Framework object]</value>
      </data>
      <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
          <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
          <comment>This is a comment</comment>
      </data>
                  
      There are any number of "resheader" rows that contain simple 
      name/value pairs.
      
      Each data row contains a name, and value. The row also contains a 
      type or mimetype. Type corresponds to a .NET class that support 
      text/value conversion through the TypeConverter architecture. 
      Classes that don't support this are serialized and stored with the 
      mimetype set.
      
      The mimetype is used for serialized objects, and tells the 
      ResXResourceReader how to depersist the object. This is currently not 
      extensible. For a given mimetype the value must be set accordingly:
      
      Note - application/x-microsoft.net.object.binary.base64 is the format 
      that the ResXResourceWriter will generate, however the reader can 
      read any of the formats listed below.
      
      mimetype: application/x-microsoft.net.object.binary.base64
      value   : The object must be serialized with 
              : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
              : and then encoded with base64 encoding.
      
      mimetype: application/x-microsoft.net.object.soap.base64
      value   : The object must be serialized with 
              : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
              : and then encoded with base64 encoding.
  
      mimetype: application/x-microsoft.net.object.bytearray.base64
      value   : The object must be serialized into a byte array 
              : using a System.ComponentModel.TypeConverter
              : and then encoded with base64 encoding.
      -->
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:import namespace="http://www.w3.org/XML/1998/namespace"/>
        <xsd:element name="root" msdata:IsDataSet="true">
            <xsd:complexType>
                <xsd:choice maxOccurs="unbounded">
                    <xsd:element name="metadata">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" use="required" type="xsd:string"/>
                            <xsd:attribute name="type" type="xsd:string"/>
                            <xsd:attribute name="mimetype" type="xsd:string"/>
                            <xsd:attribute ref="xml:space"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="assembly">
                        <xsd:complexType>
                            <xsd:attribute name="alias" type="xsd:string"/>
                            <xsd:attribute name="name" type="xsd:string"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="data">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"/>
                                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1"/>
                            <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3"/>
                            <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4"/>
                            <xsd:attribute ref="xml:space"/>
                        </xsd:complexType>
                    </xsd:element>
                    <xsd:element name="resheader">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"/>
                            </xsd:sequence>
                            <xsd:attribute name="name" type="xsd:string" use="required"/>
                        </xsd:complexType>
                    </xsd:element>
                </xsd:choice>
            </xsd:complexType>
        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>2.0</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <data name="Abbreviated_Day_Names" xml:space="preserve">
    <value>Abbreviated Day Names</value>
  </data>
    <data name="Abbreviated_Month_Genitive_Names" xml:space="preserve">
    <value>Abbreviated Month Genitive Names</value>
  </data>
    <data name="Abbreviated_Month_Names" xml:space="preserve">
    <value>Abbreviated Month Names</value>
  </data>
    <data name="Access" xml:space="preserve">
    <value>Access</value>
  </data>
    <data name="Access_Token_TimeSpan" xml:space="preserve">
    <value>Access Token Timespan</value>
  </data>
    <data name="Account" xml:space="preserve">
    <value>Account</value>
  </data>
    <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
    <data name="Activated" xml:space="preserve">
    <value>Activated</value>
  </data>
    <data name="Processed" xml:space="preserve">
    <value>Processed</value>
  </data>
    <data name="Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Add_empty_supplier" xml:space="preserve">
    <value>Add empty supplier</value>
  </data>
    <data name="Add_Applicant" xml:space="preserve">
    <value>Add Applicant</value>
  </data>
    <data name="Add_Authenticator_App" xml:space="preserve">
    <value>Add Authenticator App</value>
  </data>
    <data name="Add_Country" xml:space="preserve">
    <value>Add Country</value>
  </data>
    <data name="Add_Industry" xml:space="preserve">
    <value>Add Industry</value>
  </data>
    <data name="Add_Loss_Type" xml:space="preserve">
    <value>Add Loss Type</value>
  </data>
    <data name="Add_New_File" xml:space="preserve">
    <value>Add New File</value>
  </data>
    <data name="Add_ApplicationForm" xml:space="preserve">
      <value>New Application Form</value>
    </data>
    <data name="Add_Reference" xml:space="preserve">
    <value>Add Reference</value>
  </data>
    <data name="Add_Region" xml:space="preserve">
    <value>Add Region</value>
  </data>
    <data name="Add_Role" xml:space="preserve">
    <value>Add Role</value>
  </data>
    <data name="Add_Roles" xml:space="preserve">
    <value>Add Roles</value>
  </data>
    <data name="Add_Selected_Roles" xml:space="preserve">
    <value>Add Selected Roles</value>
  </data>
    <data name="Add_Tenant" xml:space="preserve">
    <value>Add Tenant</value>
  </data>
    <data name="Add_transcripts" xml:space="preserve">
    <value>Add Transcripts</value>
  </data>
    <data name="Add_User" xml:space="preserve">
    <value>Add User</value>
  </data>
    <data name="All" xml:space="preserve">
    <value>All</value>
  </data>
    <data name="Allowed_for_new_users" xml:space="preserve">
    <value>Allowed for new users</value>
  </data>
    <data name="Allowed_username_characters" xml:space="preserve">
    <value>Allowed username characters</value>
  </data>
    <data name="Already_have_an_account" xml:space="preserve">
    <value>Already have an account?</value>
  </data>
    <data name="AM_Designator" xml:space="preserve">
    <value>AM Designator</value>
  </data>
    <data name="and" xml:space="preserve">
    <value>and</value>
  </data>
    <data name="Applicant" xml:space="preserve">
    <value>Applicant</value>
  </data>
    <data name="Applicants" xml:space="preserve">
    <value>Applicants</value>
  </data>
    <data name="Apply_for_Military" xml:space="preserve">
    <value>Apply for Military</value>
  </data>
    <data name="App_Info" xml:space="preserve">
    <value>App Info</value>
  </data>
    <data name="April" xml:space="preserve">
    <value>April</value>
  </data>
    <data name="Are_you_sure_you_want_to_change_the_settings" xml:space="preserve">
    <value>Are you sure you want to change the settings?</value>
  </data>
  <data name="Are_you_sure_you_want_to_delete_this_product" xml:space="preserve">
    <value>Are you sure you want to delete this product?</value>
  </data>
  <data name="Are_you_sure_you_want_to_delete_this_file" xml:space="preserve">
    <value>Are you sure you want to delete this file?</value>
  </data>
    <data name="Are_you_sure_you_want_to_delete_role_name" xml:space="preserve">
    <value>Are you sure you want to delete role '{0}'?</value>
  </data>
    <data name="Are_you_sure_you_want_to_delete_user_name" xml:space="preserve">
    <value>Are you sure you want to delete user '{0}'?</value>
  </data>
    <data name="Are_you_sure_you_want_to_remove_role_name" xml:space="preserve">
    <value>Are you sure you want to remove role '{0}'?</value>
  </data>
    <data name="Are_you_sure_you_want_to_save_applicant" xml:space="preserve">
    <value>Are you sure you want to save applicant?</value>
  </data>
    <data name="Are_you_sure_you_want_to_save_Control_Framework" xml:space="preserve">
    <value>Are you sure you want to save Control Framework</value>
  </data>
    <data name="Are_you_sure_you_want_to_save_Control_Framework_Category" xml:space="preserve">
    <value>Are you sure you want to save Control Framework Category</value>
  </data>
    <data name="Are_you_sure_you_want_to_save_Control_Framework_Category_Clause" xml:space="preserve">
    <value>Are you sure you want to save Control Framework Category Clause?</value>
  </data>
    <data name="Are_you_sure_you_want_to_save_country" xml:space="preserve">
    <value>Are you sure you want to save country</value>
  </data>
    <data name="Are_you_sure_you_want_to_save_industry" xml:space="preserve">
    <value>Are you sure you want to save industry</value>
  </data>
    <data name="Are_you_sure_you_want_to_save_loss_type" xml:space="preserve">
    <value>Are you sure you want to save loss type</value>
  </data>
    <data name="Are_you_sure_you_want_to_save_form" xml:space="preserve">
      <value>Are you sure you want to save the application form?</value>
    </data>
    <data name="Are_you_sure_you_want_to_save_region" xml:space="preserve">
    <value>Are you sure you want to save region</value>
  </data>
    <data name="Are_you_sure_you_want_to_save_role" xml:space="preserve">
    <value>Are you sure you want to save role?</value>
  </data>
  <data name="Are_you_sure_you_want_to_submit_supplier_request_with_no_files" xml:space="preserve">
    <value>Are you sure you want to submit this Assessment Request with file(s) not provided?</value>
  </data>
  <data name="Selected_duplicate_files" xml:space="preserve">
    <value>You have selected the same file multiple times</value>
  </data>
  <data name="Are_you_sure_you_want_to_submit_supplier_request" xml:space="preserve">
    <value>Are you sure you want to submit this Assessment Request?</value>
  </data>
    <data name="Are_you_sure_you_want_to_save_tenant" xml:space="preserve">
    <value>Are you sure you want to save tenant?</value>
  </data>
    <data name="Are_you_sure_you_want_to_save_user_profile" xml:space="preserve">
    <value>Are you sure you want to save user profile?</value>
  </data>
    <data name="Army" xml:space="preserve">
    <value>Army</value>
  </data>
    <data name="Assigned_Roles" xml:space="preserve">
    <value>Assigned Roles</value>
  </data>
    <data name="Assign_Permissions" xml:space="preserve">
    <value>Assign Permissions</value>
  </data>
    <data name="Assign_Permissions_for" xml:space="preserve">
    <value>Assign Permissions for</value>
  </data>
    <data name="Assign_Role" xml:space="preserve">
    <value>Assign Role</value>
  </data>
    <data name="August" xml:space="preserve">
    <value>August</value>
  </data>
    <data name="Authenticator_App" xml:space="preserve">
    <value>Authenticator App</value>
  </data>
    <data name="Authenticator_Code" xml:space="preserve">
    <value>Authenticator Code</value>
  </data>
    <data name="Authorization" xml:space="preserve">
    <value>Authorization</value>
  </data>
    <data name="Authorizing" xml:space="preserve">
    <value>Authorizing</value>
  </data>
    <data name="Avatar" xml:space="preserve">
    <value>Avatar</value>
  </data>
    <data name="Awesome" xml:space="preserve">
    <value>Awesome!</value>
  </data>
    <data name="Azure_File_Storage" xml:space="preserve">
    <value>Azure File Storage</value>
  </data>
  <data name="before_you_can_log_in_with_a_recovery_code" xml:space="preserve">
    <value>before you can log in with a recovery code.</value>
  </data>
    <data name="Biomass" xml:space="preserve">
    <value>Biomass</value>
  </data>
    <data name="Biomass1" xml:space="preserve">
    <value>Biomass</value>
  </data>
    <data name="BMI" xml:space="preserve">
    <value>BMI</value>
  </data>
    <data name="BMI_Details" xml:space="preserve">
    <value>A BMI of 25.0 or more is overweight, while the healthy range is 18.5 to 24.9. BMI applies to most adults 18-65 years.</value>
  </data>
    <data name="BMI_Info" xml:space="preserve">
    <value>Body Mass Index is a simple calculation using a person’s height and weight. The formula is BMI = kg/m2 where kg is a person’s weight in kilograms and m2 is their height in metres squared.</value>
  </data>
    <data name="Body_Mass_Index" xml:space="preserve">
    <value>Body Mass Index</value>
  </data>
    <data name="Bottom" xml:space="preserve">
    <value>Bottom</value>
  </data>
    <data name="Browser_Time_Zone" xml:space="preserve">
    <value>Browser Time Zone</value>
  </data>
    <data name="Builder" xml:space="preserve">
    <value>Builder</value>
  </data>
    <data name="Buy_Now" xml:space="preserve">
    <value>Buy Now</value>
  </data>
    <data name="Bytes" xml:space="preserve">
    <value>Bytes</value>
  </data>
    <data name="Calcium" xml:space="preserve">
    <value>Calcium</value>
  </data>
    <data name="Calendar" xml:space="preserve">
    <value>Calendar</value>
  </data>
    <data name="Calendar_Week_Rule" xml:space="preserve">
    <value>Calendar Week Rule</value>
  </data>
    <data name="Californium" xml:space="preserve">
    <value>Californium</value>
  </data>
    <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
    <data name="Cancel_Upload" xml:space="preserve">
    <value>Cancel Upload</value>
  </data>
    <data name="Carbon" xml:space="preserve">
    <value>Carbon</value>
  </data>
    <data name="Cerium" xml:space="preserve">
    <value>Cerium</value>
  </data>
    <data name="Cesium" xml:space="preserve">
    <value>Cesium</value>
  </data>
    <data name="Change_Email" xml:space="preserve">
    <value>Change Email</value>
  </data>
    <data name="Change_Password" xml:space="preserve">
    <value>Change Password</value>
  </data>
    <data name="Change_User_Password" xml:space="preserve">
    <value>Change User Password</value>
  </data>
    <data name="Change_your_account_settings" xml:space="preserve">
    <value>Change Your Account Settings</value>
  </data>
    <data name="Chernobyl_1" xml:space="preserve">
    <value>Chernobyl-1</value>
  </data>
    <data name="Chernobyl_2" xml:space="preserve">
    <value>Chernobyl-2</value>
  </data>
    <data name="Chernobyl_3" xml:space="preserve">
    <value>Chernobyl-3</value>
  </data>
    <data name="Chernobyl_4" xml:space="preserve">
    <value>Chernobyl-4</value>
  </data>
    <data name="Chlorine" xml:space="preserve">
    <value>Chlorine</value>
  </data>
    <data name="Choice_name_is_required" xml:space="preserve">
    <value>Choice name is required</value>
  </data>
    <data name="Chromium" xml:space="preserve">
    <value>Chromium</value>
  </data>
    <data name="Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
    <data name="Click" xml:space="preserve">
    <value>Click</value>
  </data>
    <data name="click_here_to_log_in" xml:space="preserve">
    <value>click here to log in</value>
  </data>
    <data name="Client_Side_Authorization" xml:space="preserve">
    <value>Client-Side Authorization</value>
  </data>
    <data name="Client_Side_Validation" xml:space="preserve">
    <value>Client-Side Validation</value>
  </data>
    <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
    <data name="Close_SignalR_Connection" xml:space="preserve">
    <value>Close SignalR Connection</value>
  </data>
    <data name="Coal" xml:space="preserve">
    <value>Coal</value>
  </data>
    <data name="Coal1" xml:space="preserve">
    <value>Coal</value>
  </data>
    <data name="Cobalt" xml:space="preserve">
    <value>Cobalt</value>
  </data>
    <data name="Code" xml:space="preserve">
    <value>Code</value>
  </data>
    <data name="Code_Samples" xml:space="preserve">
    <value>Code Samples</value>
  </data>
    <data name="Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="Supplier_Assessments" xml:space="preserve">
    <value>Supplier Assessments</value>
  </data>
  <data name="No_suppliers_added" xml:space="preserve">
    <value>No Suppliers added</value>
  </data>
  <data name="View_Submission_Request" xml:space="preserve">
    <value>View submission request</value>
  </data>
    <data name="Configure_Authenticator_App" xml:space="preserve">
    <value>Configure Authenticator App</value>
  </data>
    <data name="Confirm" xml:space="preserve">
    <value>Confirm</value>
  </data>
    <data name="Confirm_Email" xml:space="preserve">
    <value>Confirm Email</value>
  </data>
    <data name="Confirm_Email_Change" xml:space="preserve">
    <value>Confirm Email Change</value>
  </data>
    <data name="Confirm_Password" xml:space="preserve">
    <value>Confirm Password</value>
  </data>
    <data name="ContentType" xml:space="preserve">
    <value>Content Type</value>
  </data>
    <data name="Control_Frameworks" xml:space="preserve">
    <value>Control Frameworks</value>
  </data>
    <data name="Control_Framework_Categories" xml:space="preserve">
    <value>Control Framework Categories</value>
  </data>
    <data name="Control_Framework_Category_Clause_name_is_required" xml:space="preserve">
    <value>Control Framework Category Clause name is required</value>
  </data>
    <data name="Control_Framework_Category_name_is_required" xml:space="preserve">
    <value>Control Framework Category name is required</value>
  </data>
    <data name="Control_Framework_Management" xml:space="preserve">
    <value>Control Framework Management</value>
  </data>
    <data name="Control_Framework_name_is_required" xml:space="preserve">
    <value>Control Framework name is required</value>
  </data>
    <data name="Copernicium" xml:space="preserve">
    <value>Copernicium</value>
  </data>
    <data name="Copper" xml:space="preserve">
    <value>Copper</value>
  </data>
    <data name="Countries" xml:space="preserve">
    <value>Countries</value>
  </data>
    <data name="Country Settings" xml:space="preserve">
    <value>Country Settings</value>
  </data>
    <data name="Country_exposure_level_is_required" xml:space="preserve">
    <value>Country exposure level is required</value>
  </data>
    <data name="Country_name_is_required" xml:space="preserve">
    <value>Country name is required</value>
  </data>
    <data name="Create" xml:space="preserve">
    <value>Create</value>
  </data>
    <data name="Created_On" xml:space="preserve">
      <value>Created On</value>
    </data>
  <data name="For_Organisation" xml:space="preserve">
  <value>For Organisation</value>
</data>
    <data name="Culture_Based_Resources" xml:space="preserve">
    <value>Culture-Based Resources</value>
  </data>
    <data name="Culture_Code" xml:space="preserve">
    <value>Culture Code</value>
  </data>
    <data name="Culture_Currency_Symbol" xml:space="preserve">
    <value>Culture Currency Symbol</value>
  </data>
    <data name="Culture_DateTime_Pattern" xml:space="preserve">
    <value>Culture DateTime Pattern</value>
  </data>
    <data name="Culture_Info" xml:space="preserve">
    <value>Culture Info</value>
  </data>
    <data name="Curium" xml:space="preserve">
    <value>Curium</value>
  </data>
    <data name="Current_Culture_DateTime_Format" xml:space="preserve">
    <value>Current Culture DateTime Format</value>
  </data>
    <data name="Current_Culture_Info" xml:space="preserve">
    <value>Current Culture Info</value>
  </data>
    <data name="Current_Culture_Time_Zone_Info" xml:space="preserve">
    <value>Current Culture Time Zone Info</value>
  </data>
    <data name="Current_Documents" xml:space="preserve">
    <value>Current Documents</value>
  </data>
    <data name="Current_password" xml:space="preserve">
    <value>Current Password</value>
  </data>
    <data name="Current_Transcripts" xml:space="preserve">
    <value>Current Transcripts</value>
  </data>
    <data name="Danger" xml:space="preserve">
    <value>Danger</value>
  </data>
    <data name="Darmstadtium" xml:space="preserve">
    <value>Darmstadtium</value>
  </data>
    <data name="Dashboard" xml:space="preserve">
    <value>Dashboard</value>
  </data>
    <data name="Data_Export_SignalR_Hangfire" xml:space="preserve">
    <value>Data Export (SignalR &amp; Hangfire)</value>
  </data>
    <data name="DateTime_Format" xml:space="preserve">
    <value>DateTime Format</value>
  </data>
    <data name="Date_of_Birth" xml:space="preserve">
    <value>Date of Birth</value>
  </data>
    <data name="Date_Separator" xml:space="preserve">
    <value>Date Separator</value>
  </data>
    <data name="Day" xml:space="preserve">
    <value>Day</value>
  </data>
    <data name="Day_Names" xml:space="preserve">
    <value>Day Names</value>
  </data>
    <data name="Deactivated" xml:space="preserve">
    <value>Deactivated</value>
  </data>
    <data name="December" xml:space="preserve">
    <value>December</value>
  </data>
    <data name="Default_Admin_Credentials" xml:space="preserve">
    <value>Default Admin Credentials</value>
  </data>
    <data name="Default_lockout_time_span" xml:space="preserve">
    <value>Default lockout time span</value>
  </data>
    <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
    <data name="Delete_my_Account" xml:space="preserve">
    <value>Delete My Account</value>
  </data>
    <data name="Delete_Personal_Data" xml:space="preserve">
    <value>Delete Personal Data</value>
  </data>
    <data name="Deleting_this_data_will_permanently_remove_your_account_and_this_cannot_be_recovered"
          xml:space="preserve">
    <value>Deleting this data will permanently remove your account, and this cannot be recovered.</value>
  </data>
    <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
    <data name="Description_for" xml:space="preserve">
    <value>Description for</value>
  </data>
    <data name="Disable_2FA" xml:space="preserve">
    <value>Disable Two-Factor Authentication</value>
  </data>
    <data name="Disable_Two-Factor_Authentication" xml:space="preserve">
    <value>Disable Two-Factor Authentication</value>
  </data>
    <data name="Disabling_2FA_does_not_change_the_keys_used_in_authenticator_apps" xml:space="preserve">
    <value>Disabling Two-Factor Authentication does not change the keys used in authenticator apps</value>
  </data>
    <data name="Dismissed" xml:space="preserve">
    <value>Dismissed</value>
  </data>
    <data name="Display_Name" xml:space="preserve">
    <value>Display Name</value>
  </data>
    <data name="Documents" xml:space="preserve">
    <value>Documents</value>
  </data>
    <data name="If_you_do_not_have_access_to_your_device_you_can" xml:space="preserve">
    <value>If you do not have access to your device, you can</value>
  </data>
    <data name="Dont_have_an_account" xml:space="preserve">
    <value>Don't have an account?</value>
  </data>
    <data name="Download" xml:space="preserve">
    <value>Download</value>
  </data>
    <data name="Downloads" xml:space="preserve">
    <value>Downloads</value>
  </data>
    <data name="Do_you_really_want_to_delete_this_record" xml:space="preserve">
    <value>Do you really want to delete this record?</value>
  </data>
    <data name="Do_you_want_to_remove_this_record" xml:space="preserve">
    <value>Do you want to remove this record?</value>
  </data>
    <data name="Dubnium" xml:space="preserve">
    <value>Dubnium</value>
  </data>
    <data name="Dysprosium" xml:space="preserve">
    <value>Dysprosium</value>
  </data>
    <data name="Earnings" xml:space="preserve">
    <value>Earnings</value>
  </data>
    <data name="Earning_Report" xml:space="preserve">
    <value>Earning Report</value>
  </data>
    <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
    <data name="Edit_Applicant" xml:space="preserve">
    <value>Edit Applicant</value>
  </data>
    <data name="Edit_Control_Framework" xml:space="preserve">
    <value>Edit Control Framework</value>
  </data>
    <data name="Edit_Reference" xml:space="preserve">
    <value>Edit Reference</value>
  </data>
    <data name="Edit_Role" xml:space="preserve">
    <value>Edit Role</value>
  </data>
    <data name="Edit_Tenant" xml:space="preserve">
    <value>Edit Tenant</value>
  </data>
    <data name="Edit_User" xml:space="preserve">
    <value>Edit User</value>
  </data>
    <data name="Einsteinium" xml:space="preserve">
    <value>Einsteinium</value>
  </data>
    <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
    <data name="Email_Change_Confirmation" xml:space="preserve">
    <value>Email Change Confirmation</value>
  </data>
    <data name="Email_Change_Confirmed" xml:space="preserve">
    <value>Email Change Confirmed</value>
  </data>
    <data name="Email_Confirmation" xml:space="preserve">
    <value>Email Confirmation</value>
  </data>
    <data name="Email_Confirmed" xml:space="preserve">
    <value>Email Confirmed</value>
  </data>
    <data name="Employee_broke_a_finger_while_writing_some_code" xml:space="preserve">
    <value>Employee broke a finger while writing some code</value>
  </data>
    <data name="enable_QR_code_generation" xml:space="preserve">
    <value>enable QR code generation</value>
  </data>
    <data name="End" xml:space="preserve">
    <value>End</value>
  </data>
    <data name="End_Slope" xml:space="preserve">
    <value>End Slope</value>
  </data>
    <data name="English_Name" xml:space="preserve">
    <value>English Name</value>
  </data>
    <data name="Enter_confirmation_code_below" xml:space="preserve">
    <value>Enter confirmation code below</value>
  </data>
    <data name="Enter_the_code_in_the_confirmation_box_below" xml:space="preserve">
    <value>Enter the code in the confirmation box below.</value>
  </data>
    <data name="Enter_your_credentials_below" xml:space="preserve">
    <value>Enter your credentials below</value>
  </data>
    <data name="Enter_your_email" xml:space="preserve">
    <value>Enter your email.</value>
  </data>
    <data name="entries" xml:space="preserve">
    <value>entries</value>
  </data>
    <data name="Erbium" xml:space="preserve">
    <value>Erbium</value>
  </data>
    <data name="Error_Pages" xml:space="preserve">
    <value>Error Pages</value>
  </data>
    <data name="Europium" xml:space="preserve">
    <value>Europium</value>
  </data>
    <data name="Export" xml:space="preserve">
    <value>Export</value>
  </data>
    <data name="ExportAsPdfImmediate" xml:space="preserve">
    <value>Export as PDF (Immediately)</value>
  </data>
    <data name="ExportAsPdfInBackground" xml:space="preserve">
    <value>Export as PDF (Fire and forgot)</value>
  </data>
    <data name="Exporting_data_may_take_a_while" xml:space="preserve">
    <value>Exporting data may take a while.</value>
  </data>
    <data name="Exposure_Level" xml:space="preserve">
    <value>Exposure Level</value>
  </data>
    <data name="Failed" xml:space="preserve">
    <value>Failed</value>
  </data>
    <data name="FAQ" xml:space="preserve">
    <value>FAQ</value>
  </data>
    <data name="Features" xml:space="preserve">
    <value>Features</value>
  </data>
    <data name="February" xml:space="preserve">
    <value>February</value>
  </data>
    <data name="Fermium" xml:space="preserve">
    <value>Fermium</value>
  </data>
    <data name="FileName" xml:space="preserve">
    <value>File Name</value>
  </data>
    <data name="File_is_empty" xml:space="preserve">
    <value>File is empty.</value>
  </data>
    <data name="File_Name" xml:space="preserve">
    <value>File name</value>
  </data>
    <data name="File_Rename" xml:space="preserve">
    <value>File Rename</value>
  </data>
    <data name="File_Size" xml:space="preserve">
    <value>File Size</value>
  </data>
    <data name="File_Type" xml:space="preserve">
    <value>File Type</value>
  </data>
    <data name="File_upload_has_been_cancelled" xml:space="preserve">
    <value>File upload has been cancelled.</value>
  </data>
    <data name="Finish" xml:space="preserve">
    <value>Finish</value>
  </data>
    <data name="Fire_in_reactor_core" xml:space="preserve">
    <value>Fire in reactor core</value>
  </data>
    <data name="First" xml:space="preserve">
    <value>First</value>
  </data>
    <data name="First_Day_of_Week" xml:space="preserve">
    <value>First Day of Week</value>
  </data>
    <data name="First_Name" xml:space="preserve">
    <value>First Name</value>
  </data>
    <data name="Fluent_Validation" xml:space="preserve">
    <value>Fluent Validation</value>
  </data>
    <data name="Fluorine" xml:space="preserve">
    <value>Fluorine</value>
  </data>
    <data name="Forget_this_browser" xml:space="preserve">
    <value>Forget this browser</value>
  </data>
    <data name="Forgot_password_confirmation" xml:space="preserve">
    <value>Forgot password confirmation</value>
  </data>
    <data name="Forgot_your_password" xml:space="preserve">
    <value>Forgot your password?</value>
  </data>
    <data name="for_how_to_configure_a_real_email_sender" xml:space="preserve">
    <value>for how to configure a real email sender</value>
  </data>
    <data name="Fossil" xml:space="preserve">
    <value>Fossil</value>
  </data>
    <data name="Francium" xml:space="preserve">
    <value>Francium</value>
  </data>
    <data name="FullName" xml:space="preserve">
    <value>Full Name</value>
  </data>
    <data name="Full_DateTime_Pattern" xml:space="preserve">
    <value>Full DateTime Pattern</value>
  </data>
    <data name="Gas" xml:space="preserve">
    <value>Gas</value>
  </data>
    <data name="Gas1" xml:space="preserve">
    <value>Gas</value>
  </data>
    <data name="Generate" xml:space="preserve">
    <value>Generate</value>
  </data>
    <data name="generate_a_new_set_of_recovery_codes" xml:space="preserve">
    <value>generate a new set of recovery codes</value>
  </data>
    <data name="Generating_new_recovery_codes_does_not_change_the_keys_used_in_authenticator_apps" xml:space="preserve">
    <value>Generating new recovery codes does not change the keys used in authenticator apps.</value>
  </data>
    <data name="Geothermal" xml:space="preserve">
    <value>Geothermal</value>
  </data>
    <data name="Germany" xml:space="preserve">
    <value>Germany</value>
  </data>
    <data name="Getting_Started" xml:space="preserve">
    <value>Getting Started</value>
  </data>
    <data name="Global_Settings" xml:space="preserve">
    <value>Global Settings</value>
  </data>
    <data name="Global_Spread" xml:space="preserve">
    <value>Global Spread</value>
  </data>
    <data name="Global_Variable" xml:space="preserve">
    <value>Global Variable</value>
  </data>
    <data name="Got_it" xml:space="preserve">
    <value>Got it!</value>
  </data>
    <data name="Graphite_on_roof" xml:space="preserve">
    <value>Graphite on roof</value>
  </data>
    <data name="Height" xml:space="preserve">
    <value>Height</value>
  </data>
    <data name="Hello" xml:space="preserve">
    <value>Hello</value>
  </data>
    <data name="here" xml:space="preserve">
    <value>here</value>
  </data>
    <data name="Hi" xml:space="preserve">
    <value/>
  </data>
    <data name="High" xml:space="preserve">
    <value>High</value>
  </data>
    <data name="Home" xml:space="preserve">
    <value>Home</value>
  </data>
    <data name="Hour" xml:space="preserve">
    <value>Hour</value>
  </data>
    <data name="Hydro" xml:space="preserve">
    <value>Hydro</value>
  </data>
    <data name="Identity" xml:space="preserve">
    <value>Identity</value>
  </data>
    <data name="Identity_Settings" xml:space="preserve">
    <value>Identity Settings</value>
  </data>
    <data name="If_you_do_not_complete_your_authenticator_app_configuration_you_may_lose_access_to_your_account"
          xml:space="preserve">
    <value>If you do not complete your authenticator app configuration you may lose access to your account.</value>
  </data>
    <data name="If_you_lose_your_device_and_dont_have_the_recovery_codes_you_will_lose_access_to_your_account"
          xml:space="preserve">
    <value>If you lose your device and don't have the recovery codes you will lose access to your account</value>
  </data>
    <data name="If_you_reset_your_authenticator_key_your_authenticator_app_will_not_work_until_you_reconfigure_it"
          xml:space="preserve">
    <value>If you reset your authenticator key your authenticator app will not work until you reconfigure it.</value>
  </data>
    <data name="If_you_wish_to_change_the_key_used_in_an_authenticator_app_you_should" xml:space="preserve">
    <value>If you wish to change the key used in an authenticator app you should</value>
  </data>
    <data name="Im_sorry_I_can't_display_anything_until_you" xml:space="preserve">
    <value>I'm sorry, I can't display anything until you</value>
  </data>
    <data name="Incident_in_plant_number_4" xml:space="preserve">
    <value>Incident in plant number 4</value>
  </data>
    <data name="Industries" xml:space="preserve">
    <value>Industries</value>
  </data>
    <data name="Industry_exposure_level_is_required" xml:space="preserve">
    <value>Industry exposure level is required</value>
  </data>
    <data name="Industry_name_is_required" xml:space="preserve">
    <value>Industry name is required</value>
  </data>
    <data name="Industry_Settings" xml:space="preserve">
    <value>Industry Settings</value>
  </data>
    <data name="Installation" xml:space="preserve">
    <value>Installation</value>
  </data>
    <data name="Interpolation_Algorithm" xml:space="preserve">
    <value>Interpolation Algorithm</value>
  </data>
    <data name="into_your_two_factor_authenticator_app._Spaces_and_casing_do_not_matter" xml:space="preserve">
    <value>into your two factor authenticator app. Spaces and casing do not matter.</value>
  </data>
    <data name="Invalid_file_name" xml:space="preserve">
    <value>Invalid file name.</value>
  </data>
    <data name="In_Progress" xml:space="preserve">
    <value>Processing</value>
  </data>
    <data name="Ispum" xml:space="preserve">
    <value>Sed vitae hendrerit justo. Aenean eu mi in urna eleifend imperdiet a vel ipsum. Morbi ac risus vel massa tincidunt lacinia. Aliquam augue libero, venenatis sit amet neque non, dignissim interdum elit. Nunc condimentum enim non ante egestas lobortis. Sed ac felis gravida nisi blandit porta. Sed ultrices finibus ullamcorper. Nullam congue malesuada rhoncus. In non varius arcu. Curabitur porta erat id neque fermentum, ut finibus dui venenatis. Fusce tristique cursus pulvinar. Nam consectetur convallis orci, a elementum elit ultrices ac. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin leo nulla, facilisis sit amet placerat sed, blandit non sapien.</value>
  </data>
    <data name="Issues_rising" xml:space="preserve">
    <value>Issues rising</value>
  </data>
    <data name="Is_Active_User" xml:space="preserve">
    <value>Is Active User</value>
  </data>
    <data name="Is_Default" xml:space="preserve">
    <value>Is Default</value>
  </data>
    <data name="Is_Email_Confirmed" xml:space="preserve">
    <value>Email Confirmed</value>
  </data>
    <data name="Is_Static" xml:space="preserve">
    <value>Is Static</value>
  </data>
    <data name="Items_per_page" xml:space="preserve">
    <value>Items per page</value>
  </data>
    <data name="January" xml:space="preserve">
    <value>January</value>
  </data>
    <data name="Job_Title" xml:space="preserve">
    <value>Job Title</value>
  </data>
    <data name="Join_the_Military" xml:space="preserve">
    <value>Join the Military</value>
  </data>
    <data name="July" xml:space="preserve">
    <value>July</value>
  </data>
    <data name="June" xml:space="preserve">
    <value>June</value>
  </data>
    <data name="Last" xml:space="preserve">
    <value>Last</value>
  </data>
    <data name="Last_Name" xml:space="preserve">
    <value>Last Name</value>
  </data>
    <data name="Learn_how_to" xml:space="preserve">
    <value>Learn how to</value>
  </data>
    <data name="Left" xml:space="preserve">
    <value>Left</value>
  </data>
    <data name="Legend_Position" xml:space="preserve">
    <value>Legend Position</value>
  </data>
    <data name="Live_data_powered_by_SignalR" xml:space="preserve">
    <value>Live data powered by SignalR</value>
  </data>
    <data name="Loading_On_Demand" xml:space="preserve">
    <value>Loading on Demand</value>
  </data>
    <data name="Localization" xml:space="preserve">
    <value>Localization</value>
  </data>
    <data name="Local_Time" xml:space="preserve">
    <value>Local Time</value>
  </data>
    <data name="Locations" xml:space="preserve">
    <value>Locations</value>
  </data>
    <data name="Lockout_Settings" xml:space="preserve">
    <value>Lockout Settings</value>
  </data>
    <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
    <data name="login_lowered" xml:space="preserve">
    <value>login</value>
  </data>
    <data name="Login_out" xml:space="preserve">
    <value>Login out...</value>
  </data>
    <data name="Login_to_your_account" xml:space="preserve">
    <value>Login to your account</value>
  </data>
    <data name="Login_With_Authenticator_Code" xml:space="preserve">
    <value>Login with Authenticator Code</value>
  </data>
    <data name="Login_With_Recovery_Code" xml:space="preserve">
    <value>Login with Recovery Code</value>
  </data>
    <data name="Logout" xml:space="preserve">
    <value>Logout</value>
  </data>
    <data name="Log_in" xml:space="preserve">
    <value>Log in</value>
  </data>
    <data name="log_in_with_a_recovery_code" xml:space="preserve">
    <value>log in with a recovery code</value>
  </data>
    <data name="Long_Date_Pattern" xml:space="preserve">
    <value>Long Date Pattern</value>
  </data>
    <data name="Long_Time_Pattern" xml:space="preserve">
    <value>Long Time Pattern</value>
  </data>
    <data name="Lorem" xml:space="preserve">
    <value>Mauris cursus dui in tellus gravida, ut iaculis ante mattis. Aenean blandit faucibus finibus. Aenean euismod lorem metus. Ut vitae nulla ac mauris vulputate accumsan ac non velit. Cras at tincidunt augue, quis eleifend est. Quisque magna diam, elementum ut pellentesque vitae, porttitor quis quam. Praesent vulputate varius ex et suscipit. Praesent imperdiet facilisis nisi non sodales. Nullam sit amet lorem quis nisi pellentesque ornare. Pellentesque maximus sed urna vel euismod. In egestas odio id est gravida lobortis. In a varius mauris, quis ornare metus. Sed semper pretium magna, a vulputate lorem vulputate vitae. Quisque eu congue quam, sit amet euismod ipsum.</value>
  </data>
    <data name="Loss_Types" xml:space="preserve">
    <value>Loss Types</value>
  </data>
    <data name="Loss_type_is_required" xml:space="preserve">
    <value>Loss type is required</value>
  </data>
    <data name="Low" xml:space="preserve">
    <value>Low</value>
  </data>
    <data name="March" xml:space="preserve">
    <value>March</value>
  </data>
    <data name="Master_Details_Form" xml:space="preserve">
    <value>Master-Details Form</value>
  </data>
    <data name="Maximum_Failed_Access_Attempts" xml:space="preserve">
    <value>Maximum Failed Access Attempts</value>
  </data>
    <data name="May" xml:space="preserve">
    <value>May</value>
  </data>
    <data name="Medium" xml:space="preserve">
    <value>Medium</value>
  </data>
    <data name="Military_Applicants" xml:space="preserve">
    <value>Military Applicants</value>
  </data>
    <data name="Minor" xml:space="preserve">
    <value>Minor</value>
  </data>
    <data name="Minute" xml:space="preserve">
    <value>Minutes</value>
  </data>
    <data name="Modified_On" xml:space="preserve">
    <value>Modification Date</value>
  </data>
    <data name="Month" xml:space="preserve">
    <value>Month</value>
  </data>
    <data name="Month_Day_Pattern" xml:space="preserve">
    <value>Month Day Pattern</value>
  </data>
    <data name="Month_Genitive_Names" xml:space="preserve">
    <value>Month Genitive Names</value>
  </data>
    <data name="Month_Names" xml:space="preserve">
    <value>Month Names</value>
  </data>
    <data name="My_Account" xml:space="preserve">
    <value>My Account</value>
  </data>
    <data name="My_Claims" xml:space="preserve">
    <value>My Claims</value>
  </data>
    <data name="My_Permissions" xml:space="preserve">
    <value>My Permissions</value>
  </data>
    <data name="My_Profile" xml:space="preserve">
    <value>My Profile</value>
  </data>
  <data name="Policies" xml:space="preserve">
    <value>Policies</value>
  </data>
  <data name="Forms" xml:space="preserve">
    <value>Forms</value>
  </data>
  <data name="Intents" xml:space="preserve">
    <value>Intents</value>
  </data>
    <data name="My_Profile_Picture" xml:space="preserve">
    <value>My Profile Picture</value>
  </data>
    <data name="OrganisationName" xml:space="preserve">
    <value>Organisation Name</value>
  </data>
    <data name="Native_Calendar_Name" xml:space="preserve">
    <value>Native Calendar Name</value>
  </data>
    <data name="Native_Name" xml:space="preserve">
    <value>Native Name</value>
  </data>
    <data name="Natural_Spline" xml:space="preserve">
    <value>Natural Spline</value>
  </data>
    <data name="New_Email" xml:space="preserve">
    <value>New Email</value>
  </data>
    <data name="New_Registered_Users_are_Active_by_Default" xml:space="preserve">
    <value>New Registered Users are Active by Default</value>
  </data>
    <data name="Next" xml:space="preserve">
    <value>Next</value>
  </data>
    <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
    <data name="Normally_this_would_be_emailed" xml:space="preserve">
    <value>Normally this would be emailed</value>
  </data>
    <data name="Not_Active" xml:space="preserve">
    <value>Not Active</value>
  </data>
    <data name="November" xml:space="preserve">
    <value>November</value>
  </data>
    <data name="No_data_available" xml:space="preserve">
    <value>No data available.</value>
  </data>
    <data name="No_keep_it" xml:space="preserve">
    <value>No, keep it</value>
  </data>
    <data name="Nuclear" xml:space="preserve">
    <value>Nuclear</value>
  </data>
    <data name="Occupational_injury" xml:space="preserve">
    <value>Occupational injury</value>
  </data>
    <data name="October" xml:space="preserve">
    <value>October</value>
  </data>
    <data name="Oil" xml:space="preserve">
    <value>Oil</value>
  </data>
    <data name="Oil1" xml:space="preserve">
    <value>Oil</value>
  </data>
    <data name="Once_you_have_scanned_the_QR_code_or_input_the_key_above" xml:space="preserve">
    <value>Once you have scanned the QR code or input the key above</value>
  </data>
    <data name="On_Premise_File_Storage" xml:space="preserve">
    <value>On-Premise File Storage</value>
  </data>
    <data name="Options" xml:space="preserve">
    <value>Options</value>
  </data>
    <data name="Option" xml:space="preserve">
    <value>Option</value>
  </data>
    <data name="or" xml:space="preserve">
    <value>or</value>
  </data>
    <data name="out_of" xml:space="preserve">
    <value>out of</value>
  </data>
    <data name="Overview" xml:space="preserve">
    <value>Overview</value>
  </data>
    <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
    <data name="Password_Settings" xml:space="preserve">
    <value>Password Settings</value>
  </data>
    <data name="Pending" xml:space="preserve">
    <value>Pending</value>
  </data>
    <data name="Periodic" xml:space="preserve">
    <value>Periodic</value>
  </data>
    <data name="Permissions" xml:space="preserve">
    <value>Permissions</value>
  </data>
    <data name="Personal_Data" xml:space="preserve">
    <value>Personal Data</value>
  </data>
    <data name="Phone_Number" xml:space="preserve">
    <value>Phone Number</value>
  </data>
    <data name="Phone_number_is_required" xml:space="preserve">
    <value>Phone number is required.</value>
  </data>
    <data name="Play" xml:space="preserve">
    <value>Play</value>
  </data>
    <data name="Please" xml:space="preserve">
    <value>Please</value>
  </data>
  <data name="Please_select_an_application_form" xml:space="preserve">
    <value>Please select an application form</value>
  </data>
    <data name="Please_check_your_email_to_confirm_your_account" xml:space="preserve">
    <value>Please check your email to confirm your account.</value>
  </data>
    <data name="Please_check_your_email_to_reset_your_password" xml:space="preserve">
      <value>Please check your email to reset your password</value>
    </data>
    <data name="Please_provide_new_name_for_the_file_or_leave_it_as_is" xml:space="preserve">
    <value>Please provide new name for the file or leave it as is.</value>
  </data>
    <data name="PM_Designator" xml:space="preserve">
    <value>PM Designator</value>
  </data>
    <data name="Previous" xml:space="preserve">
    <value>Previous</value>
  </data>
    <data name="Profile" xml:space="preserve">
    <value>Profile</value>
  </data>
    <data name="Profile_Picture" xml:space="preserve">
    <value>Profile Picture</value>
  </data>
    <data name="Proof_of_Concepts" xml:space="preserve">
    <value>Proof of Concepts</value>
  </data>
    <data name="Properties" xml:space="preserve">
    <value>Properties</value>
  </data>
    <data name="Property" xml:space="preserve">
    <value>Property</value>
  </data>
    <data name="QueryString" xml:space="preserve">
    <value>Search Criteria</value>
  </data>
    <data name="ApplicationForms" xml:space="preserve">
      <value>Application Forms</value>
    </data>
    <data name="Form_Builder" xml:space="preserve">
      <value>Form Builder</value>
    </data>
    <data name="Form_name_is_required" xml:space="preserve">
      <value>Form name is required</value>
    </data>
    <data name="Form_page_is_required" xml:space="preserve">
    <value>Question page is required</value>
  </data>
    <data name="Form_Page_title_is_required" xml:space="preserve">
      <value>Form page title is required</value>
    </data>
    <data name="Form_title_is_required" xml:space="preserve">
      <value>Form title is required</value>
    </data>
    <data name="Question_is_required" xml:space="preserve">
    <value>Question is required</value>
  </data>
    <data name="Question_title_is_required" xml:space="preserve">
    <value>Question title is required</value>
  </data>
    <data name="Question_type_is_required" xml:space="preserve">
    <value>Question type is required</value>
  </data>
    <data name="Quick_Search" xml:space="preserve">
    <value>Quick Search</value>
  </data>
    <data name="Randomize" xml:space="preserve">
    <value>Randomize</value>
  </data>
    <data name="Ready" xml:space="preserve">
    <value>Ready</value>
  </data>
    <data name="Realtime_Data" xml:space="preserve">
    <value>Realtime Data</value>
  </data>
    <data name="Recent_incidents" xml:space="preserve">
    <value>Recent incidents</value>
  </data>
    <data name="Recovery_Code" xml:space="preserve">
    <value>Recovery Code</value>
  </data>
    <data name="Recovery_Codes" xml:space="preserve">
    <value>Recovery Codes</value>
  </data>
    <data name="Reference" xml:space="preserve">
    <value>Reference</value>
  </data>
    <data name="References" xml:space="preserve">
    <value>References</value>
  </data>
    <data name="Reference_name_is_required" xml:space="preserve">
    <value>Reference name is required.</value>
  </data>
    <data name="Refresh_Token_TimeSpan" xml:space="preserve">
    <value>Refresh Token Timespan</value>
  </data>
    <data name="Region" xml:space="preserve">
    <value>Region</value>
  </data>
    <data name="Regions" xml:space="preserve">
    <value>Regions</value>
  </data>
    <data name="Region_exposure_level_is_required" xml:space="preserve">
    <value>Region exposure level is required</value>
  </data>
    <data name="Region_name_is_required" xml:space="preserve">
    <value>Region name is required</value>
  </data>
    <data name="Region_Settings" xml:space="preserve">
    <value>Region Settings</value>
  </data>
    <data name="Register" xml:space="preserve">
    <value>Register</value>
  </data>
    <data name="Register_as_a_new_user" xml:space="preserve">
    <value>Register as a new user</value>
  </data>
    <data name="Register_Confirmation" xml:space="preserve">
    <value>Register Confirmation</value>
  </data>
    <data name="Remember_this_machine" xml:space="preserve">
    <value>Remember this machine</value>
  </data>
    <data name="Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
    <data name="Reporting" xml:space="preserve">
    <value>Reporting</value>
  </data>
    <data name="Reports" xml:space="preserve">
    <value>Reports</value>
  </data>
    <data name="Report_Details" xml:space="preserve">
    <value>Report Details</value>
  </data>
    <data name="Required_Length" xml:space="preserve">
    <value>Required Length</value>
  </data>
    <data name="Required_Unique_Characters" xml:space="preserve">
    <value>Required Unique Characters</value>
  </data>
    <data name="Require_Confirmed_Account" xml:space="preserve">
    <value>Require Confirmed Account</value>
  </data>
    <data name="Require_Confirmed_Email" xml:space="preserve">
    <value>Require Confirmed Email</value>
  </data>
    <data name="Require_Confirmed_Phone_Number" xml:space="preserve">
    <value>Require Confirmed Phone Number</value>
  </data>
    <data name="Require_Digits" xml:space="preserve">
    <value>Require Digits</value>
  </data>
    <data name="Require_Lowercase_Characters" xml:space="preserve">
    <value>Require Lowercase Characters</value>
  </data>
    <data name="Require_Non_Alphanumeric" xml:space="preserve">
    <value>Require Non-Alphanumeric</value>
  </data>
    <data name="Require_Uppercase_Characters" xml:space="preserve">
    <value>Require Uppercase Characters</value>
  </data>
    <data name="Resend" xml:space="preserve">
    <value>Resend</value>
  </data>
    <data name="Resend_Email_Confirmation" xml:space="preserve">
    <value>Resend Email Confirmation</value>
  </data>
    <data name="Reset_2FA_App" xml:space="preserve">
    <value>Reset Two-Factor Authentication App</value>
  </data>
    <data name="Reset_2FA_Recovery_Codes" xml:space="preserve">
    <value>Reset Two-Factor Authentication Recovery Codes</value>
  </data>
    <data name="Reset_Authenticator_App" xml:space="preserve">
    <value>Reset Authenticator App</value>
  </data>
    <data name="Reset_Authenticator_Key" xml:space="preserve">
    <value>Reset Authenticator Key</value>
  </data>
    <data name="Reset_Password" xml:space="preserve">
    <value>Reset Password</value>
  </data>
    <data name="Reset_Password_Confirmation" xml:space="preserve">
    <value>Reset Password Confirmation</value>
  </data>
    <data name="Reset_Recovery_Codes" xml:space="preserve">
    <value>Reset Recovery Codes</value>
  </data>
    <data name="reset_your_authenticator_keys" xml:space="preserve">
    <value>reset your authenticator keys</value>
  </data>
    <data name="Reset_your_password" xml:space="preserve">
    <value>Reset your password.</value>
  </data>
    <data name="Resource.Issues_is_almost_reaching_100" xml:space="preserve">
    <value>Resource.Issues is almost reaching 100</value>
  </data>
    <data name="Resources" xml:space="preserve">
    <value>Resources</value>
  </data>
    <data name="Return" xml:space="preserve">
    <value>Return</value>
  </data>
    <data name="RFC1123_Pattern" xml:space="preserve">
    <value>RFC1123 Pattern</value>
  </data>
    <data name="Right" xml:space="preserve">
    <value>Right</value>
  </data>
    <data name="Roentgen" xml:space="preserve">
    <value>Roentgen</value>
  </data>
  <data name="Role" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="Role_Name" xml:space="preserve">
    <value>Role Name</value>
  </data>
    <data name="Rows_Per_Page" xml:space="preserve">
    <value>Rows per page:</value>
  </data>
    <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
    <data name="Save_ApplicationForm" xml:space="preserve">
      <value>Save Application Form</value>
    </data>
    <data name="Search" xml:space="preserve">
    <value>Search</value>
  </data>
    <data name="Search_by_Roles" xml:space="preserve">
    <value>Search by Roles</value>
  </data>
    <data name="Security_Control_Framework" xml:space="preserve">
    <value>Security Control Framework</value>
  </data>
    <data name="see" xml:space="preserve">
    <value>see</value>
  </data>
    <data name="Select" xml:space="preserve">
    <value>Select</value>
  </data>
    <data name="select_for_dropdown" xml:space="preserve">
    <value>-Select-</value>
  </data>
    <data name="Select_Roles" xml:space="preserve">
    <value>Select Roles</value>
  </data>
    <data name="Send_Activation_Email" xml:space="preserve">
    <value>Send Activation Email</value>
  </data>
    <data name="Septemper" xml:space="preserve">
    <value>Septemper</value>
  </data>
    <data name="Series_1" xml:space="preserve">
    <value>Series 1</value>
  </data>
    <data name="Series_2" xml:space="preserve">
    <value>Series 2</value>
  </data>
    <data name="Server_Side_Authorization" xml:space="preserve">
    <value>Server-Side Authorization</value>
  </data>
    <data name="Server_Side_Validation" xml:space="preserve">
    <value>Server Side Validation</value>
  </data>
    <data name="Settings" xml:space="preserve">
    <value>Settings</value>
  </data>
    <data name="Setup_Authenticator_App" xml:space="preserve">
    <value>Setup Authenticator App</value>
  </data>
    <data name="Set_Random_Password" xml:space="preserve">
    <value>Set Random Password</value>
  </data>
    <data name="Shortest_Day_Names" xml:space="preserve">
    <value>Shortest Day Names</value>
  </data>
    <data name="Shortest_Day_Names1" xml:space="preserve">
    <value>Shortest Day Names</value>
  </data>
    <data name="Short_Date_Pattern" xml:space="preserve">
    <value>Short Date Pattern</value>
  </data>
    <data name="Short_Time_Pattern" xml:space="preserve">
    <value>Short Time Pattern</value>
  </data>
    <data name="Showing" xml:space="preserve">
    <value>Showing</value>
  </data>
    <data name="Side_Authorization" xml:space="preserve">
    <value>Side Authorization</value>
  </data>
    <data name="Sign_in" xml:space="preserve">
    <value>Sign in</value>
  </data>
    <data name="Sign_in_Settings" xml:space="preserve">
    <value>Sign in Settings</value>
  </data>
    <data name="Sign_up" xml:space="preserve">
    <value>Sign up</value>
  </data>
  <data name="Create_New_Account_for" xml:space="preserve">
    <value>Create New Account for</value>
  </data>
  <data name="Account_Creation_Successful" xml:space="preserve">
    <value>Registration Successful</value>
  </data>
  <data name="Account_Creation_Successful_Details" xml:space="preserve">
    <value>Your account was successfully created, look out for an email asking you to change your password, before logging in</value>
  </data>
    <data name="Solar" xml:space="preserve">
    <value>Solar</value>
  </data>
    <data name="Something_went_wrong" xml:space="preserve">
    <value>Something went wrong!</value>
  </data>
    <data name="Sorry_there's_nothing_at_this_address" xml:space="preserve">
    <value>Sorry, there's nothing at this address.</value>
  </data>
    <data name="Sortable_DateTime_Pattern" xml:space="preserve">
    <value>Sortable DateTime Pattern</value>
  </data>
    <data name="SSN" xml:space="preserve">
    <value>Social Security Number</value>
  </data>
    <data name="SSNShort" xml:space="preserve">
    <value>SSN</value>
  </data>
    <data name="Start" xml:space="preserve">
    <value>Start</value>
  </data>
    <data name="Start_SignalR_Connection" xml:space="preserve">
    <value>Start SignalR Connection</value>
  </data>
    <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
    <data name="Stop" xml:space="preserve">
    <value>Stop</value>
  </data>
    <data name="Storage_type_is" xml:space="preserve">
    <value>Storage type is</value>
  </data>
    <data name="String" xml:space="preserve">
    <value/>
  </data>
    <data name="Submit" xml:space="preserve">
    <value>Submit</value>
  </data>
    <data name="Succeeded" xml:space="preserve">
    <value>Succeeded</value>
  </data>
    <data name="Support" xml:space="preserve">
    <value>Support</value>
  </data>
    <data name="Surname" xml:space="preserve">
    <value>Surname</value>
  </data>
    <data name="Survey_Created" xml:space="preserve">
    <value>Survey Created</value>
  </data>
    <data name="Suspend" xml:space="preserve">
    <value>Suspend</value>
  </data>
    <data name="Sweden" xml:space="preserve">
    <value>Sweden</value>
  </data>
    <data name="Tenants" xml:space="preserve">
    <value>Tenants</value>
  </data>
    <data name="Thank_you_for_confirming_your_email" xml:space="preserve">
    <value>Thank you for confirming your email.</value>
  </data>
    <data name="these_docs" xml:space="preserve">
    <value>these docs</value>
  </data>
    <data name="The_page_you_were_looking_for_doesnt_exist" xml:space="preserve">
    <value>The page you were looking for doesn't exist.</value>
  </data>
    <data name="The_selected_file_exceeds_the_allowed_maximum_size_limit" xml:space="preserve">
    <value>The selected file exceeds the allowed maximum size limit.</value>
  </data>
    <data name="The_selected_file_extension_is_not_allowed" xml:space="preserve">
    <value>The selected file extension is not allowed.</value>
  </data>
    <data name="This_action_only_disables_2FA" xml:space="preserve">
    <value>This action only disables Two-Factor Authentication.</value>
  </data>
    <data name="This_app_does_not_currently_have_a_real_email_sender_registered" xml:space="preserve">
    <value>This app does not currently have a real email sender registered</value>
  </data>
    <data name="This_page_allows_you_to_download_or_delete_that_data" xml:space="preserve">
    <value>This page allows you to download or delete that data.</value>
  </data>
    <data name="This_process_disables_2FA_until_you_verify_your_authenticator_app" xml:space="preserve">
    <value>This process disables Two-Factor Authentication until you verify your authenticator app.</value>
  </data>
    <data name="Time_Separator" xml:space="preserve">
    <value>Time Separator</value>
  </data>
    <data name="Time_Zone" xml:space="preserve">
    <value>Time Zone</value>
  </data>
    <data name="Time_Zone_Info" xml:space="preserve">
    <value>Time Zone Info</value>
  </data>
    <data name="Time_Zone_OffSet" xml:space="preserve">
    <value>Time Zone OffSet</value>
  </data>
    <data name="Tips" xml:space="preserve">
    <value>Tips</value>
  </data>
    <data name="Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="Completed_Organisation_Submissions" xml:space="preserve">
    <value>Completed Organisation Submissions</value>
  </data>
    <data name="Token_Info" xml:space="preserve">
    <value>Token Info</value>
  </data>
    <data name="Token_Settings" xml:space="preserve">
    <value>Token Settings</value>
  </data>
    <data name="Top" xml:space="preserve">
    <value>Top</value>
  </data>
    <data name="Total_Cost" xml:space="preserve">
    <value>Total Cost</value>
  </data>
    <data name="to_confirm_your_account" xml:space="preserve">
    <value>to confirm your account</value>
  </data>
    <data name="to_login" xml:space="preserve">
    <value>to login.</value>
  </data>
    <data name="Two_Factor_Authentication" xml:space="preserve">
    <value>Two-Factor Authentication</value>
  </data>
    <data name="UI_Culture_Code" xml:space="preserve">
    <value>UI Culture Code</value>
  </data>
    <data name="Unable_to_upload_an_empty_file" xml:space="preserve">
    <value>Unable to upload an empty file.</value>
  </data>
    <data name="Unauthorised" xml:space="preserve">
    <value>Unauthorised</value>
  </data>
    <data name="United_States" xml:space="preserve">
    <value>United States</value>
  </data>
    <data name="Units_of_Time" xml:space="preserve">
    <value>Units of Time</value>
  </data>
    <data name="Universal_Sortable_DateTime_Pattern" xml:space="preserve">
    <value>Universal Sortable DateTime Pattern</value>
  </data>
    <data name="Update" xml:space="preserve">
    <value>Update</value>
  </data>
    <data name="Update_Loss_Type" xml:space="preserve">
    <value>Update Loss Type</value>
  </data>
    <data name="Update_ApplicationForm" xml:space="preserve">
    <value>Update Application Form</value>
  </data>
    <data name="Upload_Avatar" xml:space="preserve">
    <value>Upload Avatar</value>
  </data>
    <data name="Upload_Documents" xml:space="preserve">
    <value>Upload Documents</value>
  </data>
    <data name="Username" xml:space="preserve">
    <value>Username</value>
  </data>
    <data name="Users" xml:space="preserve">
    <value>Users</value>
  </data>
    <data name="Users_Management" xml:space="preserve">
    <value>Users Management</value>
  </data>
    <data name="User_Account" xml:space="preserve">
    <value>User Account</value>
  </data>
    <data name="User_Documents" xml:space="preserve">
    <value>User Documents</value>
  </data>
    <data name="User_is_not_found" xml:space="preserve">
    <value>User is not found.</value>
  </data>
    <data name="User_Profile" xml:space="preserve">
    <value>User Profile</value>
  </data>
    <data name="User_Settings" xml:space="preserve">
    <value>User Settings</value>
  </data>
    <data name="Use_a_local_account_to_log_in" xml:space="preserve">
    <value>Use a local account to log in.</value>
  </data>
    <data name="Validation_Summary" xml:space="preserve">
    <value>Validation Summary</value>
  </data>
    <data name="Value" xml:space="preserve">
    <value>Value</value>
  </data>
    <data name="Verify" xml:space="preserve">
    <value>Verify</value>
  </data>
    <data name="Very_High" xml:space="preserve">
    <value>Very High</value>
  </data>
    <data name="Very_Low" xml:space="preserve">
    <value>Very Low</value>
  </data>
    <data name="View" xml:space="preserve">
    <value>View</value>
  </data>
    <data name="View_Applicant" xml:space="preserve">
    <value>View Applicant</value>
  </data>
    <data name="Visit_Website" xml:space="preserve">
    <value>Visit Website</value>
  </data>
    <data name="Weight" xml:space="preserve">
    <value>Weight</value>
  </data>
    <data name="Wind" xml:space="preserve">
    <value>Wind</value>
  </data>
    <data name="Year_Month_Pattern" xml:space="preserve">
    <value>Year Month Pattern</value>
  </data>
    <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
    <data name="Yes_delete_it" xml:space="preserve">
    <value>Yes, delete it</value>
  </data>
    <data name="Yes_remove_it" xml:space="preserve">
    <value>Yes, remove it</value>
  </data>
    <data name="Yes_rename_it" xml:space="preserve">
    <value>Yes, rename it</value>
  </data>
    <data name="Your_account_contains_personal_data_that_you_have_given_us" xml:space="preserve">
    <value>Your account contains personal data that you have given us.</value>
  </data>
    <data name="Your_password_has_been_reset" xml:space="preserve">
    <value>Your password has been reset</value>
  </data>
    <data name="Your_report_0_is_ready_to_download" xml:space="preserve">
    <value>Your report {0} is ready to download.</value>
  </data>
    <data name="Your_report_generation_has_failed" xml:space="preserve">
    <value>Your report generation has failed.</value>
  </data>
    <data name="Your_report_is_being_generated" xml:space="preserve">
    <value>Your report is being generated.</value>
  </data>
    <data name="Your_report_is_being_initiated" xml:space="preserve">
    <value>Your report is being initiated.</value>
  </data>
    <data name="Your_report_is_ready_to_download" xml:space="preserve">
    <value>Your report is ready to download.</value>
  </data>
    <data name="Your_report__0__is_ready_to_download\" xml:space="preserve">
    <value>Your report {0} is ready to download.</value>
  </data>
    <data name="your_two_factor_authentication_app_will_provide_you_with_a_unique_code" xml:space="preserve">
    <value>your two factor authentication app will provide you with a unique code.</value>
  </data>
    <data name="You_are_not_authenticated_to_export_this_report" xml:space="preserve">
    <value>You are not authenticated to export this report.</value>
  </data>
    <data name="You_are_not_authorized_to_access_this_page" xml:space="preserve">
    <value>You are not authorized to access this page</value>
  </data>
  <data name="You_have_no_recovery_codes_left" xml:space="preserve">
    <value>You have no recovery codes left.</value>
  </data>
    <data name="You_have_num_recovery_codes_left" xml:space="preserve">
    <value>You have {0} recovery codes left.</value>
  </data>
    <data name="You_have_one_recovery_code_left" xml:space="preserve">
    <value>You have one recovery code left.</value>
  </data>
    <data name="You_must" xml:space="preserve">
    <value>You must</value>
  </data>
    <data name="You_should" xml:space="preserve">
    <value>You should</value>
  </data>
    <data name="Broking_House" xml:space="preserve">
    <value>Broking House</value>
  </data>
    <data name="Contact_Name" xml:space="preserve">
      <value>Contact name</value>
    </data>
    <data name="Broking_House_Relationships" xml:space="preserve">
    <value>Broking House Relationships</value>
  </data>
    <data name="Add_Broking_House" xml:space="preserve">
    <value>Add Broking House</value>
  </data>
  <data name="Broking_Houses" xml:space="preserve">
    <value>Broking Houses</value>
  </data>
    <data name="Subdomain" xml:space="preserve">
    <value>Subdomain</value>
  </data>
    <data name="Manage_Brokers" xml:space="preserve">
    <value>Manage Brokers</value>
  </data>
    <data name="Add_Broking_House_Employee" xml:space="preserve">
      <value>Add Broking House Employee</value>
    </data>
    <data name="Organisations" xml:space="preserve">
      <value>Organisations</value>
    </data>
    <data name="Add_Org" xml:space="preserve">
      <value>Add Org</value>
    </data>
    <data name="Add_Organisation" xml:space="preserve">
      <value>Add Organisation</value>
    </data>
    <data name="Unprocessed" xml:space="preserve">
      <value>Unprocessed</value>
    </data>
    <data name="Organisation_Requests" xml:space="preserve">
      <value>Organisation Requests</value>
    </data>
    <data name="My_Organisations" xml:space="preserve">
      <value>My Organisations</value>
    </data>
    <data name="Add_Organisation_Request" xml:space="preserve">
      <value>Add Organisation Request</value>
    </data>
    <data name="Client_Information" xml:space="preserve">
      <value>Client Information</value>
    </data>
    <data name="Assigned_Brokers" xml:space="preserve">
      <value>Assigned Brokers</value>
    </data>
    <data name="Select_brokers_to_associate_with_the_organisation" xml:space="preserve">
      <value>Select brokers to associate with the organisation</value>
    </data>
    <data name="No_brokers_available" xml:space="preserve">
      <value>No brokers available</value>
    </data>
    <data name="Additional_Info" xml:space="preserve">
      <value>Additional Info</value>
    </data>
    <data name="Additional_comments_for_onboarding" xml:space="preserve">
      <value>Additional comments for TheiaLens onboarding team (not visible to client)</value>
    </data>
    <data name="Organisation_name" xml:space="preserve">
      <value>Organisation name</value>
    </data>
    <data name="Contact_Email" xml:space="preserve">
      <value>Contact Email</value>
    </data>
    <data name="Contact_Phone_Number" xml:space="preserve">
      <value>Contact Phone Number</value>
    </data>
    <data name="Brokers" xml:space="preserve">
      <value>Brokers</value>
    </data>
    <data name="Organisation_Mgt" xml:space="preserve">
      <value>Organisation Mgt</value>
    </data>
    <data name="Access_House" xml:space="preserve">
      <value>Access House</value>
    </data>
    <data name="Access_Org" xml:space="preserve">
      <value>Access Org</value>
    </data>
    <data name="Control_framework_option_formatting" xml:space="preserve">
      <value>The Control Framework name is formatted incorrectly</value>
    </data>
    <data name="Control_framework_validation_failed" xml:space="preserve">
      <value>Could not validate the selected Control Frameworks</value>
    </data>
    <data name="Control_framework_saved" xml:space="preserve">
      <value>Control Framework successfully saved</value>5
    </data>
    <data name="Org_Admin" xml:space="preserve">
      <value>Org Admin</value>
    </data>
    <data name="Org_Admin_Explanation" xml:space="preserve">
      <value>The Organisation Admin will be able to add other Organisation Admins and other Organisation Users. The Admin will be the first user on the organisation’s account</value>
    </data>
    <data name="Admin_Full_Name" xml:space="preserve">
      <value>Admin Full Name</value>
    </data>
    <data name="Admin_Email" xml:space="preserve">
      <value>Admin Email</value>
    </data>
    <data name="Resend_disabled_information" xml:space="preserve">
      <value>Cannot resend again, as this user is already on the system</value>
    </data>
    <data name="Organisation_Unprocessed" xml:space="preserve">
      <value>Unprocessed</value>
    </data>
    <data name="Name" xml:space="preserve">
      <value>Name</value>
    </data>
    <data name="Manager_Users" xml:space="preserve">
      <value>Manage Users</value>
    </data>
    <data name="Copied" xml:space="preserve">
      <value>Copied</value>
    </data>
    <data name="BrokerRequest" xml:space="preserve">
      <value>Request Broker</value>
    </data>
    <data name="Forename" xml:space="preserve">
      <value>Forename</value>
    </data>
    <data name="Could_not_make_request" xml:space="preserve">
      <value>An error occurred and no request was made</value>
    </data>
    <data name="Broking_house_name" xml:space="preserve">
      <value>Broking House Name</value>
    </data>
    <data name="Email_format_invalid" xml:space="preserve">
      <value>Email format is invalid</value>
    </data>
    <data name="Phone_number_is_invalid" xml:space="preserve">
      <value>Phone number is invalid</value>
    </data>
    <data name="Your_information" xml:space="preserve">
      <value>Your Information</value>
    </data>
    <data name="Broking_house_information" xml:space="preserve">
      <value>Broking House Information</value>
    </data>
    <data name="Requests" xml:space="preserve">
      <value>Requests</value>
    </data>
    <data name="Associations" xml:space="preserve">
      <value>Associations</value>
    </data>
    <data name="Broking_Name" xml:space="preserve">
      <value>Broking Name</value>
    </data>
    <data name="Add_Association" xml:space="preserve">
      <value>Add Association</value>
    </data>
    <data name="Associate_And_Email" xml:space="preserve">
      <value>Associate &amp; Email</value>
    </data>
    <data name="You_need_authenticator_QR" xml:space="preserve">
      <value>You need &lt;strong&gt;Microsoft Authenticator&lt;/strong&gt; or &lt;strong&gt;Google Authenticator&lt;/strong&gt; on Android or iOS installed</value>
    </data>
    <data name="Scan_the_QR_code">
      <value>Scan the QR Code or enter this key:</value>
    </data>
    <data name="into_your_app">
      <value>into your app, spaces and casing do not matter</value>
    </data>
    <data name="Copy_codes" xml:space="preserve">
      <value>Copy Codes</value>
    </data>
    <data name="Store_these_codes_in_a_safe_place" xml:space="preserve">
      <value>Store these codes in a safe place</value>
    </data>
    <data name="My_Brokers" xml:space="preserve">
      <value>My Brokers</value>
    </data>
    <data name="Broker_Name" xml:space="preserve">
      <value>Broker Name</value>
    </data>
    <data name="Approved_Date" xml:space="preserve">
      <value>Approved Date</value>
    </data>
    <data name="Approve" xml:space="preserve">
      <value>Approve</value>
    </data>
    <data name="Unapprove" xml:space="preserve">
      <value>Unapprove</value>
    </data>
    <data name="Awaiting_Approval" xml:space="preserve">
      <value>Awaiting Approval</value>
    </data>
    <data name="Show_Awaiting_Approval_Only" xml:space="preserve">
      <value>Show Awaiting Approval Only</value>
    </data>
    <data name="Brokers_Awaiting_Your_Approval" xml:space="preserve">
      <value>broker(s) awaiting your approval</value>
    </data>
    <data name="This_Will_Give_Broker_Access_To_Info" xml:space="preserve">
      <value>This will give the broker access to your security information.</value>
    </data>
    <data name="Broker_Wont_Be_Able_To_Access_Security_Record" xml:space="preserve">
      <value>The broker will not be able to access your security record.</value>
    </data>
    <data name="You_can" xml:space="preserve">
      <value>You can</value>
    </data>
    <data name="Year_Established" xml:space="preserve">
      <value>Year Established</value>
    </data>
    <data name="Reporting_Currency" xml:space="preserve">
      <value>Reporting Currency</value>
    </data>
    <data name="Industry" xml:space="preserve">
      <value>Industry</value>
    </data>
    <data name="HQ_Address" xml:space="preserve">
      <value>Address</value>
    </data>
    <data name="HQ_City" xml:space="preserve">
      <value>City</value>
    </data>
    <data name="HQ_Country" xml:space="preserve">
      <value>Country</value>
    </data>
    <data name="Basic_Info" xml:space="preserve">
      <value>Basic Info</value>
    </data>
    <data name="HQ_Location" xml:space="preserve">
      <value>HQ Location</value>
    </data>
    <data name="HQ_Postcode" xml:space="preserve">
      <value>Postcode/Zipcode</value>
    </data>
    <data name="General" xml:space="preserve">
      <value>General</value>
    </data>
    <data name="Summary" xml:space="preserve">
      <value>Summary</value>
    </data>
    <data name="Activity" xml:space="preserve">
      <value>Activity</value>
    </data>
    <data name="Financials" xml:space="preserve">
      <value>Financials</value>
    </data>
    <data name="Contracts" xml:space="preserve">
      <value>Contracts</value>
    </data>
    <data name="Vendors" xml:space="preserve">
      <value>Vendors</value>
    </data>
    <data name="Suppliers" xml:space="preserve">
      <value>Suppliers</value>
    </data>
    <data name="IT_OT_Info" xml:space="preserve">
      <value>IT/OT Info</value>
    </data>
    <data name="Add_Activity" xml:space="preserve">
      <value>Add Activity</value>
    </data>
    <data name="Year_Started" xml:space="preserve">
      <value>Year Started</value>
    </data>
    <data name="Revenue_Percent" xml:space="preserve">
      <value>Revenue %</value>
    </data>
    <data name="Website" xml:space="preserve">
      <value>Website</value>
    </data>
    <data name="Employees_Count" xml:space="preserve">
      <value>Employees Count</value>
    </data>
    <data name="Revenues_Add_Up_To_100" xml:space="preserve">
      <value>Revenues should add up to 100%</value>
    </data>
    <data name="Approximate_Number_of_Contracts" xml:space="preserve">
      <value>Approximate Number of Contracts</value>
    </data>
    <data name="Average_Contract_Duration_Years" xml:space="preserve">
      <value>Average Contract Duration (years)</value>
    </data>
    <data name="Average_Contract_Size" xml:space="preserve">
      <value>Average Contract Size</value>
    </data>
    <data name="Add_Contract" xml:space="preserve">
      <value>Add Contract</value>
    </data>
    <data name="Client_Name" xml:space="preserve">
      <value>Client Name</value>
    </data>
    <data name="Open_assessment" xml:space="preserve">
      <value>Open assessment</value>
    </data>
    <data name="Open_client" xml:space="preserve">
      <value>Open Client</value>
    </data>
    <data name="Product_Service" xml:space="preserve">
      <value>Product/Service</value>
    </data>
    <data name="Contract_Value" xml:space="preserve">
      <value>Contract Value</value>
    </data>
    <data name="Top_Five_Contracts" xml:space="preserve">
      <value>Top Five Contracts</value>
    </data>
    <data name="Duration_Years" xml:space="preserve">
      <value>Duration (years)</value>
    </data>
    <data name="Organisation_Revenue" xml:space="preserve">
      <value>Organisation Revenue</value>
    </data>
    <data name="Last_Year_Revenue" xml:space="preserve">
      <value>Last Year Revenue</value>
    </data>
    <data name="Last_Year_Gross_Profit" xml:space="preserve">
      <value>Last Year Gross Profit</value>
    </data>
    <data name="Current_Year_Revenue" xml:space="preserve">
      <value>Current Year Revenue</value>
    </data>
    <data name="Next_Year_Projected_Revenue" xml:space="preserve">
      <value>Next Year Projected Revenue</value>
    </data>
    <data name="Country_Revenue_Split" xml:space="preserve">
      <value>Country Revenue Split</value>
    </data>
    <data name="Country" xml:space="preserve">
      <value>Country</value>
    </data>
    <data name="Industry_Revenue_Split" xml:space="preserve">
      <value>Industry Revenue Split</value>
    </data>
    <data name="StoredData" xml:space="preserve">
      <value>Stored Data</value>
    </data>
    <data name="Number_of_Personal_identifiable_information_records" xml:space="preserve">
      <value>Number of Personal Identifiable Information (PII) Records</value>
    </data>
    <data name="Number_of_Payment_Card_Information_Records" xml:space="preserve">
      <value>Number of Payment Card Information (PCI) Records</value>
    </data>
    <data name="Number_of_Personal_Health_Information_Records" xml:space="preserve">
      <value>Number of Personal Health Information (PHI) Records</value>
    </data>
    <data name="Number_of_Biometric_Records" xml:space="preserve">
      <value>Number of Biometric Records</value>
    </data>
    <data name="Approx_Number_of_Personal_Records_Stored_In_Db" xml:space="preserve">
      <value>Approximate Number of Personal Records Stored in a Single Db</value>
    </data>
    <data name="Number_of_Servers" xml:space="preserve">
      <value>Number of Servers</value>
    </data>
    <data name="Number_of_Endpoints" xml:space="preserve">
      <value>Number of Endpoints</value>
    </data>
    <data name="Approx_Total_IT_Hardware_Value" xml:space="preserve">
      <value>Approx. Total IT Hardware Value</value>
    </data>
    <data name="Approx_Total_OT_Hardware_Value" xml:space="preserve">
      <value>Approx. Total OT Hardware Value</value>
    </data>
    <data name="IT_OT_Infrastructure" xml:space="preserve">
      <value>IT/OT Infrastructure</value>
    </data>
    <data name="Data_Centre_Location" xml:space="preserve">
      <value>Data Centre Locations</value>
    </data>
    <data name="No_of_Data_Centres" xml:space="preserve">
      <value>No. of Data Centres</value>
    </data>
    <data name="Add_Location" xml:space="preserve">
      <value>Add Location</value>
    </data>
    <data name="Unexpected_Error" xml:space="preserve">
      <value>Unexpected Error</value>
    </data>
    <data name="Add_Supplier" xml:space="preserve">
      <value>Add Supplier</value>
    </data>
    <data name="Supplier_Name" xml:space="preserve">
      <value>Supplier Name</value>
    </data>
    <data name="Control_Framework_Name" xml:space="preserve">
      <value>Control Framework Name</value>
    </data>
    <data name="Score" xml:space="preserve">
      <value>Score</value>
    </data>
    <data name="Product_Services" xml:space="preserve">
      <value>Product/Services</value>
    </data>
    <data name="Supplier_Type" xml:space="preserve">
      <value>Supplier Type</value>
    </data>
    <data name="Invalid_data" xml:space="preserve">
      <value>Invalid Data</value>
    </data>
    <data name="Version" xml:space="preserve">
      <value>Version</value>
    </data>
    <data name="Enabled" xml:space="preserve">
      <value>Enabled</value>
    </data>
    <data name="Assessment_Version" xml:space="preserve">
      <value>Assessment Version</value>
    </data>
    <data name="Date" xml:space="preserve">
      <value>Date</value>
    </data>
    <data name="All_Forms" xml:space="preserve">
      <value>All Forms</value>
    </data>
    <data name="Org_Profile" xml:space="preserve">
      <value>Org Profile</value>
    </data>
    <data name="Add_Selected_Forms" xml:space="preserve">
      <value>Add Selected Forms</value>
    </data>
    <data name="Request_Form" xml:space="preserve">
      <value>Request Form</value>
    </data>
    <data name="Active" xml:space="preserve">
      <value>Active</value>
    </data>
    <data name="Back" xml:space="preserve">
      <value>Back</value>
    </data>
    <data name="Contact" xml:space="preserve">
      <value>Contact</value>
    </data>
    <data name="You_have_to_select_at_least_one_form" xml:space="preserve">
      <value>You have to pick at least one form</value>
    </data>
    <data name="Navigate_to_Broker" xml:space="preserve">
      <value>Navigate to Broker</value>
    </data>
    <data name="Remove_Association" xml:space="preserve">
      <value>Remove Association</value>
    </data>
    <data name="No_brokers_selected" xml:space="preserve">
      <value>No brokers selected</value>
    </data>
    <data name="Updated_Successfully" xml:space="preserve">
      <value>Updated Successfully</value>
    </data>
    <data name="Add_Version" xml:space="preserve">
      <value>Add Version</value>
    </data>
    <data name="Business_Activity" xml:space="preserve">
      <value>Business Activity</value>
    </data>
    <data name="Organisation" xml:space="preserve">
      <value>Organisation</value>
    </data>
    <data name="No_form_versions_selected" xml:space="preserve">
      <value>No form versions selected</value>
    </data>
    <data name="MyForms" xml:space="preserve">
      <value>My Forms</value>
    </data>
    <data name="AllForms" xml:space="preserve">
      <value>All Forms</value>
    </data>
    <data name="Application_Form_Code" xml:space="preserve">
      <value>Code</value>
    </data>
    <data name="Application_Form_Name" xml:space="preserve">
      <value>Application Form Name</value>
    </data>
    <data name="Application_Form_Completed_Date" xml:space="preserve">
      <value>Completed Date</value>
    </data>
    <data name="Application_Form_Status" xml:space="preserve">
      <value>Status</value>
    </data>
    <data name="Application_Form_Origin" xml:space="preserve">
      <value>Origin</value>
    </data>
    <data name="Open" xml:space="preserve">
      <value>Open</value>
    </data>
    <data name="Open_And_Submit" xml:space="preserve">
      <value>Open and Submit</value>
    </data>
    <data name="Manage_Underwriters" xml:space="preserve">
      <value>Manage Underwriters</value>
    </data>
    <data name="Answer_Application_Form" xml:space="preserve">
      <value>Answer Application Form</value>
    </data>
    <data name="Read_Application_Form" xml:space="preserve">
      <value>Read Application Form</value>
    </data>
    <data name="Not_Completed" xml:space="preserve">
      <value>Not Completed</value>
    </data>
    <data name="Survey_cannot_be_null" xml:space="preserve">
      <value>Survey cannot be null</value>
    </data>
    <data name="Application_form_submission" xml:space="preserve">
      <value>Are you sure you would like to proceed? Confirming your intention to submit will mean the form will no longer be editable by anyone and you will only be able to view it</value>
    </data>
    <data name="Application_form_submission_supplier" xml:space="preserve">
      <value>Are you sure you would like to proceed? Confirming your intention to submit will mean the form is selectable by any and all organisations you work with, you can still edit and save changes</value>
    </data>
    <data name="Confirm_submission" xml:space="preserve">
      <value>Confirm Submission</value>
    </data>
    <data name="Save_progress" xml:space="preserve">
      <value>Save Progress</value>
    </data>
    <data name="An_error_occured" xml:space="preserve">
      <value>An error occured</value>
    </data>
    <data name="Insurers" xml:space="preserve">
      <value>Insurers</value>
    </data>
    <data name="Add_Insurer" xml:space="preserve">
      <value>Add Insurer</value>
    </data>
    <data name="Insurer" xml:space="preserve">
      <value>Insurer</value>
    </data>
    <data name="Submissions" xml:space="preserve">
      <value>Submissions</value>
    </data>
    <data name="Create_Submission" xml:space="preserve">
      <value>Create Submission</value>
    </data>
    <data name="Create_New_Submission" xml:space="preserve">
      <value>Create New Submission</value>
    </data>
    <data name="Submission" xml:space="preserve">
      <value>Submission</value>
    </data>
    <data name="Content" xml:space="preserve">
      <value>Content</value>
    </data>
    <data name="Due_By" xml:space="preserve">
      <value>Due By</value>
    </data>
    <data name="Organisation_name_cannot_be_null" xml:space="preserve">
      <value>Organisation name cannot be null</value>
    </data>
    <data name="Employees_cannot_be_null" xml:space="preserve">
      <value>Employees cannot be null</value>
    </data>
    <data name="Application_form_cannot_be_null" xml:space="preserve">
      <value>Application forms cannot be null</value>
    </data>
    <data name="Please_fill_in_all_information" xml:space="preserve">
      <value>Please fill in all the information</value>
    </data>
    <data name="Application_Form_Modified_On" xml:space="preserve">
      <value>Modified On</value>
    </data>
    <data name="Application_Form_Completed_On" xml:space="preserve">
      <value>Completed On</value>
    </data>
    <data name="Not_Started" xml:space="preserve">
      <value>Not Started</value>
    </data>
    <data name="Please_select_an_organisation" xml:space="preserve">
      <value>Please select an organisation</value>
    </data>
    <data name="Void" xml:space="preserve">
      <value>Void</value>
    </data>
    <data name="Voided" xml:space="preserve">
      <value>Voided</value>
    </data>
    <data name="Selectable_Application_Forms" xml:space="preserve">
      <value>Selectable Application Forms</value>
    </data>
    <data name="Progress" xml:space="preserve">
      <value>Progress</value>
    </data>
    <data name="Submission_Name" xml:space="preserve">
      <value>Submission Name</value>
    </data>
    <data name="Removed" xml:space="preserve">
      <value>Removed</value>
    </data>
    <data name="Application_form_has_been_removed" xml:space="preserve">
      <value>The application form was removed before submitting</value>
    </data>
    <data name="Form_removed" xml:space="preserve">
      <value>Application form removed</value>
    </data>
    <data name="Tooltip_cannot_remove_application_form" xml:space="preserve">
      <value>Form cannot be removed</value>
    </data>
    <data name="Tooltip_remove_application_form" xml:space="preserve">
      <value>Remove from the submission</value>
    </data>
    <data name="Tooltip_can_void_submission" xml:space="preserve">
      <value>Void the submission and all forms beneath it</value>
    </data>
    <data name="Tooltip_cannot_void_submission" xml:space="preserve">
      <value>Cannot void the Submission once it has either been completed or voided</value>
    </data>
    <data name="RequestedBy" xml:space="preserve">
      <value>Requested By</value>
    </data>
    <data name="Requested_On" xml:space="preserve">
      <value>Requested On</value>
    </data>
    <data name="Last_Modified_On" xml:space="preserve">
      <value>Last Modified On</value>
    </data>
    <data name="Internal" xml:space="preserve">
      <value>Internal</value>
    </data>
    <data name="Tooltip_cannot_submit_submission" xml:space="preserve">
      <value>Submission incomplete</value>
    </data>
    <data name="Tooltip_can_submit_submission" xml:space="preserve">
      <value>Sends out your Submission/Control Assessment to be reviewed</value>
    </data>
    <data name="Tooltip_cannot_open_application_form" xml:space="preserve">
      <value>Unable to open application form</value>
    </data>
    <data name="Tooltip_open_application_form" xml:space="preserve">
      <value>Open application form</value>
    </data>
    <data name="Tooltip_open_analysis" xml:space="preserve">
      <value>Open analysis</value>
    </data>
    <data name="Tooltip_download_file" xml:space="preserve">
      <value>Download file</value>
    </data>
    <data name="Tooltip_can_remove_supplier" xml:space="preserve">
      <value>Can remove Supplier</value>
    </data>
    <data name="Tooltip_can_open_supplier_page" xml:space="preserve">
      <value>Can open Supplier</value>
    </data>
    <data name="No_Data" xml:space="preserve">
      <value>No Data</value>
    </data>
    <data name="Create_Self_Assessment" xml:space="preserve">
      <value>Create Self Assessment</value>
    </data>
    <data name="Tooltip_cannot_submit_submission_to_self" xml:space="preserve">
      <value>Cannot submit submission to yourself</value>
    </data>
    <data name="Confirm_submit_submission" xml:space="preserve">
      <value>Confirm Submitting for Review</value>
    </data>
    <data name="Submitting_submission" xml:space="preserve">
      <value>Once shared, this Quote Request can be viewed by others and cannot be modified you can modify who it has been shared to at anytime. Once submitted you will be taken back automatically when the analysis completes</value>
    </data>
    <data name="Submitted" xml:space="preserve">
      <value>Submitted</value>
    </data>
    <data name="Tooltip_cannot_submit_already_submitted" xml:space="preserve">
      <value>Cannot submit and already submitted form</value>
    </data>
    <data name="Tooltip_cannot_view_form" xml:space="preserve">
      <value>Cannot view application form</value>
    </data>
    <data name="View_Application_Form" xml:space="preserve">
      <value>View Application Form</value>
    </data>
    <data name="Tooltip_can_open_in_ready_only" xml:space="preserve">
      <value>Open form in read-only</value>
    </data>
    <data name="Tooltip_cannot_open_submission_in_dashboard" xml:space="preserve">
      <value>Cannot open this submission in your dashboard</value>
    </data>
    <data name="Tooltip_can_open_submission_in_dashboard" xml:space="preserve">
      <value>Can open submissiom in your dashboard</value>
    </data>
    <data name="Send" xml:space="preserve">
      <value>Send</value>
    </data>
    <data name="Tooltip_can_send_to_underwriter" xml:space="preserve">
      <value>Send submission to underwriter</value>
    </data>
    <data name="Tooltip_cannot_send_submission_to_underwriter" xml:space="preserve">
      <value>Cannot send submission to underwriter</value>
    </data>
    <data name="Broker" xml:space="preserve">
      <value>Broker</value>
    </data>
    <data name="Control_Assessments" xml:space="preserve">
      <value>Control Assessments</value>
    </data>
    <data name="Assessment_Name" xml:space="preserve">
      <value>Assessment Name</value>
    </data>
    <data name="Tooltip_open_submission" xml:space="preserve">
      <value>Open submission to view forms</value>
    </data>
    <data name="Tooltip_open_submission_in_dashboard" xml:space="preserve">
      <value>Open submission dashboard</value>
    </data>
    <data name="Tooltip_cannot_open_submission" xml:space="preserve">
      <value>Unable to open submission</value>
    </data>
    <data name="Show" xml:space="preserve">
      <value>Show</value>
    </data>
    <data name="Add_a_question" xml:space="preserve">
      <value>Add a question...</value>
    </data>
    <data name="Forum" xml:space="preserve">
      <value>Forum</value>
    </data>
    <data name="Add_Question" xml:space="preserve">
      <value>Add Question</value>
    </data>
    <data name="Awaiting" xml:space="preserve">
      <value>Awaiting</value>
    </data>
    <data name="Edited" xml:space="preserve">
      <value>Edited</value>
    </data>
    <data name="Add_a_message" xml:space="preserve">
      <value>Add a message...</value>
    </data>
    <data name="Add_Message" xml:space="preserve">
      <value>Add Message</value>
    </data>
    <data name="Question_is_waiting_for_approval" xml:space="preserve">
      <value>Question is waiting for approval</value>
    </data>
    <data name="Satisfied_State_Name" xml:space="preserve">
      <value>Positives</value>
    </data>
    <data name="Dissatisfied_State_Name" xml:space="preserve">
      <value>Areas of Improvement</value>
    </data>
    <data name="Unanswered_State_Name" xml:space="preserve">
      <value>Unanswered</value>
    </data>
    <data name="Assessment_Date" xml:space="preserve">
      <value>Assessment Date</value>
    </data>
    <data name="Loading" xml:space="preserve">
      <value>Loading...</value>
    </data>
    <data name="Basic_Submission_Data" xml:space="preserve">
      <value>Basic Submission Data</value>
    </data>
    <data name="Number_of_Submissions" xml:space="preserve">
      <value>Number of Submissions</value>
    </data>
    <data name="Number_of_Incomplete_Submissions" xml:space="preserve">
      <value>Number of Incomplete Submissions</value>
    </data>
    <data name="Signed_Off_By" xml:space="preserve">
      <value>Signed Off By</value>
    </data>
    <data name="Benchmark_Results" xml:space="preserve">
      <value>Benchmark Results</value>
    </data>
    <data name="Submission_cannot_be_null" xml:space="preserve">
      <value>Submission cannot be null</value>
    </data>
    <data name="Organisational_Information" xml:space="preserve">
      <value>Organisation Information</value>
    </data>
    <data name="Revenue" xml:space="preserve">
      <value>Revenue</value>
    </data>
    <data name="Question_Name" xml:space="preserve">
      <value>Question Name</value>
    </data>
    <data name="Answer" xml:space="preserve">
      <value>Answer</value>
    </data>
    <data name="Comments" xml:space="preserve">
      <value>Comments</value>
    </data>
    <data name="Files" xml:space="preserve">
      <value>Files</value>
    </data>
    <data name="My_Control_Assessments" xml:space="preserve">
      <value>My Control Assessments</value>
    </data>
    <data name="Manage_Documents" xml:space="preserve">
      <value>Manage Documents</value>
    </data>
    <data name="Organisation_Profile" xml:space="preserve">
      <value>Organisation Profile</value>
    </data>
    <data name="Edit_View_Organisation_Profile" xml:space="preserve">
      <value>Edit/View Organisation Profile</value>
    </data>
    <data name="Submission_View_Organisation_Profile_Information" xml:space="preserve">
      <value>Please make sure your Organisation's profile is up-to-date before signing off on your Submission</value>
    </data>
    <data name="Analysing" xml:space="preserve">
      <value>Analysing...</value>
    </data>
    <data name="Control_Framework_Domain_Details" xml:space="preserve">
      <value>Control Framework Domain Details</value>
    </data>
    <data name="Please_select_a_domain" xml:space="preserve">
      <value>Please select a domain</value>
    </data>
    <data name="Maturity" xml:space="preserve">
      <value>Maturity</value>
    </data>
    <data name="Top_Areas_of_Improvement" xml:space="preserve">
      <value>Top 10 Areas of Improvement</value>
    </data>
    <data name="Insurer_Name" xml:space="preserve">
      <value>Insurer Name</value>
    </data>
    <data name="Underwriter" xml:space="preserve">
      <value>Underwriter</value>
    </data>
    <data name="Share" xml:space="preserve">
      <value>Share</value>
    </data>
    <data name="Tooltip_can_share_submission_to_broking_house" xml:space="preserve">
      <value>Can share Submission to Broking House</value>
    </data>
  <data name="Tooltip_can_share_submission_to_others" xml:space="preserve">
      <value>Share submission to Underwriters and Broking Houses</value>
    </data>
    <data name="My_Submissions" xml:space="preserve">
      <value>My Submissions</value>
    </data>
    <data name="Analyse" xml:space="preserve">
      <value>Analyse</value>
    </data>
    <data name="Submitted_On" xml:space="preserve">
      <value>Submitted On</value>
    </data>
    <data name="Submitted_By" xml:space="preserve">
      <value>Submitted By</value>
    </data>
    <data name="Created_By" xml:space="preserve">
      <value>Created By</value>
    </data>
    <data name="Organisation_Submissions" xml:space="preserve">
      <value>Organisation Submissions</value>
    </data>
    <data name="Generate_Quote_Request" xml:space="preserve">
      <value>Generate Quote Request</value>
    </data>
    <data name="Add_Brokers" xml:space="preserve">
      <value>Add Brokers</value>
    </data>
    <data name="Add_Underwriters" xml:space="preserve">
      <value>Add Underwriters</value>
    </data>
    <data name="Send_to_Underwriters_and_Brokers" xml:space="preserve">
      <value>Send to Brokers and Underwriters</value>
    </data>
    <data name="View_Organisation" xml:space="preserve">
      <value>View Organisation</value>
    </data>
    <data name="Tooltip_cannot_remove_file" xml:space="preserve">
      <value>Cannot remove file you do not own</value>
    </data>
    <data name="Tooltip_view_quote_request" xml:space="preserve">
      <value>View Quote Request</value>
    </data>
    <data name="Pending_Broker_Requests" xml:space="preserve">
      <value>Pending Broker Request(s)</value>
    </data>
    <data name="Unanswered_Forum_Questions" xml:space="preserve">
      <value>Unanswered Forum Question(s)</value>
    </data>
    <data name="Incomplete_Assessments" xml:space="preserve">
      <value>Incomplete Assessment(s)</value>
    </data>
    <data name="Organisation_Profile_Complete" xml:space="preserve">
      <value>Organisation Profile Complete</value>
    </data>
    <data name="Brokers_that_have_access_to_your_most_recent_submission" xml:space="preserve">
      <value>Brokers that have access to your most recent submission</value>
    </data>
    <data name="No_broker_has_access_to_your_most_recent_control_assessment" xml:space="preserve">
      <value>No broker has access to your most recent control assessment</value>
    </data>
    <data name="Underwriters_that_have_access_to_your_most_recent_submission" xml:space="preserve">
      <value>Underwriters that have access to your most recent submission</value>
    </data>
    <data name="No_underwriter_has_access_to_your_most_recent_control_assessment" xml:space="preserve">
      <value>No underwriter has access to your most recent control assessment</value>
    </data>
    <data name="View_Full_Submission" xml:space="preserve">
      <value>View Full Submission</value>
    </data>
    <data name="Please_add_insurers_and_brokers" xml:space="preserve">
      <value>Please add insurers and brokers</value>
    </data>
    <data name="Please_enter_submission_name" xml:space="preserve">
      <value>Please enter Submission name</value>
    </data>
    <data name="Modify_Brokers_And_Underwriters" xml:space="preserve">
      <value>Modify Brokers and Underwriters</value>
    </data>
    <data name="Underwriters" xml:space="preserve">
      <value>Underwriters</value>
    </data>
    <data name="Please_select_at_least_one_application_form" xml:space="preserve">
      <value>Please select at least one application form</value>
    </data>
    <data name="Validation_of_the_code_did_not_succeed" xml:space="preserve">
      <value>Validation of the code did not succeed</value>
    </data>
    <data name="Change_your_Primary_Broking_House" xml:space="preserve">
      <value>Change your Primary Broking House</value>
    </data>
    <data name="Yourself" xml:space="preserve">
      <value>Yourself</value>
    </data>
    <data name="Internal_Assessment" xml:space="preserve">
      <value>Internal Assessment</value>
    </data>
    <data name="Assign_Brokers" xml:space="preserve">
      <value>Assign Brokers</value>
    </data>
    <data name="Assign_Underwriters" xml:space="preserve">
      <value>Assign Underwriters</value>
    </data>
    <data name="Submission_Requests" xml:space="preserve">
      <value>Submission Requests</value>
    </data>
    <data name="View_Submission" xml:space="preserve">
      <value>View Submission</value>
    </data>
    <data name="Organisation_Submission" xml:space="preserve">
      <value>Organisation Submission</value>
    </data>
    <data name="My_Quote_Requests" xml:space="preserve">
      <value>My Quote Requests</value>
    </data>
    <data name="View_Control_Framework_Domain" xml:space="preserve">
      <value>View Control Framework Domain</value>
    </data>
    <data name="Full_Submission" xml:space="preserve">
      <value>Full Submission</value>
    </data>
    <data name="Request_Submission" xml:space="preserve">
      <value>Request Submission</value>
    </data>
    <data name="Request_New_Submission" xml:space="preserve">
      <value>Request New Submission</value>
    </data>
    <data name="Application_Forms" xml:space="preserve">
      <value>Application Forms</value>
    </data>
    <data name="Supplier_application_forms_contains_no_data" xml:space="preserve">
      <value>No application forms</value>
    </data>
    <data name="Supplier_files_contains_no_data" xml:space="preserve">
      <value>No additional files</value>
    </data>
    <data name="Application_Form" xml:space="preserve">
      <value>Application Form</value>
    </data>
    <data name="Value_cannot_be_null" xml:space="preserve">
      <value>Value cannot be null</value>
    </data>
    <data name="Informational_State_Name" xml:space="preserve">
      <value>Informational</value>
    </data>
    <data name="Unread_Submissions" xml:space="preserve">
      <value>Unread Submissions</value>
    </data>
    <data name="Unopened_Quote_Requests" xml:space="preserve">
      <value>Unopened Quote Requests</value>
    </data>
    <data name="Submissions_received_by_team_in" xml:space="preserve">
      <value>Submissions Received By Team in {0}</value>
    </data>
    <data name="Top_3_Industries" xml:space="preserve">
      <value>Top 3 Industries</value>
    </data>
    <data name="Top_3_Countries" xml:space="preserve">
      <value>Top 3 Countries</value>
    </data>
    <data name="Organisation_Revenue_Split" xml:space="preserve">
      <value>Organisation Revenue Split</value>
    </data>
    <data name="Quote_Request_Split" xml:space="preserve">
      <value>Quote Request Split</value>
    </data>
    <data name="Unopened_Organisation_Submissions" xml:space="preserve">
      <value>Unopened Org Submissions</value>
    </data>
    <data name="Organisation_Submissions_This_Month" xml:space="preserve">
      <value>Org Submissions This Month</value>
    </data>
    <data name="Open_Organisation_Requests" xml:space="preserve">
      <value>Open Org Requests</value>
    </data>
    <data name="Quote_Requests_Sent" xml:space="preserve">
      <value>Quote Requests Sent</value>
    </data>
    <data name="Please_select_a_role" xml:space="preserve">
      <value>Please select a role</value>
    </data>
    <data name="Details" xml:space="preserve">
      <value>Details</value>
    </data>
    <data name="Primary_Broker" xml:space="preserve">
      <value>Primary Broker</value>
    </data>
    <data name="Inactive" xml:space="preserve">
      <value>Inactive</value>
    </data>
    <data name="Request_is_taking_longer_than_expected" xml:space="preserve">
      <value>Request is taking longer than expected...</value>
    </data>
    <data name="No_organisation_selected" xml:space="preserve">
      <value>No organisation selected</value>
    </data>
    <data name="No_broking_house_selected" xml:space="preserve">
      <value>No broking house selected</value>
    </data>
    <data name="Not_Available" xml:space="preserve">
      <value>N/A</value>
    </data>
    <data name="Suspended" xml:space="preserve">
      <value>Suspended</value>
    </data>
    <data name="Position" xml:space="preserve">
      <value>Position</value>
    </data>
    <data name="Top_5_Percent" xml:space="preserve">
      <value>Top 5th Percent</value>
    </data>
    <data name="Top_10_Percent" xml:space="preserve">
      <value>Top 10th Percent</value>
    </data>
    <data name="Top_20_Percent" xml:space="preserve">
      <value>Top 20th Percent</value>
    </data>
    <data name="Top_50th_Percent" xml:space="preserve">
      <value>Top 50th Percent</value>
    </data>
    <data name="Greater_than_top_50th_percent" xml:space="preserve">
      <value>>50th Percent</value>
    </data>
    <data name="Percent_Invalid" xml:space="preserve">
      <value>Invalid Percentage</value>
    </data>
    <data name="Request_Broker" xml:space="preserve">
      <value>Request Broker</value>
    </data>
    <data name="In_Use" xml:space="preserve">
      <value>In Use</value>
    </data>
    <data name="Product_in_use_cannot_be_modified" xml:space="preserve">
      <value>Product is in use and cannot be modified</value>
    </data>
    <data name="Please_add_a_page_to_the_form" xml:space="preserve">
      <value>Please add a page to the form</value>
    </data>
    <data name="Please_add_a_question" xml:space="preserve">
      <value>Please add a question to your form</value>
    </data>
    <data name="Confirm_voiding_submission" xml:space="preserve">
      <value>Confirm Voiding Submission</value>
    </data>
    <data name="Void_submission" xml:space="preserve">
      <value>Are you sure you want to void this Submission? Once voided, it cannot be edited</value>
    </data>
    <data name="Confirm_removing_application_form" xml:space="preserve">
      <value>Confirm Removing Application Form</value>
    </data>
    <data name="Remove_application_form" xml:space="preserve">
      <value>Are you sure you want to remove this application form? Once removed, it cannot be edited</value>
    </data>
    <data name="Are_you_sure_you_want_to_remove_the_application_form_" xml:space="preserve">
      <value>Are you sure you want to remove the application form?</value>
    </data>
    <data name="Complete_mfa_settings" xml:space="preserve">
      <value>Complete your MFA settings</value>
    </data>
    <data name="This_Month" xml:space="preserve">
      <value>This month</value>
    </data>
    <data name="Complete_Application_Form" xml:space="preserve">
      <value>Complete Application Form</value>
    </data>
    <data name="Complete" xml:space="preserve">
      <value>Complete</value>
    </data>
    <data name="Download_Org_Profile" xml:space="preserve">
      <value>Download Org Profile</value>
    </data>
    <data name="Scheduled" xml:space="preserve">
      <value>Scheduled</value>
    </data>
    <data name="Broker_Information" xml:space="preserve">
      <value>Broker Information</value>
    </data>
    <data name="Modify_Your_Mfa_Settings" xml:space="preserve">
      <value>Modify your MFA settings</value>
    </data>
    <data name="Reset" xml:space="preserve">
      <value>Reset</value>
    </data>
    <data name="Show_Org_Profile" xml:space="preserve">
      <value>Org Profile</value>
    </data>
    <data name="Tooltip_can_duplicate" xml:space="preserve">
      <value>Duplicate the submission</value>
    </data>
    <data name="Duplicate" xml:space="preserve">
      <value>Duplicate</value>
    </data>
    <data name="Duplicate_Submission" xml:space="preserve">
      <value>Duplicate Submission</value>
    </data>
    <data name="Duplicate_submission_message" xml:space="preserve">
      <value>Are you sure you want to duplicate this Submission?</value>
    </data>
    <data name="State" xml:space="preserve">
      <value>State</value>
    </data>
    <data name="Share_With_Underwriters" xml:space="preserve">
      <value>Share with Underwriters</value>
    </data>
    <data name="Share_With_Brokers" xml:space="preserve">
      <value>Share with Brokers</value>
    </data>
    <data name="Active_Broker" xml:space="preserve">
      <value>Active Broker</value>
    </data>
    <data name="Control_Framework_Description_Theia" xml:space="preserve">
      <value>TheiaLense SF Score is our proprietary control framework based on weighted averages of responses</value>
    </data>
    <data name="Control_Framework_Description_Not_Theia" xml:space="preserve">
      <value>Score is based on application form responses</value>
    </data>
    <data name="Submission_Name_Cannot_be_Null" xml:space="preserve">
      <value>Submission name cannot be null</value>
    </data>
    <data name="Edit_Version" xml:space="preserve">
      <value>Edit Version</value>
    </data>
    <data name="Download_as_PDF" xml:space="preserve">
      <value>Download as PDF</value>
    </data>
    <data name="Tooltip_can_send_submission" xml:space="preserve">
      <value>The submission can be sent to the client</value>
    </data>
    <data name="Confirm_sending_submission" xml:space="preserve">
      <value>Confirm sending submission</value>
    </data>
    <data name="Send_submission" xml:space="preserve">
      <value>Send submission to the client for them to complete</value>
    </data>
    <data name="Modified_By_Broker" xml:space="preserve">
      <value>Modified by Broker</value>
    </data>
    <data name="Tooltip_cannot_send_submission" xml:space="preserve">
      <value>Cannot send submission when it has already beeen sent</value>
    </data>
    <data name="Roles" xml:space="preserve">
      <value>Roles</value>
    </data>
    <data name="Submission_Progress" xml:space="preserve">
      <value>Submission Progress</value>
    </data>
    <data name="Application_Form_Progress" xml:space="preserve">
      <value>Application Form Progress</value>
    </data>
    <data name="Admin_Name" xml:space="preserve">
      <value>Admin Name</value>
    </data>
    <data name="Assign_Roles" xml:space="preserve">
      <value>Assign Roles</value>
    </data>
    <data name="Unanswered_Questions" xml:space="preserve">
      <value>Unanswered questions</value>
    </data>
    <data name="Tooltip_cannot_send_voided_submission" xml:space="preserve">
      <value>Cannot send voided submission</value>
    </data>
    <data name="Assessments" xml:space="preserve">
      <value>Assessments</value>
    </data>
    <data name="Add_Suppliers" xml:space="preserve">
      <value>Add Suppliers</value>
    </data>
    <data name="Relevant_Suppliers" xml:space="preserve">
      <value>Relevant Suppliers</value>
    </data>
    <data name="Company_Number" xml:space="preserve">
      <value>Company Number</value>
    </data>
    <data name="Application_Form_Details" xml:space="preserve">
      <value>Application Form Details</value>
    </data>
    <data name="Requirements" xml:space="preserve">
      <value>Requirements</value>
    </data>
    <data name="Calculated_Answers" xml:space="preserve">
      <value>Calculated Answers</value>
    </data>
    <data name="Summary_Details" xml:space="preserve">
      <value>Summary Details</value>
    </data>
    <data name="My_Clients" xml:space="preserve">
      <value>My Clients</value>
    </data>
    <data name="Upload" xml:space="preserve">
      <value>Upload</value>
    </data>
    <data name="Visibility" xml:space="preserve">
      <value>Visibility</value>
    </data>
    <data name="Public" xml:space="preserve">
      <value>Public</value>
    </data>
    <data name="Is_Deleted" xml:space="preserve">
      <value>Is Deleted</value>
    </data>
    <data name="My_Eyes_Only" xml:space="preserve">
      <value>My Eyes Only</value>
    </data>
    <data name="Private" xml:space="preserve">
      <value>Private</value>
    </data>
    <data name="Mark_File_Deleted_Confirm_Message" xml:space="preserve">
      <value>The file is in use and will be marked as deleted, but will remain in the system. Do you want to continue?</value>
    </data>
    <data name="Delete_File_Permanently_Confirm_Message" xml:space="preserve">
      <value>The file is not in use and will be permanently deleted. Do you want to continue?</value>
    </data>
    <data name="Select_File" xml:space="preserve">
      <value>Select file...</value>
    </data>
    <data name="Single_File_Dropzone_Hint" xml:space="preserve">
      <value>Drag and drop file here to upload</value>
    </data>
    <data name="Supplier_File_Upload_Note_Text" xml:space="preserve">
      <value>Maximum file size: 5MB. Allowed extensions: .pdf, .docx, .doc, .xlsx, .xls, .pptx, .ppt, .jpeg, .jpg, .png</value>
    </data>
    <data name="Date_Sent" xml:space="preserve">
      <value>Date sent</value>
    </data>
    <data name="Date_Completed_On" xml:space="preserve">
      <value>Date Completed On</value>
    </data>
    <data name="View_Assessment_Request" xml:space="preserve">
      <value>View Assessment Request</value>
    </data>
    <data name="Completed_On" xml:space="preserve">
      <value>Completed On</value>
    </data>
    <data name="Assessment_Request" xml:space="preserve">
      <value>Assessment Request</value>
    </data>
    <data name="Assessment_Requests" xml:space="preserve">
      <value>Assessment Requests</value>
    </data>
    <data name="Domain_Analysis" xml:space="preserve">
      <value>Domain Analysis</value>
    </data>
    <data name="Response_Analysis" xml:space="preserve">
      <value>Response Analysis</value>
    </data>
    <data name="NotApplicable" xml:space="preserve">
      <value>N/A</value>
    </data>
    <data name="I_cannot_provide_this_file" xml:space="preserve">
      <value>I can't provide this file</value>
    </data>
    <data name="Supplier_did_not_provide_file" xml:space="preserve">
      <value>Supplier did not provide file</value>
    </data>
    <data name="Expired" xml:space="preserve">
      <value>Expired</value>
    </data>
    <data name="Expiring_Soon" xml:space="preserve">
      <value>Expiring Soon</value>
    </data>
    <data name="Up_To_Date" xml:space="preserve">
      <value>Up to date</value>
    </data>
    <data name="No_files_requested" xml:space="preserve">
      <value>No files requested</value>
    </data>
    <data name="No_forms_requested" xml:space="preserve">
      <value>No forms requested</value>
    </data>
    <data name="Application_Form_Type" xml:space="preserve">
      <value>Application Form Type</value>
    </data>
    <data name="Supplier" xml:space="preserve">
      <value>Supplier</value>
    </data>
    <data name="Incomplete" xml:space="preserve">
      <value>Incomplete</value>
    </data>
    <data name="Organisation_request" xml:space="preserve">
      <value>Organisation Request</value>
    </data>
    <data name="Selected_suppliers" xml:space="preserve">
      <value>Selected Suppliers</value>
    </data>
    <data name="Select_a_supplier" xml:space="preserve">
      <value>Select a supplier...</value>
    </data>
    <data name="Tooltip_view_form_disabled" xml:space="preserve">
      <value>Application form incomplete</value>
    </data>
    <data name="Tooltip_view_form" xml:space="preserve">
      <value>View analysis</value>
    </data>
    <data name="Tooltip_edit_form" xml:space="preserve">
      <value>Edit application form</value>
    </data>
    <data name="Latest_submission" xml:space="preserve">
      <value>Latest Submission</value>
    </data>
    <data name="Latest_assessment" xml:space="preserve">
      <value>Latest Assessment</value>
    </data>
    <data name="Supplier_profile" xml:space="preserve">
      <value>Supplier Profile</value>
    </data>
    <data name="Main_Contact" xml:space="preserve">
      <value>Main Contact</value>
    </data>
    <data name="Main_Contact_Email" xml:space="preserve">
      <value>Main Contact Email</value>
    </data>
    <data name="Landline" xml:space="preserve">
      <value>Landline</value>
    </data>
    <data name="Mobile" xml:space="preserve">
      <value>Mobile</value>
    </data>
    <data name="Basic_Information" xml:space="preserve">
      <value>Basic Information</value>
    </data>
    <data name="Required_Forms" xml:space="preserve">
      <value>Required forms</value>
    </data>
    <data name="Service_Name" xml:space="preserve">
      <value>Service Name</value>
    </data>
    <data name="Supplier_Services" xml:space="preserve">
      <value>Supplier Services</value>
    </data>
    <data name="Supplier_Products" xml:space="preserve">
      <value>Supplier Products</value>
    </data>
    <data name="Version_Number" xml:space="preserve">
      <value>Version Number</value>
    </data>
    <data name="Add_New_Product" xml:space="preserve">
      <value>Add New Product</value>
    </data>
    <data name="Please_select_products_for_supplier" xml:space="preserve">
      <value>Please select products that the Supplier has added</value>
    </data>
    <data name="Change_Me" xml:space="preserve">
      <value>Change Me...</value>
    </data>
    <data name="Supplier_provided_service_not_found" xml:space="preserve">
      <value>Supplier Service Not Found</value>
    </data>
    <data name="Required_Files" xml:space="preserve">
      <value>Required Files</value>
    </data>
    <data name="Basic_Information_Supplementary_Text" xml:space="preserve">
      <value>Enter supplier information</value>
    </data>
    <data name="Required_Forms_Supplementary_Text" xml:space="preserve">
      <value>Select assessment forms to be completed by Supplier</value>
    </data>
    <data name="Required_Files_Supplementary_Text" xml:space="preserve">
      <value>Enter additional documents you would want the Supplier to provide as part of their submission. Click the 'Add File' button or press Enter to ensure they are added.</value>
    </data>
    <data name="Renewal_Date" xml:space="preserve">
      <value>Renewal Date</value>
    </data>
    <data name="Contract_Start" xml:space="preserve">
      <value>Contract Start</value>
    </data>
    <data name="Completed_Date" xml:space="preserve">
      <value>Completed Date</value>
    </data>
    <data name="Submission_Date" xml:space="preserve">
      <value>Submission Date</value>
    </data>
    <data name="New_Request" xml:space="preserve">
      <value>New Request</value>
    </data>
    <data name="Low_Criticality_Info" xml:space="preserve">
      <value>Low: No direct access to critical data. Not mission critical (5dys or more)</value>
    </data>
    <data name="Medium_Criticality_Info" xml:space="preserve">
      <value>Medium: Access to critical data. Partially mission critical (24hrs - 5dys)</value>
    </data>
    <data name="High_Criticality_Info" xml:space="preserve">
      <value>High: Access to &amp; storage of critical data. Mission critical (1min - 24hrs)</value>
    </data>
    <data name="Unchecking_Form_Warning_Info" xml:space="preserve">
      <value>Unselected forms will no longer be available for use in your quote request</value>
    </data>
    <data name="Services" xml:space="preserve">
      <value>Services</value>
    </data>
    <data name="Criticality" xml:space="preserve">
      <value>Criticality</value>
    </data>
    <data name="Add_File" xml:space="preserve">
      <value>Add File</value>
    </data>
    <data name="Both" xml:space="preserve">
      <value>Both</value>
    </data>
    <data name="Request_new_assessment" xml:space="preserve">
      <value>New Assessment</value>
    </data>
    <data name="Remove_Association_Confirmation_Question" xml:space="preserve">
      <value>Are you sure you want to remove an association with this supplier? The supplier will be removed from all unsubmitted submissions.</value>
    </data>
    <data name="Tooltip_remove_supplier" xml:space="preserve">
      <value>Remove supplier</value>
    </data>
    <data name="Tooltip_open_supplier_page" xml:space="preserve">
      <value>Open supplier</value>
    </data>
    <data name="Number_of_Contracts" xml:space="preserve">
      <value>Number of Contracts</value>
    </data>
    <data name="Incomplete_Requests" xml:space="preserve">
      <value>Incomplete requests</value>
    </data>
    <data name="Most_recent_assessment_to_client" xml:space="preserve">
      <value>Most recent assessments to client</value>
    </data>
    <data name="Most_recently_modified_assessments" xml:space="preserve">
      <value>Most recently modified assessments</value>
    </data>
    <data name="Expiring_assessments" xml:space="preserve">
      <value>Expiring Assessments</value>
    </data>
    <data name="Expired_assessments" xml:space="preserve">
      <value>Expired Assessments</value>
    </data>
    <data name="No_codes_available" xml:space="preserve">
      <value>No codes available</value>
    </data>
    <data name="Max_file_size" xml:space="preserve">
      <value>Max file size</value>
    </data>
    <data name="Allowed_extensions" xml:space="preserve">
      <value>Allowed extensions</value>
    </data>
    <data name="Form_name" xml:space="preserve">
      <value>Form name</value>
    </data>
    <data name="QA" xml:space="preserve">
      <value>Q&amp;A</value>
    </data>
    <data name="Error" xml:space="preserve">
      <value>Error</value>
    </data>
    <data name="Unsubmitted" xml:space="preserve">
      <value>Unsubmitted</value>
    </data>
    <data name="Quote_Request_Request_Fill_Info" xml:space="preserve">
      <value>You can complete application form(s) on behalf of the organisation before sending requests.</value>
    </data>
    <data name="Snapshot_cannot_be_null" xml:space="preserve">
      <value>Snapshot cannot be null</value>
    </data>
    <data name="Sent" xml:space="preserve">
      <value>Sent</value>
    </data>
    <data name="Unsent" xml:space="preserve">
      <value>Unsent</value>
    </data>
    <data name="Submission_Status" xml:space="preserve">
      <value>Submission Status</value>
    </data>
    <data name="Assessment_Status" xml:space="preserve">
      <value>Assessment Status</value>
    </data>
    <data name="Awaiting_completion_by_supplier" xml:space="preserve">
      <value>Awaiting completion by supplier</value>
    </data>
    <data name="Request_does_not_contain_any_form_s__or_file_s_" xml:space="preserve">
      <value>Request does not contain any form(s) or file(s)</value>
    </data>
    <data name="Send_a_change_password_email" xml:space="preserve">
      <value>Send Change Password Email</value>
    </data>
    <data name="Open_Supplier" xml:space="preserve">
      <value>Open Supplier</value>
    </data>
    <data name="Authorising" xml:space="preserve">
      <value>Authorising...</value>
    </data>
    <data name="Signing_in" xml:space="preserve">
      <value>Signing in...</value>
    </data>
    <data name="Completing_sign-in" xml:space="preserve">
      <value>Completing sign-in...</value>
    </data>
    <data name="Sign-in_failed" xml:space="preserve">
      <value>Sign-in failed</value>
    </data>
    <data name="Sign-out_failed" xml:space="preserve">
      <value>Sign-out failed</value>
    </data>
    <data name="Sign-out_succeeded" xml:space="preserve">
      <value>Sign-out succeeded</value>
    </data>
    <data name="Completing_sign-out" xml:space="preserve">
      <value>Completing sign-out...</value>
    </data>
    <data name="Signing_out" xml:space="preserve">
      <value>Signing out...</value>
    </data>
    <data name="Forms_and_files" xml:space="preserve">
      <value>Forms &amp; Files</value>
    </data>
    <data name="Products" xml:space="preserve">
      <value>Products</value>
    </data>
    <data name="Product_name" xml:space="preserve">
      <value>Product Name</value>
    </data>
    <data name="Please_provide_product_name" xml:space="preserve">
      <value>Please provide a product name</value>
    </data>
    <data name="Please_provide_verison_number" xml:space="preserve">
      <value>Please provide a version or model number</value>
    </data>
    <data name="Version_Model" xml:space="preserve">
      <value>Version/Model</value>
    </data>
    <data name="Complete_current_changes" xml:space="preserve">
      <value>Complete your current changes before continuing</value>
    </data>
    <data name="Edit_product" xml:space="preserve">
      <value>Edit Product</value>
    </data>
    <data name="Remove_product" xml:space="preserve">
      <value>Remove Product</value>
    </data>
    <data name="Product_in_use" xml:space="preserve">
      <value>The product is in use and cannot be modified</value>
    </data>
    <data name="Product_name_and_version_in_use" xml:space="preserve">
      <value>The Product name and version is already in use</value>
    </data>
    <data name="Save_product" xml:space="preserve">
      <value>Save product</value>
    </data>
    <data name="My_Products" xml:space="preserve">
      <value>My Products</value>
    </data>
    <data name="You_cannot_modify_a_product_you_did_not_add" xml:space="preserve">
      <value>You cannot modify a product a Supplier has added</value>
    </data>
    <data name="Enable_or_disable_product" xml:space="preserve">
      <value>Enable or disable a product from appearing your Submissions</value>
    </data>
    <data name="Synchronise" xml:space="preserve">
      <value>Synchronise</value>
    </data>
    <data name="Manage_Products" xml:space="preserve">
      <value>Manage Products</value>
    </data>
    <data name="An_unexpected_exception_occured__Please_refresh_the_page_or_contact_support_if_the_problem_persists" xml:space="preserve">
      <value>An unexpected exception occured. Please refresh the page or contact support if the problem persists</value>
    </data>
    <data name="Exception_result_default_message" xml:space="preserve">
      <value>Error while getting data</value>
    </data>
    <data name="Oops_something_bad_happened" xml:space="preserve">
      <value>Oops... something bad happened</value>
    </data>
    <data name="Refresh_page_or_contact_support" xml:space="preserve">
      <value>Please refresh the page or contact support if the problem persists</value>
    </data>
    <data name="Show_inactive" xml:space="preserve">
      <value>Show Inactive</value>
    </data>
    <data name="Can_t_create_a_new_assessment_because_the_supplier_association_is_inactive" xml:space="preserve">
      <value>Can't create a new assessment because the supplier association is inactive</value>
    </data>
    <data name="Reactivate_Supplier" xml:space="preserve">
      <value>Reactivate Supplier</value>
    </data>
    <data name="Reactivate" xml:space="preserve">
      <value>Reactivate</value>
    </data>
    <data name="Deactivate" xml:space="preserve">
      <value>Deactivate</value>
    </data>
    <data name="Deactivate_Supplier" xml:space="preserve">
      <value>Deactivate Supplier</value>
    </data>
    <data name="Registered" xml:space="preserve">
      <value>Registered</value>
    </data>
    <data name="Not_Registered" xml:space="preserve">
      <value>Not Registered</value>
    </data>
    <data name="Registered_with_No_Assessment" xml:space="preserve">
      <value>Registered with No Assessment</value>
    </data>
    <data name="Registered_with_Assessment" xml:space="preserve">
      <value>Registered with Assessment</value>
    </data>
    <data name="Confirm_removing_last_underwriter" xml:space="preserve">
      <value>Confirm Removing Last Underwriter</value>
    </data>
    <data name="Are_you_sure_you_want_to_remove_last_underwriter" xml:space="preserve">
      <value>Are you sure you want to remove the last Underwriter? Doing so will lose you access to this Quote Request and you'll have to ask the Broker to re-add you</value>
    </data>
    <data name="Void_supplier_submission_confirmation_question" xml:space="preserve">
      <value>Are you sure you want to void this supplier assessment?</value>
    </data>
    <data name="Policy_Intents" xml:space="preserve">
      <value>Policy Intents</value>
    </data>
    <data name="Policy_Wording" xml:space="preserve">
      <value>Policy Wording</value>
    </data>
    <data name="Quote" xml:space="preserve">
      <value>Quote</value>
    </data>
    <data name="Quote_Valid_Days" xml:space="preserve">
      <value>Quote Valid Days</value>
    </data>
    <data name="Policy_Period" xml:space="preserve">
      <value>Policy Period</value>
    </data>
    <data name="Inclusive_note_for_quote" xml:space="preserve">
      <value>Both days inclusive at 12:01am LST (Local Standard Time) at the main address of the insured</value>
    </data>
    <data name="Retroactive_Date" xml:space="preserve">
      <value>Retroactive Date</value>
    </data>
    <data name="Choice_of_Law_And_Jurisdiction" xml:space="preserve">
      <value>Choice of Law and Jurisdiction</value>
    </data>
    <data name="Incident_Response_Panel" xml:space="preserve">
      <value>Incident Response Panel</value>
    </data>
    <data name="Incident_and_Claim_Notifications" xml:space="preserve">
      <value>Incident and Claims Notifications</value>
    </data>
    <data name="Hello_World" xml:space="preserve">
      <value>Hello World</value>
    </data>
    <data name="Insured" xml:space="preserve">
      <value>Insured</value>
    </data>
    <data name="Create_Quote" xml:space="preserve">
      <value>Create Quote</value>
    </data>
    <data name="Please_make_sure_all_options_are_added_to_choice_array" xml:space="preserve">
      <value>Please make sure all options are added to choice array</value>
    </data>
    <data name="Policy_forms" xml:space="preserve">
      <value>Policy forms</value>
    </data>
    <data name="Add_Policy_Form" xml:space="preserve">
      <value>Add Policy Form</value>
    </data>
    <data name="Add_Head_of_Cover" xml:space="preserve">
      <value>Add Head of Cover</value>
    </data>
    <data name="Policy_form_name_is_required" xml:space="preserve">
      <value>Policy form name is required</value>
    </data>
    <data name="Heads_of_cover_name_is_required" xml:space="preserve">
      <value>Heads of cover name is required</value>
    </data>
    <data name="Edit_Policy_Form" xml:space="preserve">
      <value>Edit Policy Form</value>
    </data>
    <data name="Add_Option" xml:space="preserve">
      <value>Add Option</value>
    </data>
    <data name="Limits_of_liability" xml:space="preserve">
      <value>Limits of Liability</value>
    </data>
    <data name="Retention_waiting_period" xml:space="preserve">
      <value>Retention/Waiting Period (Any one claim)</value>
    </data>
    <data name="Covered" xml:space="preserve">
      <value>Covered</value>
    </data>
    <data name="Line" xml:space="preserve">
      <value>Line</value>
    </data>
    <data name="Premium" xml:space="preserve">
      <value>Premium</value>
    </data>
    <data name="Brokerage_Percentage" xml:space="preserve">
      <value>Brokerage (percentage)</value>
    </data>
    <data name="Policy_aggregate_limit" xml:space="preserve">
      <value>Policy Aggregate Limit of Liability</value>
    </data>
    <data name="Form_validation_errors" xml:space="preserve">
      <value>Form validation errors</value>
    </data>
    <data name="Layer" xml:space="preserve">
      <value>Layer</value>
    </data>
    <data name="Limit" xml:space="preserve">
      <value>Limit</value>
    </data>
    <data name="Excess" xml:space="preserve">
      <value>Excess</value>
    </data>
    <data name="Target_Premium" xml:space="preserve">
      <value>Target Premium</value>
    </data>
    <data name="Show_Underlying" xml:space="preserve">
      <value>Show Underlying</value>
    </data>
    <data name="Quote_Request" xml:space="preserve">
      <value>Quote Request</value>
    </data>
    <data name="Insuring_Agreements" xml:space="preserve">
      <value>Insuring Agreements</value>
    </data>
    <data name="Extensions" xml:space="preserve">
      <value>Extensions</value>
    </data>
    <data name="Policy_Start" xml:space="preserve">
      <value>Policy Start</value>
    </data>
    <data name="Policy_End" xml:space="preserve">
      <value>Policy End</value>
    </data>
    <data name="Send_to_broker" xml:space="preserve">
      <value>Send to Broker</value>
    </data>
    <data name="Text_cannot_be_just_spaces" xml:space="preserve">
      <value>Text cannot be just spaces</value>
    </data>
    <data name="Please_provide_a_value_greater_than_zero" xml:space="preserve">
      <value>Please provide a value greater than 0</value>
    </data>
    <data name="End_date_cannot_be_before_start_date" xml:space="preserve">
      <value>End date cannot be before start date</value>
    </data>
    <data name="Start_date_cannot_be_after_start_date" xml:space="preserve">
      <value>Start date cannot be after end date</value>
    </data>
    <data name="Cannot_find_file" xml:space="preserve">
      <value>Cannot find file</value>
    </data>
    <data name="Are_you_sure_you_want_to_send_the_quote" xml:space="preserve">
      <value>Are you sure you want to send the quote to the Broker?</value>
    </data>
    <data name="Add_layer" xml:space="preserve">
      <value>Add Layer</value>
    </data>
    <data name="Currency" xml:space="preserve">
      <value>Currency</value>
    </data>
    <data name="Layers" xml:space="preserve">
      <value>Layers</value>
    </data>
    <data name="Program_Stack" xml:space="preserve">
      <value>Program Stack</value>
    </data>
    <data name="No_quote_requests_available" xml:space="preserve">
      <value>Add a Quote Request to send to Brokers and Underwriters</value>
    </data>
    <data name="Quote_Requests" xml:space="preserve">
      <value>Quote Requests</value>
    </data>
    <data name="Awaiting_Response" xml:space="preserve">
      <value>Awaiting response</value>
    </data>
    <data name="Indicated" xml:space="preserve">
      <value>Indicated</value>
    </data>
    <data name="Cancelled_Request" xml:space="preserve">
      <value>Declined by broker</value>
    </data>
    <data name="Declined_By_Underwriter" xml:space="preserve">
      <value>Declined by underwriter</value>
    </data>
    <data name="Declined_By_Organisation" xml:space="preserve">
      <value>Declined by organisation</value>
    </data>
    <data name="Line_Size" xml:space="preserve">
      <value>Line size</value>
    </data>
    <data name="Quotes" xml:space="preserve">
      <value>Quotes</value>
    </data>
    <data name="Primary" xml:space="preserve">
      <value>Primary</value>
    </data>
    <data name="Excess_of" xml:space="preserve">
      <value>excess of</value>
    </data>
    <data name="Quote_policy_wording_not_selected_info" xml:space="preserve">
      <value>Select a Policy Wording to start your quote</value>
    </data>
    <data name="Add_an_option_to_your_quote" xml:space="preserve">
      <value>Add an option to your quote</value>
    </data>
    <data name="Policy_wording_not_selected" xml:space="preserve">
      <value>Policy wording not selected</value>
    </data>
    <data name="Policy_wording_change_confirmation_text" xml:space="preserve">
      <value>Are you sure you want to change the Policy Wording? Any options created will be lost</value>
    </data>
    <data name="Quote_remove_option_confirmation_text" xml:space="preserve">
      <value>Are you sure you wish to remove this option?</value>
    </data>
    <data name="Withdrawn_By_Underwriter" xml:space="preserve">
      <value>Withdrawn by underwriter</value>
    </data>
    <data name="Withdrawn_By_Broker" xml:space="preserve">
      <value>Withdrawn by broker</value>
    </data>
    <data name="Reinstate" xml:space="preserve">
      <value>Reinstate</value>
    </data>
    <data name="Reason" xml:space="preserve">
      <value>Reason</value>
    </data>
    <data name="Decline_Quote_Confirmation" xml:space="preserve">
      <value>Are you sure you want to decline this quote?</value>
    </data>
    <data name="Withdraw_Quote_Confirmation" xml:space="preserve">
      <value>Are you sure you want to withdraw this quote?</value>
    </data>
    <data name="Reinstate_Quote_Message" xml:space="preserve">
      <value>Are you sure you want to reinstate this quote? The quote will no longer be marked as declined or withdrawn.</value>
    </data>
    <data name="Withdrawn_by_broker_message" xml:space="preserve">
      <value>This quote has been withdrawn by the broker.</value>
    </data>
    <data name="Withdrawn_by_underwriter_message" xml:space="preserve">
      <value>This quote has been withdrawn by the underwriter.</value>
    </data>
    <data name="Cancelled_Request_message" xml:space="preserve">
      <value>This quote has been declined by the broker.</value>
    </data>
    <data name="Declined_By_Underwriter_message" xml:space="preserve">
      <value>This quote has been declined by the underwriter.</value>
    </data>
    <data name="Subjectivities" xml:space="preserve">
      <value>Subjectivities</value>
    </data>
    <data name="Satisfied" xml:space="preserve">
      <value>Satisfied</value>
    </data>
    <data name="Is_shared" xml:space="preserve">
      <value>Is shared</value>
    </data>
    <data name="Visible_to_the_organisation" xml:space="preserve">
      <value>Visible to the organisation</value>
    </data>
    <data name="The_layer_is_currently_visible_to_the_Organisation" xml:space="preserve">
      <value>The layer is currently visible to the organisation</value>
    </data>
    <data name="The_layer_is_currently_not_visible_to_the_Organisation" xml:space="preserve">
      <value>The layer is currently not visible to the organisation</value>
    </data>
    <data name="Hide_layer_modal_confirmation_text" xml:space="preserve">
      <value>Are you sure you wish to hide this layer from the Organisation? The Organisation will no longer be able to see the Layer or its contents. This action can be undone at anytime.</value>
    </data>
    <data name="Share_layer_modal_confirmation_text" xml:space="preserve">
      <value>Are you sure you wish to show this layer to the Organisation? The Organisation will be able to see the layer and its contents. This action can be undone at anytime.</value>
    </data>
    <data name="No_quote_requests_available_organisation" xml:space="preserve">
      <value>No quote requests available</value>
    </data>
    <data name="Not_Covered" xml:space="preserve">
      <value>Not Covered</value>
    </data>
    <data name="Broker_Email" xml:space="preserve">
      <value>Broker Email</value>
    </data>
  <data name="Select_Option" xml:space="preserve">
    <value>Select this option</value>
  </data>
  <data name="Deselect_Option" xml:space="preserve">
    <value>Deselect this option</value>
  </data>
  <data name="Selected" xml:space="preserve">
    <value>Selected</value>
  </data>
    <data name="Quote_Agreed" xml:space="preserve">
      <value>Quote Agreed</value>
    </data>
    <data name="Select_Option_Confirmation" xml:space="preserve">
      <value>Are you sure you wish to select this option? This represents an informal agreement on the cover provided and will set the status of this layer as 'Quote Agreed'. You can deselect this option at anytime, if you change your mind.</value>
    </data>
    <data name="Deselect_Option_Confirmation" xml:space="preserve">
      <value>Are you sure you wish to deselect this option? You can reselect this option at anytime, if you change your mind.</value>
    </data>
    <data name="Endorsements_And_Exclusions" xml:space="preserve">
      <value>Endorsements &amp; Exclusions</value>
    </data>
    <data name="Withdraw" xml:space="preserve">
      <value>Withdraw</value>
    </data>
    <data name="Decline" xml:space="preserve">
      <value>Decline</value>
  </data>
    <data name="Add_Quote" xml:space="preserve">
      <value>Add quote</value>
    </data>
  <data name="Agreed_Option" xml:space="preserve">
    <value>Agreed Option</value>
  </data>
  <data name="View_Option" xml:space="preserve">
    <value>View Option</value>
  </data>
    <data name="Quote_Date" xml:space="preserve">
      <value>Quote Date</value>
    </data>
  <data name="Policy_Aggregate_Limit_Of_Liability" xml:space="preserve">
    <value>Policy Aggregate Limit of Liability</value>
  </data>
  <data name="No_agreed_option_found" xml:space="preserve">
    <value>No agreed option found</value>
  </data>
  <data name="No_agreed_option_found_for_this_layer" xml:space="preserve">
    <value>No agreed option found for this layer</value>
  </data>
    <data name="Completed_Submissions" xml:space="preserve">
      <value>Completed Submissions</value>
    </data>
    <data name="Child_Submissions" xml:space="preserve">
      <value>Child Submissions</value>
    </data>
    <data name="Create_quote_request" xml:space="preserve">
      <value>Create Quote Request</value>
    </data>
    <data name="Broker_Quote_Requests" xml:space="preserve">
      <value>Wholesale Broker Quote Requests</value>
    </data>
  <data name="Undo_Cancellation" xml:space="preserve">
    <value>Undo Cancellation</value>
  </data>
    <data name="Tooltip_view_broker_quote_request" xml:space="preserve">
      <value>View Broker Quote Request</value>
    </data>
    <data name="Sub_limits_of_liability" xml:space="preserve">
      <value>Sub-limits of Liability</value>
    </data>
    <data name="Retention" xml:space="preserve">
      <value>Retention (Any one claim)</value>
    </data>
    <data name="Download_quote_sheet" xml:space="preserve">
      <value>Download Quote Sheet</value>
    </data>
    <data name="Layer_Placement" xml:space="preserve">
      <value>Layer Placement</value>
    </data>
    <data name="Placed" xml:space="preserve">
      <value>Placed</value>
    </data>
    <data name="Unplaced" xml:space="preserve">
      <value>Unplaced</value>
    </data>
  <data name="Quota_Share" xml:space="preserve">
    <value>Quota Share</value>
  </data>
  <data name="Follow_Markets" xml:space="preserve">
    <value>Follow Market(s)</value>
  </data>
  <data name="Create_Quota_Share" xml:space="preserve">
    <value>Create Quota Share</value>
  </data>
  <data name="Create_a_Quota_Share_using_this_Option" xml:space="preserve">
    <value>Create a Quota Share using this Option</value>
  </data>
  <data name="These_are_the_underwriters_that_youve_invited_to_your_quota_share" xml:space="preserve">
    <value>These are the underwriters invited to the quota share.</value>
  </data>
  <data name="Accepted_with_Amendments" xml:space="preserve">
    <value>Accepted with Amendments</value>
  </data>
  <data name="Line_Size_Orig_Currency" xml:space="preserve">
    <value>Line Size (Orig Currency)</value>
  </data>
  <data name="Premiums_Orig_Currency" xml:space="preserve">
    <value>Premiums (Orig Currency)</value>
  </data>
    <data name="Total_Placed" xml:space="preserve">
      <value>Total Placed</value>
    </data>
    <data name="Total_Premium" xml:space="preserve">
      <value>Total Premium</value>
    </data>
    <data name="Are_you_sure_you_want_to_decline_the_quote_request" xml:space="preserve">
      <value>Are you sure you want to decline this Broker Quote Request?</value>
    </data>
    <data name="Are_you_sure_you_want_to_undo_the_cancellation" xml:space="preserve">
      <value>Are you sure you want to undo the cancellation of the Broker Quote Request?</value>
    </data>
    <data name="Accepted" xml:space="preserve">
      <value>Accepted</value>
    </data>
    <data name="Declined" xml:space="preserve">
      <value>Declined</value>
    </data>
    <data name="At_least_one_underwriter_must_be_specified" xml:space="preserve">
      <value>At least one underwriter must be specified</value>
    </data>
    <data name="Quota_Share_Requests" xml:space="preserve">
      <value>Quota Share Requests</value>
    </data>
  <data name="Add_Quota_Share" xml:space="preserve">
    <value>Add Quota Share</value>
  </data>
  <data name="Max_Line_Size" xml:space="preserve">
    <value>Max Line Size</value>
  </data>
  <data name="Line_Size_is_required" xml:space="preserve">
    <value>Line Size is required</value>
  </data>
  <data name="Line_Size_must_be_greater_than_0" xml:space="preserve">
    <value>Line Size must be greater than 0</value>
  </data>
  <data name="Line_Size_exceeds_limit" xml:space="preserve">
    <value>Line Size exceeds the maximum allowed limit</value>
  </data>
    <data name="Quote_Request_Name" xml:space="preserve">
      <value>Quote Request Name</value>
    </data>
    <data name="Lead_Insurer" xml:space="preserve">
      <value>Lead Insurer</value>
    </data>
    <data name="View_Quote" xml:space="preserve">
      <value>View Quote</value>
    </data>
    <data name="Respond_to_quota_share" xml:space="preserve">
      <value>Respond to Quota Share</value>
    </data>
    <data name="Lead" xml:space="preserve">
      <value>Lead</value>
    </data>
    <data name="Follower" xml:space="preserve">
      <value>Follower</value>
    </data>
    <data name="Accept_quota_share" xml:space="preserve">
      <value>Accept Quota Share</value>
    </data>
    <data name="Decline_quota_share" xml:space="preserve">
      <value>Decline Quota Share</value>
    </data>
    <data name="Please_provide_a_reason_for_declining_this_quota_share" xml:space="preserve">
      <value>Please provide a reason for declining this quota share.</value>
    </data>
    <data name="Enter_reason_for_declining" xml:space="preserve">
      <value>Enter reason for declining...</value>
    </data>
    <data name="Enter_max_line_size" xml:space="preserve">
      <value>Enter max line size</value>
    </data>
    <data name="Existing_Subjectivities" xml:space="preserve">
      <value>Existing Subjectivities</value>
    </data>
    <data name="Is_Satisfied" xml:space="preserve">
      <value>Is Satisfied</value>
    </data>
    <data name="Add_New_Subjectivities" xml:space="preserve">
      <value>Add New Subjectivities</value>
    </data>
    <data name="Enter_subjectivity_name" xml:space="preserve">
      <value>Enter subjectivity name</value>
    </data>
    <data name="Accept" xml:space="preserve">
      <value>Accept</value>
    </data>
    <data name="Following" xml:space="preserve">
      <value>Following</value>
    </data>
    <data name="Policy_wording_is_required" xml:space="preserve">
      <value>Policy wording is required</value>
    </data>
    <data name="No_program_stack_info" xml:space="preserve">
      <value>No program stack data available for this submission.</value>
    </data>
    <data name="No_options_selected" xml:space="preserve">
      <value>No options selected</value>
    </data>
    <data name="No_follow_markets" xml:space="preserve">
      <value>No follow markets have been added yet</value>
    </data>
    <data name="Add_to_quota_share" xml:space="preserve">
      <value>Add to Quota Share</value>
    </data>
    <data name="Remove_from_quota_share" xml:space="preserve">
      <value>Remove from Quota Share</value>
    </data>
    <data name="Part" xml:space="preserve">
      <value>Part</value>
    </data>
    <data name="ILF" xml:space="preserve">
      <value>ILF</value>
    </data>
    <data name="RPM" xml:space="preserve">
      <value>RPM</value>
    </data>
    <data name="Underlying" xml:space="preserve">
      <value>Underlying</value>
    </data>
    <data name="RPM_info" xml:space="preserve">
      <value>RPM is the premium divided by the layer limit (in millions). It shows the cost of coverage per $1M insured.</value>
    </data>
    <data name="Cancel_Quote_Request" xml:space="preserve">
      <value>Cancel Quote Request</value>
    </data>
    <data name="Withdraw_Quote_Request" xml:space="preserve">
      <value>Withdraw Quote Request</value>
    </data>
    <data name="Are_you_sure_you_want_to_withdraw_this_wholesale_broker_quote_request" xml:space="preserve">
      <value>Are you sure you want to withdraw this Wholesale Broker Quote Request</value>
    </data>
    <data name="Rate_per_million" xml:space="preserve">
      <value>Rate Per Million</value>
    </data>
    <data name="Increased_limit_factor" xml:space="preserve">
      <value>Increased Limit Factor</value>
    </data>
    <data name="Reinstate_wholesale_quote" xml:space="preserve">
      <value>Are you sure you want to reinstate this Wholesale Quote Request?</value>
    </data>
    <data name="Withdrawn_by" xml:space="preserve">
      <value>Withdrawn by</value>
    </data>
    <data name="Cancelled_by" xml:space="preserve">
      <value>Cancelled by</value>
    </data>
    <data name="Unassigned" xml:space="preserve">
      <value>Unassigned</value>
    </data>
    <data name="Layer_unassigned" xml:space="preserve">
      <value>Layer unassigned</value>
    </data>
    <data name="Wholesale_Broker_Submissions" xml:space="preserve">
      <value>Wholesale Broker Submissions</value>
    </data>
    <data name="Retail_Broker_Quote_Requests" xml:space="preserve">
      <value>Retail Broker Quote Requests</value>
    </data>
    <data name="Declined_by" xml:space="preserve">
      <value>Declined by</value>
    </data>
    <data name="When_enabled_wholesale_submission" xml:space="preserve">
      <value>When enabled, the primary broker can view the full submission details. You can disable access at any time</value>
    </data>
    <data name="Broker_Quote_Request" xml:space="preserve">
      <value>Wholesale Broker Quote Request</value>
    </data>
    <data name="Wholesale_quote_request_info_text" xml:space="preserve">
      <value>This document outlines the insurance requirements as relayed by the originating broker for onward placement with underwriters. Once underwriting is complete, you can use the toggle to grant the primary broker access to the submission and allow them to continue the placement process. Please proceed with underwriting engagement based on the details below.</value>
    </data>
    <data name="Waiting_period" xml:space="preserve">
      <value>Waiting Period</value>
    </data>
    <data name="All_applicable_covers_must_have_waiting_period" xml:space="preserve">
      <value>All applicable covers must have waiting period.</value>
    </data>
    <data name="Hrs" xml:space="preserve">
      <value>hrs</value>
    </data>
    <data name="Retroactive_date_is_invalid" xml:space="preserve">
      <value>Retroactive date is invalid</value>
    </data>
    <data name="Retroactive_type" xml:space="preserve">
      <value>Retroactive type</value>
    </data>
    <data name="Date_Provided" xml:space="preserve">
      <value>Date Provided</value>
    </data>
    <data name="This_view_presents_wholesale_to_retail" xml:space="preserve">
      <value>This view presents the program put together by the Wholesale Broker to fulfil the placement requirements outlined below. It reflects the underwriting engagement carried out on your behalf.</value>
    </data>
    <data name="Retroactive_date_cannot_be_in_the_future" xml:space="preserve">
      <value>Retroactive date cannot be in the future</value>
    </data>
    <data name="Supplier_Files" xml:space="preserve">
      <value>Supplier Files</value>
    </data>
    <data name="Add_wholesale_broker_placements_to_program" xml:space="preserve">
      <value>Add wholesale broker placements to program</value>
    </data>
    <data name="Remove_wholesale_broker_placements_from_program" xml:space="preserve">
      <value>Remove wholesale broker placements from program</value>
    </data>
    <data name="Bulk_Upload_CSV" xml:space="preserve">
      <value>Bulk Upload CSV</value>
    </data>
    <data name="View_wholesale_broker_section" xml:space="preserve">
      <value>View wholesale broker section</value>
    </data>
    <data name="View_option_details" xml:space="preserve">
      <value>View option details</value>
    </data>
    <data name="Unable_to_find_wholesale_submission_for_this_option" xml:space="preserve">
      <value>Unable to find wholesale submission for this option</value>
    </data>
    <data name="Click_here_to_navigate_to_page_to_complete_action" xml:space="preserve">
      <value>You have unfinished items, click this card to navigate to the page to complete these tasks</value>
    </data>
    <data name="Wholesale_broker_section_on" xml:space="preserve">
      <value>Wholesale broker section on</value>
    </data>
    <data name="Layer_info_card_subtitle" xml:space="preserve">
      <value>This document outlines the details of the layer for which a quote has been requested. Please proceed with underwriting and provide your quote(s) in the Quotes tab.</value>
    </data>
    <data name="Line_size_limit" xml:space="preserve">
      <value>Line size limit</value>
    </data>
    <data name="Maximum_line_size_tooltip" xml:space="preserve">
      <value>Maximum line size the follow market can contribute</value>
    </data>
    <data name="No_selected_options_in_layer" xml:space="preserve">
      <value>No selected options in layer</value>
    </data>
    <data name="No_notifications_found" xml:space="preserve">
      <value>No notifications found</value>
    </data>
    <data name="Statistics" xml:space="preserve">
      <value>Statistics</value>
    </data>
    <data name="Recently_modified" xml:space="preserve">
      <value>Recently Modified</value>
    </data>
    <data name="Pending_quotes_card_subtitle" xml:space="preserve">
      <value>Showing your 25 requests with the nearest due dates across all organisations that are still awaiting a response</value>
    </data>
    <data name="Recently_modified_quotes_card_subtitle" xml:space="preserve">
      <value>Showing the 25 most recently modified requests across all organisations</value>
    </data>
    <data name="No_quotes_available" xml:space="preserve">
      <value>No quotes available</value>
    </data>
    <data name="has_sent_you" xml:space="preserve">
      <value>has sent you</value>
    </data>
    <data name="Notifications" xml:space="preserve">
      <value>Notifications</value>
    </data>
    <data name="has_submitted" xml:space="preserve">
      <value>has submitted</value>
    </data>
    <data name="Supplier_contact" xml:space="preserve">
      <value>Supplier contact</value>
    </data>
    <data name="Supplier_details" xml:space="preserve">
      <value>supplier details</value>
    </data>
    <data name="Supplier_email" xml:space="preserve">
      <value>Supplier email</value>
    </data>
    <data name="Supplier_products_subtitle" xml:space="preserve">
      <value>Products marked as `In use` will appear in your organisation's submission as part of the tools you currently use</value>
    </data>
    <data name="Not_in_use_by_your_organisation" xml:space="preserve">
      <value>Not in use by your organisation</value>
    </data>
    <data name="In_use_by_your_organisation" xml:space="preserve">
      <value>In use by your organisation</value>
    </data>
    <data name="Assessment_requests_subtitle" xml:space="preserve">
      <value>View and track all assessment requests your organisation has sent to this supplier</value>
    </data>
    <data name="Visible_to_organisation" xml:space="preserve">
      <value>Visible to Organisation</value>
    </data>
    <data name="Broker_quotes" xml:space="preserve">
      <value>Broker quotes</value>
    </data>
    <data name="Insurer_quotes" xml:space="preserve">
      <value>Insurer quotes</value>
    </data>
    <data name="Insurers_and_brokers_will_recognize_the_request_by_this_name" xml:space="preserve">
      <value>Insurers and brokers will recognize the request by this name</value>
    </data>
    <data name="Mark_all_as_read" xml:space="preserve">
      <value>Mark all as read</value>
    </data>
    <data name="Wholesale_unshareable_info" xml:space="preserve">
      <value>Please select at least one option to be able to share the submission to the primary broker</value>
    </data>
    <data name="Liability_agreement" xml:space="preserve">
      <value>Liability agreement</value>
    </data>
    <data name="Any one claim" xml:space="preserve">
      <value>Any one claim</value>
    </data>
    <data name="In_the_aggregate" xml:space="preserve">
      <value>In the aggregate</value>
    </data>
    <data name="Limit_of_liability" xml:space="preserve">
      <value>Limit of liability</value>
    </data>
    <data name="A" xml:space="preserve">
      <value>A</value>
    </data>
    <data name="Submission_page_card_header" xml:space="preserve">
      <value>is your Organisation’s central record of information used to assess, manage, or market your insurance program.</value>
    </data>
    <data name="Each_submission_contains" xml:space="preserve">
      <value>Each submission contains your</value>
    </data>
    <data name="Application_forms_comma_supporting_documents" xml:space="preserve">
      <value>application form(s), supporting documents</value>
    </data>
    <data name="Everything_needed_to_eval" xml:space="preserve">
      <value>— everything needed to evaluate your risk profile and program performance.</value>
    </data>
    <data name="Create_submissions" xml:space="preserve">
      <value>Create submissions</value>
    </data>
    <data name="For_internal_self_assessment" xml:space="preserve">
      <value>for internal self-assessment to view your scores and insights.</value>
    </data>
    <data name="Share_submission_with_a_broker" xml:space="preserve">
      <value>Share submissions with a Broker</value>
    </data>
    <data name="Have_them_market_insurance" xml:space="preserve">
      <value>to have them complete or market your insurance program.</value>
    </data>
    <data name="Existing_completed_submissions" xml:space="preserve">
      <value>existing completed submissions to reuse or refine your information.</value>
    </data>
    <data name="Use_this_page_to_manage_your_submissions" xml:space="preserve">
      <value>Use this page to manage all your submissions — whether for internal review or collaboration with a Broker. </value>
    </data>
    <data name="Submission_information" xml:space="preserve">
      <value>Submission Information</value>
    </data>
    <data name="Self_Assessment" xml:space="preserve">
      <value>Self Assessment</value>
    </data>
    <data name="Submission_Details" xml:space="preserve">
      <value>Submission Details</value>
    </data>
    <data name="This_submission_was_created_by" xml:space="preserve">
      <value>This submission was created by</value>
    </data>
    <data name="Please_review_and_complete_submission_details" xml:space="preserve">
      <value>Please review and complete all required forms, upload supporting documents, and add supplier details as needed.
Once your submission is complete, you can:
Submit to a Broker - for them to review and finalise your insurance program.
Submit for internal use - to view your scores and insights. You can also duplicate the submission later if you want to send a refined version to a Broker.</value>
    </data>
    <data name="Recipient" xml:space="preserve">
      <value>Recipient</value>
    </data>
    <data name="Complete_the_forms_below" xml:space="preserve">
      <value>Complete the forms below to provide all the necessary information for your submission. You can save your progress as you go, review your answers at any time, and submit once everything is complete.</value>
    </data>
    <data name="Upload_your_documents_here" xml:space="preserve">
      <value>Upload any supporting files related to your submission — such as certificates, reports, or other relevant documentation. These files will stay linked to this submission and can be updated or replaced at any time.</value>
    </data>
    <data name="Add_your_suppliers_here" xml:space="preserve">
      <value>Add your suppliers here. This information helps provide a complete picture of your operations and can be useful for both internal analysis and external review by a Broker. Suppliers can be managed in the</value>
    </data>
    <data name="Suppliers_page" xml:space="preserve">
      <value>Suppliers Page.</value>
    </data>
    <data name="Your_submission" xml:space="preserve">
      <value>Your submission</value>
    </data>
    <data name="Please_review_and_complete_all_required_data" xml:space="preserve">
      <value>Please review and complete all required forms, upload supporting documents and add supplier details as needed.</value>
    </data>
    <data name="Once_submission_is_complete" xml:space="preserve">
      <value>Once your submission is complete, you can</value>
    </data>
    <data name="Submit_to_a_broker" xml:space="preserve">
      <value>Submit to a broker - for them to review and finalise your insurance program.</value>
    </data>
    <data name="Submit_for_internal_use" xml:space="preserve">
      <value>Submit for internal use - to view your scores and insights</value>
    </data>
    <data name="You_can_also_duplicate_this_submission" xml:space="preserve">
      <value>You can also duplicate the submission later if you want to send a refined version to a broker.</value>
    </data>
    <data name="Please_make_sure_your_Organisation_profile_is_up_to_date" xml:space="preserve">
      <value>Please make sure your organisation's profile is up-to-date before signing off on your submission.</value>
    </data>
    <data name="Use" xml:space="preserve">
      <value>Use</value>
    </data>
    <data name="Self_assessments" xml:space="preserve">
      <value>Self-assessments</value>
    </data>
    <data name="to_understand_risk_profile" xml:space="preserve">
      <value>to understand your risk profile or program readiness. Complete the forms, upload any relevant documents, and select the recipient. You can also submit your forms for analysis as part of an</value>
    </data>
    <data name="to_view_your_results_before_a_broker" xml:space="preserve">
      <value>to review your results before deciding whether to send it to a Broker.</value>
    </data>
    <data name="Primary_Lead" xml:space="preserve">
      <value>Primary Lead</value>
    </data>
</root>