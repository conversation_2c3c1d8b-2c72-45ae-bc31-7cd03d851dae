//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Theia.FrontendResources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Theia.FrontendResources.Resource", typeof(Resource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A.
        /// </summary>
        public static string A {
            get {
                return ResourceManager.GetString("A", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abbreviated Day Names.
        /// </summary>
        public static string Abbreviated_Day_Names {
            get {
                return ResourceManager.GetString("Abbreviated_Day_Names", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abbreviated Month Genitive Names.
        /// </summary>
        public static string Abbreviated_Month_Genitive_Names {
            get {
                return ResourceManager.GetString("Abbreviated_Month_Genitive_Names", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abbreviated Month Names.
        /// </summary>
        public static string Abbreviated_Month_Names {
            get {
                return ResourceManager.GetString("Abbreviated_Month_Names", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accept.
        /// </summary>
        public static string Accept {
            get {
                return ResourceManager.GetString("Accept", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accept Quota Share.
        /// </summary>
        public static string Accept_quota_share {
            get {
                return ResourceManager.GetString("Accept_quota_share", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accepted.
        /// </summary>
        public static string Accepted {
            get {
                return ResourceManager.GetString("Accepted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accepted with Amendments.
        /// </summary>
        public static string Accepted_with_Amendments {
            get {
                return ResourceManager.GetString("Accepted_with_Amendments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access.
        /// </summary>
        public static string Access {
            get {
                return ResourceManager.GetString("Access", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access House.
        /// </summary>
        public static string Access_House {
            get {
                return ResourceManager.GetString("Access_House", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access Org.
        /// </summary>
        public static string Access_Org {
            get {
                return ResourceManager.GetString("Access_Org", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access Token Timespan.
        /// </summary>
        public static string Access_Token_TimeSpan {
            get {
                return ResourceManager.GetString("Access_Token_TimeSpan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account.
        /// </summary>
        public static string Account {
            get {
                return ResourceManager.GetString("Account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registration Successful.
        /// </summary>
        public static string Account_Creation_Successful {
            get {
                return ResourceManager.GetString("Account_Creation_Successful", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your account was successfully created, look out for an email asking you to change your password, before logging in.
        /// </summary>
        public static string Account_Creation_Successful_Details {
            get {
                return ResourceManager.GetString("Account_Creation_Successful_Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Actions.
        /// </summary>
        public static string Actions {
            get {
                return ResourceManager.GetString("Actions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activated.
        /// </summary>
        public static string Activated {
            get {
                return ResourceManager.GetString("Activated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active.
        /// </summary>
        public static string Active {
            get {
                return ResourceManager.GetString("Active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active Broker.
        /// </summary>
        public static string Active_Broker {
            get {
                return ResourceManager.GetString("Active_Broker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activity.
        /// </summary>
        public static string Activity {
            get {
                return ResourceManager.GetString("Activity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        public static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add a message....
        /// </summary>
        public static string Add_a_message {
            get {
                return ResourceManager.GetString("Add_a_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add a question....
        /// </summary>
        public static string Add_a_question {
            get {
                return ResourceManager.GetString("Add_a_question", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Activity.
        /// </summary>
        public static string Add_Activity {
            get {
                return ResourceManager.GetString("Add_Activity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add an option to your quote.
        /// </summary>
        public static string Add_an_option_to_your_quote {
            get {
                return ResourceManager.GetString("Add_an_option_to_your_quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Applicant.
        /// </summary>
        public static string Add_Applicant {
            get {
                return ResourceManager.GetString("Add_Applicant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Application Form.
        /// </summary>
        public static string Add_ApplicationForm {
            get {
                return ResourceManager.GetString("Add_ApplicationForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Association.
        /// </summary>
        public static string Add_Association {
            get {
                return ResourceManager.GetString("Add_Association", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Authenticator App.
        /// </summary>
        public static string Add_Authenticator_App {
            get {
                return ResourceManager.GetString("Add_Authenticator_App", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Brokers.
        /// </summary>
        public static string Add_Brokers {
            get {
                return ResourceManager.GetString("Add_Brokers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Broking House.
        /// </summary>
        public static string Add_Broking_House {
            get {
                return ResourceManager.GetString("Add_Broking_House", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Broking House Employee.
        /// </summary>
        public static string Add_Broking_House_Employee {
            get {
                return ResourceManager.GetString("Add_Broking_House_Employee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Contract.
        /// </summary>
        public static string Add_Contract {
            get {
                return ResourceManager.GetString("Add_Contract", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Country.
        /// </summary>
        public static string Add_Country {
            get {
                return ResourceManager.GetString("Add_Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add empty supplier.
        /// </summary>
        public static string Add_empty_supplier {
            get {
                return ResourceManager.GetString("Add_empty_supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add File.
        /// </summary>
        public static string Add_File {
            get {
                return ResourceManager.GetString("Add_File", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Head of Cover.
        /// </summary>
        public static string Add_Head_of_Cover {
            get {
                return ResourceManager.GetString("Add_Head_of_Cover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Industry.
        /// </summary>
        public static string Add_Industry {
            get {
                return ResourceManager.GetString("Add_Industry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Insurer.
        /// </summary>
        public static string Add_Insurer {
            get {
                return ResourceManager.GetString("Add_Insurer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Layer.
        /// </summary>
        public static string Add_layer {
            get {
                return ResourceManager.GetString("Add_layer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Location.
        /// </summary>
        public static string Add_Location {
            get {
                return ResourceManager.GetString("Add_Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Loss Type.
        /// </summary>
        public static string Add_Loss_Type {
            get {
                return ResourceManager.GetString("Add_Loss_Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Message.
        /// </summary>
        public static string Add_Message {
            get {
                return ResourceManager.GetString("Add_Message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New File.
        /// </summary>
        public static string Add_New_File {
            get {
                return ResourceManager.GetString("Add_New_File", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Product.
        /// </summary>
        public static string Add_New_Product {
            get {
                return ResourceManager.GetString("Add_New_Product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Subjectivities.
        /// </summary>
        public static string Add_New_Subjectivities {
            get {
                return ResourceManager.GetString("Add_New_Subjectivities", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Option.
        /// </summary>
        public static string Add_Option {
            get {
                return ResourceManager.GetString("Add_Option", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Org.
        /// </summary>
        public static string Add_Org {
            get {
                return ResourceManager.GetString("Add_Org", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Organisation.
        /// </summary>
        public static string Add_Organisation {
            get {
                return ResourceManager.GetString("Add_Organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Organisation Request.
        /// </summary>
        public static string Add_Organisation_Request {
            get {
                return ResourceManager.GetString("Add_Organisation_Request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Policy Form.
        /// </summary>
        public static string Add_Policy_Form {
            get {
                return ResourceManager.GetString("Add_Policy_Form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Question.
        /// </summary>
        public static string Add_Question {
            get {
                return ResourceManager.GetString("Add_Question", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Quota Share.
        /// </summary>
        public static string Add_Quota_Share {
            get {
                return ResourceManager.GetString("Add_Quota_Share", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add quote.
        /// </summary>
        public static string Add_Quote {
            get {
                return ResourceManager.GetString("Add_Quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Reference.
        /// </summary>
        public static string Add_Reference {
            get {
                return ResourceManager.GetString("Add_Reference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Region.
        /// </summary>
        public static string Add_Region {
            get {
                return ResourceManager.GetString("Add_Region", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Role.
        /// </summary>
        public static string Add_Role {
            get {
                return ResourceManager.GetString("Add_Role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Roles.
        /// </summary>
        public static string Add_Roles {
            get {
                return ResourceManager.GetString("Add_Roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Selected Forms.
        /// </summary>
        public static string Add_Selected_Forms {
            get {
                return ResourceManager.GetString("Add_Selected_Forms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Selected Roles.
        /// </summary>
        public static string Add_Selected_Roles {
            get {
                return ResourceManager.GetString("Add_Selected_Roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Supplier.
        /// </summary>
        public static string Add_Supplier {
            get {
                return ResourceManager.GetString("Add_Supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Suppliers.
        /// </summary>
        public static string Add_Suppliers {
            get {
                return ResourceManager.GetString("Add_Suppliers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Tenant.
        /// </summary>
        public static string Add_Tenant {
            get {
                return ResourceManager.GetString("Add_Tenant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add to Quota Share.
        /// </summary>
        public static string Add_to_quota_share {
            get {
                return ResourceManager.GetString("Add_to_quota_share", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Transcripts.
        /// </summary>
        public static string Add_transcripts {
            get {
                return ResourceManager.GetString("Add_transcripts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Underwriters.
        /// </summary>
        public static string Add_Underwriters {
            get {
                return ResourceManager.GetString("Add_Underwriters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add User.
        /// </summary>
        public static string Add_User {
            get {
                return ResourceManager.GetString("Add_User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Version.
        /// </summary>
        public static string Add_Version {
            get {
                return ResourceManager.GetString("Add_Version", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add wholesale broker placements to program.
        /// </summary>
        public static string Add_wholesale_broker_placements_to_program {
            get {
                return ResourceManager.GetString("Add_wholesale_broker_placements_to_program", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add your suppliers here. This information helps provide a complete picture of your operations and can be useful for both internal analysis and external review by a Broker. Suppliers can be managed in the.
        /// </summary>
        public static string Add_your_suppliers_here {
            get {
                return ResourceManager.GetString("Add_your_suppliers_here", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Additional comments for TheiaLens onboarding team (not visible to client).
        /// </summary>
        public static string Additional_comments_for_onboarding {
            get {
                return ResourceManager.GetString("Additional_comments_for_onboarding", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Additional Info.
        /// </summary>
        public static string Additional_Info {
            get {
                return ResourceManager.GetString("Additional_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Admin Email.
        /// </summary>
        public static string Admin_Email {
            get {
                return ResourceManager.GetString("Admin_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Admin Full Name.
        /// </summary>
        public static string Admin_Full_Name {
            get {
                return ResourceManager.GetString("Admin_Full_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Admin Name.
        /// </summary>
        public static string Admin_Name {
            get {
                return ResourceManager.GetString("Admin_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agreed Option.
        /// </summary>
        public static string Agreed_Option {
            get {
                return ResourceManager.GetString("Agreed_Option", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All.
        /// </summary>
        public static string All {
            get {
                return ResourceManager.GetString("All", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All applicable covers must have waiting period..
        /// </summary>
        public static string All_applicable_covers_must_have_waiting_period {
            get {
                return ResourceManager.GetString("All_applicable_covers_must_have_waiting_period", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Forms.
        /// </summary>
        public static string All_Forms {
            get {
                return ResourceManager.GetString("All_Forms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Forms.
        /// </summary>
        public static string AllForms {
            get {
                return ResourceManager.GetString("AllForms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allowed extensions.
        /// </summary>
        public static string Allowed_extensions {
            get {
                return ResourceManager.GetString("Allowed_extensions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allowed for new users.
        /// </summary>
        public static string Allowed_for_new_users {
            get {
                return ResourceManager.GetString("Allowed_for_new_users", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allowed username characters.
        /// </summary>
        public static string Allowed_username_characters {
            get {
                return ResourceManager.GetString("Allowed_username_characters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Already have an account?.
        /// </summary>
        public static string Already_have_an_account {
            get {
                return ResourceManager.GetString("Already_have_an_account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AM Designator.
        /// </summary>
        public static string AM_Designator {
            get {
                return ResourceManager.GetString("AM_Designator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occured.
        /// </summary>
        public static string An_error_occured {
            get {
                return ResourceManager.GetString("An_error_occured", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An unexpected exception occured. Please refresh the page or contact support if the problem persists.
        /// </summary>
        public static string An_unexpected_exception_occured__Please_refresh_the_page_or_contact_support_if_the_problem_persists {
            get {
                return ResourceManager.GetString("An_unexpected_exception_occured__Please_refresh_the_page_or_contact_support_if_th" +
                        "e_problem_persists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Analyse.
        /// </summary>
        public static string Analyse {
            get {
                return ResourceManager.GetString("Analyse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Analysing....
        /// </summary>
        public static string Analysing {
            get {
                return ResourceManager.GetString("Analysing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to and.
        /// </summary>
        public static string and {
            get {
                return ResourceManager.GetString("and", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Answer.
        /// </summary>
        public static string Answer {
            get {
                return ResourceManager.GetString("Answer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Answer Application Form.
        /// </summary>
        public static string Answer_Application_Form {
            get {
                return ResourceManager.GetString("Answer_Application_Form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Any one claim.
        /// </summary>
        public static string Any_one_claim {
            get {
                return ResourceManager.GetString("Any one claim", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to App Info.
        /// </summary>
        public static string App_Info {
            get {
                return ResourceManager.GetString("App_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applicant.
        /// </summary>
        public static string Applicant {
            get {
                return ResourceManager.GetString("Applicant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Applicants.
        /// </summary>
        public static string Applicants {
            get {
                return ResourceManager.GetString("Applicants", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Form.
        /// </summary>
        public static string Application_Form {
            get {
                return ResourceManager.GetString("Application_Form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application forms cannot be null.
        /// </summary>
        public static string Application_form_cannot_be_null {
            get {
                return ResourceManager.GetString("Application_form_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code.
        /// </summary>
        public static string Application_Form_Code {
            get {
                return ResourceManager.GetString("Application_Form_Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completed Date.
        /// </summary>
        public static string Application_Form_Completed_Date {
            get {
                return ResourceManager.GetString("Application_Form_Completed_Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completed On.
        /// </summary>
        public static string Application_Form_Completed_On {
            get {
                return ResourceManager.GetString("Application_Form_Completed_On", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Form Details.
        /// </summary>
        public static string Application_Form_Details {
            get {
                return ResourceManager.GetString("Application_Form_Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The application form was removed before submitting.
        /// </summary>
        public static string Application_form_has_been_removed {
            get {
                return ResourceManager.GetString("Application_form_has_been_removed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Modified On.
        /// </summary>
        public static string Application_Form_Modified_On {
            get {
                return ResourceManager.GetString("Application_Form_Modified_On", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Form Name.
        /// </summary>
        public static string Application_Form_Name {
            get {
                return ResourceManager.GetString("Application_Form_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Origin.
        /// </summary>
        public static string Application_Form_Origin {
            get {
                return ResourceManager.GetString("Application_Form_Origin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Form Progress.
        /// </summary>
        public static string Application_Form_Progress {
            get {
                return ResourceManager.GetString("Application_Form_Progress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        public static string Application_Form_Status {
            get {
                return ResourceManager.GetString("Application_Form_Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to proceed? Confirming your intention to submit will mean the form will no longer be editable by anyone and you will only be able to view it.
        /// </summary>
        public static string Application_form_submission {
            get {
                return ResourceManager.GetString("Application_form_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to proceed? Confirming your intention to submit will mean the form is selectable by any and all organisations you work with, you can still edit and save changes.
        /// </summary>
        public static string Application_form_submission_supplier {
            get {
                return ResourceManager.GetString("Application_form_submission_supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Form Type.
        /// </summary>
        public static string Application_Form_Type {
            get {
                return ResourceManager.GetString("Application_Form_Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Forms.
        /// </summary>
        public static string Application_Forms {
            get {
                return ResourceManager.GetString("Application_Forms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to application form(s), supporting documents.
        /// </summary>
        public static string Application_forms_comma_supporting_documents {
            get {
                return ResourceManager.GetString("Application_forms_comma_supporting_documents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Forms.
        /// </summary>
        public static string ApplicationForms {
            get {
                return ResourceManager.GetString("ApplicationForms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply for Military.
        /// </summary>
        public static string Apply_for_Military {
            get {
                return ResourceManager.GetString("Apply_for_Military", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approve.
        /// </summary>
        public static string Approve {
            get {
                return ResourceManager.GetString("Approve", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved Date.
        /// </summary>
        public static string Approved_Date {
            get {
                return ResourceManager.GetString("Approved_Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approximate Number of Personal Records Stored in a Single Db.
        /// </summary>
        public static string Approx_Number_of_Personal_Records_Stored_In_Db {
            get {
                return ResourceManager.GetString("Approx_Number_of_Personal_Records_Stored_In_Db", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approx. Total IT Hardware Value.
        /// </summary>
        public static string Approx_Total_IT_Hardware_Value {
            get {
                return ResourceManager.GetString("Approx_Total_IT_Hardware_Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approx. Total OT Hardware Value.
        /// </summary>
        public static string Approx_Total_OT_Hardware_Value {
            get {
                return ResourceManager.GetString("Approx_Total_OT_Hardware_Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approximate Number of Contracts.
        /// </summary>
        public static string Approximate_Number_of_Contracts {
            get {
                return ResourceManager.GetString("Approximate_Number_of_Contracts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to April.
        /// </summary>
        public static string April {
            get {
                return ResourceManager.GetString("April", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to change the settings?.
        /// </summary>
        public static string Are_you_sure_you_want_to_change_the_settings {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_change_the_settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to decline this Broker Quote Request?.
        /// </summary>
        public static string Are_you_sure_you_want_to_decline_the_quote_request {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_decline_the_quote_request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete role &apos;{0}&apos;?.
        /// </summary>
        public static string Are_you_sure_you_want_to_delete_role_name {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_delete_role_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this file?.
        /// </summary>
        public static string Are_you_sure_you_want_to_delete_this_file {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_delete_this_file", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this product?.
        /// </summary>
        public static string Are_you_sure_you_want_to_delete_this_product {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_delete_this_product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete user &apos;{0}&apos;?.
        /// </summary>
        public static string Are_you_sure_you_want_to_delete_user_name {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_delete_user_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to remove the last Underwriter? Doing so will lose you access to this Quote Request and you&apos;ll have to ask the Broker to re-add you.
        /// </summary>
        public static string Are_you_sure_you_want_to_remove_last_underwriter {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_remove_last_underwriter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to remove role &apos;{0}&apos;?.
        /// </summary>
        public static string Are_you_sure_you_want_to_remove_role_name {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_remove_role_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to remove the application form?.
        /// </summary>
        public static string Are_you_sure_you_want_to_remove_the_application_form_ {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_remove_the_application_form_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to save applicant?.
        /// </summary>
        public static string Are_you_sure_you_want_to_save_applicant {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_save_applicant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to save Control Framework.
        /// </summary>
        public static string Are_you_sure_you_want_to_save_Control_Framework {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_save_Control_Framework", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to save Control Framework Category.
        /// </summary>
        public static string Are_you_sure_you_want_to_save_Control_Framework_Category {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_save_Control_Framework_Category", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to save Control Framework Category Clause?.
        /// </summary>
        public static string Are_you_sure_you_want_to_save_Control_Framework_Category_Clause {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_save_Control_Framework_Category_Clause", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to save country.
        /// </summary>
        public static string Are_you_sure_you_want_to_save_country {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_save_country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to save the application form?.
        /// </summary>
        public static string Are_you_sure_you_want_to_save_form {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_save_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to save industry.
        /// </summary>
        public static string Are_you_sure_you_want_to_save_industry {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_save_industry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to save loss type.
        /// </summary>
        public static string Are_you_sure_you_want_to_save_loss_type {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_save_loss_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to save region.
        /// </summary>
        public static string Are_you_sure_you_want_to_save_region {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_save_region", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to save role?.
        /// </summary>
        public static string Are_you_sure_you_want_to_save_role {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_save_role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to save tenant?.
        /// </summary>
        public static string Are_you_sure_you_want_to_save_tenant {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_save_tenant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to save user profile?.
        /// </summary>
        public static string Are_you_sure_you_want_to_save_user_profile {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_save_user_profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to send the quote to the Broker?.
        /// </summary>
        public static string Are_you_sure_you_want_to_send_the_quote {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_send_the_quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to submit this Assessment Request?.
        /// </summary>
        public static string Are_you_sure_you_want_to_submit_supplier_request {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_submit_supplier_request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to submit this Assessment Request with file(s) not provided?.
        /// </summary>
        public static string Are_you_sure_you_want_to_submit_supplier_request_with_no_files {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_submit_supplier_request_with_no_files", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to undo the cancellation of the Broker Quote Request?.
        /// </summary>
        public static string Are_you_sure_you_want_to_undo_the_cancellation {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_undo_the_cancellation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to withdraw this Wholesale Broker Quote Request.
        /// </summary>
        public static string Are_you_sure_you_want_to_withdraw_this_wholesale_broker_quote_request {
            get {
                return ResourceManager.GetString("Are_you_sure_you_want_to_withdraw_this_wholesale_broker_quote_request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Army.
        /// </summary>
        public static string Army {
            get {
                return ResourceManager.GetString("Army", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assessment Date.
        /// </summary>
        public static string Assessment_Date {
            get {
                return ResourceManager.GetString("Assessment_Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assessment Name.
        /// </summary>
        public static string Assessment_Name {
            get {
                return ResourceManager.GetString("Assessment_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assessment Request.
        /// </summary>
        public static string Assessment_Request {
            get {
                return ResourceManager.GetString("Assessment_Request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assessment Requests.
        /// </summary>
        public static string Assessment_Requests {
            get {
                return ResourceManager.GetString("Assessment_Requests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View and track all assessment requests your organisation has sent to this supplier.
        /// </summary>
        public static string Assessment_requests_subtitle {
            get {
                return ResourceManager.GetString("Assessment_requests_subtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assessment Status.
        /// </summary>
        public static string Assessment_Status {
            get {
                return ResourceManager.GetString("Assessment_Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assessment Version.
        /// </summary>
        public static string Assessment_Version {
            get {
                return ResourceManager.GetString("Assessment_Version", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assessments.
        /// </summary>
        public static string Assessments {
            get {
                return ResourceManager.GetString("Assessments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign Brokers.
        /// </summary>
        public static string Assign_Brokers {
            get {
                return ResourceManager.GetString("Assign_Brokers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign Permissions.
        /// </summary>
        public static string Assign_Permissions {
            get {
                return ResourceManager.GetString("Assign_Permissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign Permissions for.
        /// </summary>
        public static string Assign_Permissions_for {
            get {
                return ResourceManager.GetString("Assign_Permissions_for", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign Role.
        /// </summary>
        public static string Assign_Role {
            get {
                return ResourceManager.GetString("Assign_Role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign Roles.
        /// </summary>
        public static string Assign_Roles {
            get {
                return ResourceManager.GetString("Assign_Roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign Underwriters.
        /// </summary>
        public static string Assign_Underwriters {
            get {
                return ResourceManager.GetString("Assign_Underwriters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assigned Brokers.
        /// </summary>
        public static string Assigned_Brokers {
            get {
                return ResourceManager.GetString("Assigned_Brokers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assigned Roles.
        /// </summary>
        public static string Assigned_Roles {
            get {
                return ResourceManager.GetString("Assigned_Roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Associate &amp; Email.
        /// </summary>
        public static string Associate_And_Email {
            get {
                return ResourceManager.GetString("Associate_And_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Associations.
        /// </summary>
        public static string Associations {
            get {
                return ResourceManager.GetString("Associations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to At least one underwriter must be specified.
        /// </summary>
        public static string At_least_one_underwriter_must_be_specified {
            get {
                return ResourceManager.GetString("At_least_one_underwriter_must_be_specified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to August.
        /// </summary>
        public static string August {
            get {
                return ResourceManager.GetString("August", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authenticator App.
        /// </summary>
        public static string Authenticator_App {
            get {
                return ResourceManager.GetString("Authenticator_App", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authenticator Code.
        /// </summary>
        public static string Authenticator_Code {
            get {
                return ResourceManager.GetString("Authenticator_Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorising....
        /// </summary>
        public static string Authorising {
            get {
                return ResourceManager.GetString("Authorising", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorization.
        /// </summary>
        public static string Authorization {
            get {
                return ResourceManager.GetString("Authorization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorizing.
        /// </summary>
        public static string Authorizing {
            get {
                return ResourceManager.GetString("Authorizing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Avatar.
        /// </summary>
        public static string Avatar {
            get {
                return ResourceManager.GetString("Avatar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average Contract Duration (years).
        /// </summary>
        public static string Average_Contract_Duration_Years {
            get {
                return ResourceManager.GetString("Average_Contract_Duration_Years", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average Contract Size.
        /// </summary>
        public static string Average_Contract_Size {
            get {
                return ResourceManager.GetString("Average_Contract_Size", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Awaiting.
        /// </summary>
        public static string Awaiting {
            get {
                return ResourceManager.GetString("Awaiting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Awaiting Approval.
        /// </summary>
        public static string Awaiting_Approval {
            get {
                return ResourceManager.GetString("Awaiting_Approval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Awaiting completion by supplier.
        /// </summary>
        public static string Awaiting_completion_by_supplier {
            get {
                return ResourceManager.GetString("Awaiting_completion_by_supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Awaiting response.
        /// </summary>
        public static string Awaiting_Response {
            get {
                return ResourceManager.GetString("Awaiting_Response", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Awesome!.
        /// </summary>
        public static string Awesome {
            get {
                return ResourceManager.GetString("Awesome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Azure File Storage.
        /// </summary>
        public static string Azure_File_Storage {
            get {
                return ResourceManager.GetString("Azure_File_Storage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Back.
        /// </summary>
        public static string Back {
            get {
                return ResourceManager.GetString("Back", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basic Info.
        /// </summary>
        public static string Basic_Info {
            get {
                return ResourceManager.GetString("Basic_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basic Information.
        /// </summary>
        public static string Basic_Information {
            get {
                return ResourceManager.GetString("Basic_Information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter supplier information.
        /// </summary>
        public static string Basic_Information_Supplementary_Text {
            get {
                return ResourceManager.GetString("Basic_Information_Supplementary_Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basic Submission Data.
        /// </summary>
        public static string Basic_Submission_Data {
            get {
                return ResourceManager.GetString("Basic_Submission_Data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to before you can log in with a recovery code..
        /// </summary>
        public static string before_you_can_log_in_with_a_recovery_code {
            get {
                return ResourceManager.GetString("before_you_can_log_in_with_a_recovery_code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Benchmark Results.
        /// </summary>
        public static string Benchmark_Results {
            get {
                return ResourceManager.GetString("Benchmark_Results", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Biomass.
        /// </summary>
        public static string Biomass {
            get {
                return ResourceManager.GetString("Biomass", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Biomass.
        /// </summary>
        public static string Biomass1 {
            get {
                return ResourceManager.GetString("Biomass1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BMI.
        /// </summary>
        public static string BMI {
            get {
                return ResourceManager.GetString("BMI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A BMI of 25.0 or more is overweight, while the healthy range is 18.5 to 24.9. BMI applies to most adults 18-65 years..
        /// </summary>
        public static string BMI_Details {
            get {
                return ResourceManager.GetString("BMI_Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Body Mass Index is a simple calculation using a person’s height and weight. The formula is BMI = kg/m2 where kg is a person’s weight in kilograms and m2 is their height in metres squared..
        /// </summary>
        public static string BMI_Info {
            get {
                return ResourceManager.GetString("BMI_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Body Mass Index.
        /// </summary>
        public static string Body_Mass_Index {
            get {
                return ResourceManager.GetString("Body_Mass_Index", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Both.
        /// </summary>
        public static string Both {
            get {
                return ResourceManager.GetString("Both", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom.
        /// </summary>
        public static string Bottom {
            get {
                return ResourceManager.GetString("Bottom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Broker.
        /// </summary>
        public static string Broker {
            get {
                return ResourceManager.GetString("Broker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Broker Email.
        /// </summary>
        public static string Broker_Email {
            get {
                return ResourceManager.GetString("Broker_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Broker Information.
        /// </summary>
        public static string Broker_Information {
            get {
                return ResourceManager.GetString("Broker_Information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Broker Name.
        /// </summary>
        public static string Broker_Name {
            get {
                return ResourceManager.GetString("Broker_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wholesale Broker Quote Request.
        /// </summary>
        public static string Broker_Quote_Request {
            get {
                return ResourceManager.GetString("Broker_Quote_Request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wholesale Broker Quote Requests.
        /// </summary>
        public static string Broker_Quote_Requests {
            get {
                return ResourceManager.GetString("Broker_Quote_Requests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Broker quotes.
        /// </summary>
        public static string Broker_quotes {
            get {
                return ResourceManager.GetString("Broker_quotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The broker will not be able to access your security record..
        /// </summary>
        public static string Broker_Wont_Be_Able_To_Access_Security_Record {
            get {
                return ResourceManager.GetString("Broker_Wont_Be_Able_To_Access_Security_Record", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brokerage (percentage).
        /// </summary>
        public static string Brokerage_Percentage {
            get {
                return ResourceManager.GetString("Brokerage_Percentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Broker.
        /// </summary>
        public static string BrokerRequest {
            get {
                return ResourceManager.GetString("BrokerRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brokers.
        /// </summary>
        public static string Brokers {
            get {
                return ResourceManager.GetString("Brokers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to broker(s) awaiting your approval.
        /// </summary>
        public static string Brokers_Awaiting_Your_Approval {
            get {
                return ResourceManager.GetString("Brokers_Awaiting_Your_Approval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brokers that have access to your most recent submission.
        /// </summary>
        public static string Brokers_that_have_access_to_your_most_recent_submission {
            get {
                return ResourceManager.GetString("Brokers_that_have_access_to_your_most_recent_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Broking House.
        /// </summary>
        public static string Broking_House {
            get {
                return ResourceManager.GetString("Broking_House", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Broking House Information.
        /// </summary>
        public static string Broking_house_information {
            get {
                return ResourceManager.GetString("Broking_house_information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Broking House Name.
        /// </summary>
        public static string Broking_house_name {
            get {
                return ResourceManager.GetString("Broking_house_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Broking House Relationships.
        /// </summary>
        public static string Broking_House_Relationships {
            get {
                return ResourceManager.GetString("Broking_House_Relationships", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Broking Houses.
        /// </summary>
        public static string Broking_Houses {
            get {
                return ResourceManager.GetString("Broking_Houses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Broking Name.
        /// </summary>
        public static string Broking_Name {
            get {
                return ResourceManager.GetString("Broking_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browser Time Zone.
        /// </summary>
        public static string Browser_Time_Zone {
            get {
                return ResourceManager.GetString("Browser_Time_Zone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Builder.
        /// </summary>
        public static string Builder {
            get {
                return ResourceManager.GetString("Builder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Upload CSV.
        /// </summary>
        public static string Bulk_Upload_CSV {
            get {
                return ResourceManager.GetString("Bulk_Upload_CSV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Business Activity.
        /// </summary>
        public static string Business_Activity {
            get {
                return ResourceManager.GetString("Business_Activity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy Now.
        /// </summary>
        public static string Buy_Now {
            get {
                return ResourceManager.GetString("Buy_Now", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bytes.
        /// </summary>
        public static string Bytes {
            get {
                return ResourceManager.GetString("Bytes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calcium.
        /// </summary>
        public static string Calcium {
            get {
                return ResourceManager.GetString("Calcium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculated Answers.
        /// </summary>
        public static string Calculated_Answers {
            get {
                return ResourceManager.GetString("Calculated_Answers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calendar.
        /// </summary>
        public static string Calendar {
            get {
                return ResourceManager.GetString("Calendar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calendar Week Rule.
        /// </summary>
        public static string Calendar_Week_Rule {
            get {
                return ResourceManager.GetString("Calendar_Week_Rule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Californium.
        /// </summary>
        public static string Californium {
            get {
                return ResourceManager.GetString("Californium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can&apos;t create a new assessment because the supplier association is inactive.
        /// </summary>
        public static string Can_t_create_a_new_assessment_because_the_supplier_association_is_inactive {
            get {
                return ResourceManager.GetString("Can_t_create_a_new_assessment_because_the_supplier_association_is_inactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel Quote Request.
        /// </summary>
        public static string Cancel_Quote_Request {
            get {
                return ResourceManager.GetString("Cancel_Quote_Request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel Upload.
        /// </summary>
        public static string Cancel_Upload {
            get {
                return ResourceManager.GetString("Cancel_Upload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancelled by.
        /// </summary>
        public static string Cancelled_by {
            get {
                return ResourceManager.GetString("Cancelled_by", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Declined by broker.
        /// </summary>
        public static string Cancelled_Request {
            get {
                return ResourceManager.GetString("Cancelled_Request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This quote has been declined by the broker..
        /// </summary>
        public static string Cancelled_Request_message {
            get {
                return ResourceManager.GetString("Cancelled_Request_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot find file.
        /// </summary>
        public static string Cannot_find_file {
            get {
                return ResourceManager.GetString("Cannot_find_file", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Carbon.
        /// </summary>
        public static string Carbon {
            get {
                return ResourceManager.GetString("Carbon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cerium.
        /// </summary>
        public static string Cerium {
            get {
                return ResourceManager.GetString("Cerium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cesium.
        /// </summary>
        public static string Cesium {
            get {
                return ResourceManager.GetString("Cesium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change Email.
        /// </summary>
        public static string Change_Email {
            get {
                return ResourceManager.GetString("Change_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change Me....
        /// </summary>
        public static string Change_Me {
            get {
                return ResourceManager.GetString("Change_Me", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change Password.
        /// </summary>
        public static string Change_Password {
            get {
                return ResourceManager.GetString("Change_Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change User Password.
        /// </summary>
        public static string Change_User_Password {
            get {
                return ResourceManager.GetString("Change_User_Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change Your Account Settings.
        /// </summary>
        public static string Change_your_account_settings {
            get {
                return ResourceManager.GetString("Change_your_account_settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change your Primary Broking House.
        /// </summary>
        public static string Change_your_Primary_Broking_House {
            get {
                return ResourceManager.GetString("Change_your_Primary_Broking_House", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chernobyl-1.
        /// </summary>
        public static string Chernobyl_1 {
            get {
                return ResourceManager.GetString("Chernobyl_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chernobyl-2.
        /// </summary>
        public static string Chernobyl_2 {
            get {
                return ResourceManager.GetString("Chernobyl_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chernobyl-3.
        /// </summary>
        public static string Chernobyl_3 {
            get {
                return ResourceManager.GetString("Chernobyl_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chernobyl-4.
        /// </summary>
        public static string Chernobyl_4 {
            get {
                return ResourceManager.GetString("Chernobyl_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Child Submissions.
        /// </summary>
        public static string Child_Submissions {
            get {
                return ResourceManager.GetString("Child_Submissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chlorine.
        /// </summary>
        public static string Chlorine {
            get {
                return ResourceManager.GetString("Chlorine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choice name is required.
        /// </summary>
        public static string Choice_name_is_required {
            get {
                return ResourceManager.GetString("Choice_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choice of Law and Jurisdiction.
        /// </summary>
        public static string Choice_of_Law_And_Jurisdiction {
            get {
                return ResourceManager.GetString("Choice_of_Law_And_Jurisdiction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chromium.
        /// </summary>
        public static string Chromium {
            get {
                return ResourceManager.GetString("Chromium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear.
        /// </summary>
        public static string Clear {
            get {
                return ResourceManager.GetString("Clear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click.
        /// </summary>
        public static string Click {
            get {
                return ResourceManager.GetString("Click", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to click here to log in.
        /// </summary>
        public static string click_here_to_log_in {
            get {
                return ResourceManager.GetString("click_here_to_log_in", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have unfinished items, click this card to navigate to the page to complete these tasks.
        /// </summary>
        public static string Click_here_to_navigate_to_page_to_complete_action {
            get {
                return ResourceManager.GetString("Click_here_to_navigate_to_page_to_complete_action", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Information.
        /// </summary>
        public static string Client_Information {
            get {
                return ResourceManager.GetString("Client_Information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Name.
        /// </summary>
        public static string Client_Name {
            get {
                return ResourceManager.GetString("Client_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client-Side Authorization.
        /// </summary>
        public static string Client_Side_Authorization {
            get {
                return ResourceManager.GetString("Client_Side_Authorization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client-Side Validation.
        /// </summary>
        public static string Client_Side_Validation {
            get {
                return ResourceManager.GetString("Client_Side_Validation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        public static string Close {
            get {
                return ResourceManager.GetString("Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close SignalR Connection.
        /// </summary>
        public static string Close_SignalR_Connection {
            get {
                return ResourceManager.GetString("Close_SignalR_Connection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Coal.
        /// </summary>
        public static string Coal {
            get {
                return ResourceManager.GetString("Coal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Coal.
        /// </summary>
        public static string Coal1 {
            get {
                return ResourceManager.GetString("Coal1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cobalt.
        /// </summary>
        public static string Cobalt {
            get {
                return ResourceManager.GetString("Cobalt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code.
        /// </summary>
        public static string Code {
            get {
                return ResourceManager.GetString("Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code Samples.
        /// </summary>
        public static string Code_Samples {
            get {
                return ResourceManager.GetString("Code_Samples", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comments.
        /// </summary>
        public static string Comments {
            get {
                return ResourceManager.GetString("Comments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Number.
        /// </summary>
        public static string Company_Number {
            get {
                return ResourceManager.GetString("Company_Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete.
        /// </summary>
        public static string Complete {
            get {
                return ResourceManager.GetString("Complete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete Application Form.
        /// </summary>
        public static string Complete_Application_Form {
            get {
                return ResourceManager.GetString("Complete_Application_Form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete your current changes before continuing.
        /// </summary>
        public static string Complete_current_changes {
            get {
                return ResourceManager.GetString("Complete_current_changes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete your MFA settings.
        /// </summary>
        public static string Complete_mfa_settings {
            get {
                return ResourceManager.GetString("Complete_mfa_settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete the forms below to provide all the necessary information for your submission. You can save your progress as you go, review your answers at any time, and submit once everything is complete..
        /// </summary>
        public static string Complete_the_forms_below {
            get {
                return ResourceManager.GetString("Complete_the_forms_below", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completed.
        /// </summary>
        public static string Completed {
            get {
                return ResourceManager.GetString("Completed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completed Date.
        /// </summary>
        public static string Completed_Date {
            get {
                return ResourceManager.GetString("Completed_Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completed On.
        /// </summary>
        public static string Completed_On {
            get {
                return ResourceManager.GetString("Completed_On", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completed Organisation Submissions.
        /// </summary>
        public static string Completed_Organisation_Submissions {
            get {
                return ResourceManager.GetString("Completed_Organisation_Submissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completed Submissions.
        /// </summary>
        public static string Completed_Submissions {
            get {
                return ResourceManager.GetString("Completed_Submissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completing sign-in....
        /// </summary>
        public static string Completing_sign_in {
            get {
                return ResourceManager.GetString("Completing_sign-in", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completing sign-out....
        /// </summary>
        public static string Completing_sign_out {
            get {
                return ResourceManager.GetString("Completing_sign-out", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configure Authenticator App.
        /// </summary>
        public static string Configure_Authenticator_App {
            get {
                return ResourceManager.GetString("Configure_Authenticator_App", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm.
        /// </summary>
        public static string Confirm {
            get {
                return ResourceManager.GetString("Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Email.
        /// </summary>
        public static string Confirm_Email {
            get {
                return ResourceManager.GetString("Confirm_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Email Change.
        /// </summary>
        public static string Confirm_Email_Change {
            get {
                return ResourceManager.GetString("Confirm_Email_Change", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Password.
        /// </summary>
        public static string Confirm_Password {
            get {
                return ResourceManager.GetString("Confirm_Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Removing Application Form.
        /// </summary>
        public static string Confirm_removing_application_form {
            get {
                return ResourceManager.GetString("Confirm_removing_application_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Removing Last Underwriter.
        /// </summary>
        public static string Confirm_removing_last_underwriter {
            get {
                return ResourceManager.GetString("Confirm_removing_last_underwriter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm sending submission.
        /// </summary>
        public static string Confirm_sending_submission {
            get {
                return ResourceManager.GetString("Confirm_sending_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Submission.
        /// </summary>
        public static string Confirm_submission {
            get {
                return ResourceManager.GetString("Confirm_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Submitting for Review.
        /// </summary>
        public static string Confirm_submit_submission {
            get {
                return ResourceManager.GetString("Confirm_submit_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm Voiding Submission.
        /// </summary>
        public static string Confirm_voiding_submission {
            get {
                return ResourceManager.GetString("Confirm_voiding_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact.
        /// </summary>
        public static string Contact {
            get {
                return ResourceManager.GetString("Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Email.
        /// </summary>
        public static string Contact_Email {
            get {
                return ResourceManager.GetString("Contact_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact name.
        /// </summary>
        public static string Contact_Name {
            get {
                return ResourceManager.GetString("Contact_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Phone Number.
        /// </summary>
        public static string Contact_Phone_Number {
            get {
                return ResourceManager.GetString("Contact_Phone_Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Content.
        /// </summary>
        public static string Content {
            get {
                return ResourceManager.GetString("Content", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Content Type.
        /// </summary>
        public static string ContentType {
            get {
                return ResourceManager.GetString("ContentType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contract Start.
        /// </summary>
        public static string Contract_Start {
            get {
                return ResourceManager.GetString("Contract_Start", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contract Value.
        /// </summary>
        public static string Contract_Value {
            get {
                return ResourceManager.GetString("Contract_Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contracts.
        /// </summary>
        public static string Contracts {
            get {
                return ResourceManager.GetString("Contracts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Assessments.
        /// </summary>
        public static string Control_Assessments {
            get {
                return ResourceManager.GetString("Control_Assessments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Categories.
        /// </summary>
        public static string Control_Framework_Categories {
            get {
                return ResourceManager.GetString("Control_Framework_Categories", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Category Clause name is required.
        /// </summary>
        public static string Control_Framework_Category_Clause_name_is_required {
            get {
                return ResourceManager.GetString("Control_Framework_Category_Clause_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Category name is required.
        /// </summary>
        public static string Control_Framework_Category_name_is_required {
            get {
                return ResourceManager.GetString("Control_Framework_Category_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Score is based on application form responses.
        /// </summary>
        public static string Control_Framework_Description_Not_Theia {
            get {
                return ResourceManager.GetString("Control_Framework_Description_Not_Theia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TheiaLense SF Score is our proprietary control framework based on weighted averages of responses.
        /// </summary>
        public static string Control_Framework_Description_Theia {
            get {
                return ResourceManager.GetString("Control_Framework_Description_Theia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Domain Details.
        /// </summary>
        public static string Control_Framework_Domain_Details {
            get {
                return ResourceManager.GetString("Control_Framework_Domain_Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Management.
        /// </summary>
        public static string Control_Framework_Management {
            get {
                return ResourceManager.GetString("Control_Framework_Management", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework Name.
        /// </summary>
        public static string Control_Framework_Name {
            get {
                return ResourceManager.GetString("Control_Framework_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Framework name is required.
        /// </summary>
        public static string Control_Framework_name_is_required {
            get {
                return ResourceManager.GetString("Control_Framework_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Control Framework name is formatted incorrectly.
        /// </summary>
        public static string Control_framework_option_formatting {
            get {
                return ResourceManager.GetString("Control_framework_option_formatting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 5.
        /// </summary>
        public static string Control_framework_saved {
            get {
                return ResourceManager.GetString("Control_framework_saved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Could not validate the selected Control Frameworks.
        /// </summary>
        public static string Control_framework_validation_failed {
            get {
                return ResourceManager.GetString("Control_framework_validation_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Control Frameworks.
        /// </summary>
        public static string Control_Frameworks {
            get {
                return ResourceManager.GetString("Control_Frameworks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copernicium.
        /// </summary>
        public static string Copernicium {
            get {
                return ResourceManager.GetString("Copernicium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copied.
        /// </summary>
        public static string Copied {
            get {
                return ResourceManager.GetString("Copied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copper.
        /// </summary>
        public static string Copper {
            get {
                return ResourceManager.GetString("Copper", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy Codes.
        /// </summary>
        public static string Copy_codes {
            get {
                return ResourceManager.GetString("Copy_codes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred and no request was made.
        /// </summary>
        public static string Could_not_make_request {
            get {
                return ResourceManager.GetString("Could_not_make_request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Countries.
        /// </summary>
        public static string Countries {
            get {
                return ResourceManager.GetString("Countries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country.
        /// </summary>
        public static string Country {
            get {
                return ResourceManager.GetString("Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country exposure level is required.
        /// </summary>
        public static string Country_exposure_level_is_required {
            get {
                return ResourceManager.GetString("Country_exposure_level_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country name is required.
        /// </summary>
        public static string Country_name_is_required {
            get {
                return ResourceManager.GetString("Country_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country Revenue Split.
        /// </summary>
        public static string Country_Revenue_Split {
            get {
                return ResourceManager.GetString("Country_Revenue_Split", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country Settings.
        /// </summary>
        public static string Country_Settings {
            get {
                return ResourceManager.GetString("Country Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Covered.
        /// </summary>
        public static string Covered {
            get {
                return ResourceManager.GetString("Covered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create.
        /// </summary>
        public static string Create {
            get {
                return ResourceManager.GetString("Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create a Quota Share using this Option.
        /// </summary>
        public static string Create_a_Quota_Share_using_this_Option {
            get {
                return ResourceManager.GetString("Create_a_Quota_Share_using_this_Option", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create New Account for.
        /// </summary>
        public static string Create_New_Account_for {
            get {
                return ResourceManager.GetString("Create_New_Account_for", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create New Submission.
        /// </summary>
        public static string Create_New_Submission {
            get {
                return ResourceManager.GetString("Create_New_Submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Quota Share.
        /// </summary>
        public static string Create_Quota_Share {
            get {
                return ResourceManager.GetString("Create_Quota_Share", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Quote.
        /// </summary>
        public static string Create_Quote {
            get {
                return ResourceManager.GetString("Create_Quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Quote Request.
        /// </summary>
        public static string Create_quote_request {
            get {
                return ResourceManager.GetString("Create_quote_request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Self Assessment.
        /// </summary>
        public static string Create_Self_Assessment {
            get {
                return ResourceManager.GetString("Create_Self_Assessment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Submission.
        /// </summary>
        public static string Create_Submission {
            get {
                return ResourceManager.GetString("Create_Submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create submissions.
        /// </summary>
        public static string Create_submissions {
            get {
                return ResourceManager.GetString("Create_submissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created By.
        /// </summary>
        public static string Created_By {
            get {
                return ResourceManager.GetString("Created_By", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created On.
        /// </summary>
        public static string Created_On {
            get {
                return ResourceManager.GetString("Created_On", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Criticality.
        /// </summary>
        public static string Criticality {
            get {
                return ResourceManager.GetString("Criticality", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Culture-Based Resources.
        /// </summary>
        public static string Culture_Based_Resources {
            get {
                return ResourceManager.GetString("Culture_Based_Resources", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Culture Code.
        /// </summary>
        public static string Culture_Code {
            get {
                return ResourceManager.GetString("Culture_Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Culture Currency Symbol.
        /// </summary>
        public static string Culture_Currency_Symbol {
            get {
                return ResourceManager.GetString("Culture_Currency_Symbol", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Culture DateTime Pattern.
        /// </summary>
        public static string Culture_DateTime_Pattern {
            get {
                return ResourceManager.GetString("Culture_DateTime_Pattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Culture Info.
        /// </summary>
        public static string Culture_Info {
            get {
                return ResourceManager.GetString("Culture_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Curium.
        /// </summary>
        public static string Curium {
            get {
                return ResourceManager.GetString("Curium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency.
        /// </summary>
        public static string Currency {
            get {
                return ResourceManager.GetString("Currency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Culture DateTime Format.
        /// </summary>
        public static string Current_Culture_DateTime_Format {
            get {
                return ResourceManager.GetString("Current_Culture_DateTime_Format", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Culture Info.
        /// </summary>
        public static string Current_Culture_Info {
            get {
                return ResourceManager.GetString("Current_Culture_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Culture Time Zone Info.
        /// </summary>
        public static string Current_Culture_Time_Zone_Info {
            get {
                return ResourceManager.GetString("Current_Culture_Time_Zone_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Documents.
        /// </summary>
        public static string Current_Documents {
            get {
                return ResourceManager.GetString("Current_Documents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Password.
        /// </summary>
        public static string Current_password {
            get {
                return ResourceManager.GetString("Current_password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Transcripts.
        /// </summary>
        public static string Current_Transcripts {
            get {
                return ResourceManager.GetString("Current_Transcripts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Year Revenue.
        /// </summary>
        public static string Current_Year_Revenue {
            get {
                return ResourceManager.GetString("Current_Year_Revenue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Danger.
        /// </summary>
        public static string Danger {
            get {
                return ResourceManager.GetString("Danger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Darmstadtium.
        /// </summary>
        public static string Darmstadtium {
            get {
                return ResourceManager.GetString("Darmstadtium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dashboard.
        /// </summary>
        public static string Dashboard {
            get {
                return ResourceManager.GetString("Dashboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data Centre Locations.
        /// </summary>
        public static string Data_Centre_Location {
            get {
                return ResourceManager.GetString("Data_Centre_Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data Export (SignalR &amp; Hangfire).
        /// </summary>
        public static string Data_Export_SignalR_Hangfire {
            get {
                return ResourceManager.GetString("Data_Export_SignalR_Hangfire", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        public static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Completed On.
        /// </summary>
        public static string Date_Completed_On {
            get {
                return ResourceManager.GetString("Date_Completed_On", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date of Birth.
        /// </summary>
        public static string Date_of_Birth {
            get {
                return ResourceManager.GetString("Date_of_Birth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Provided.
        /// </summary>
        public static string Date_Provided {
            get {
                return ResourceManager.GetString("Date_Provided", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date sent.
        /// </summary>
        public static string Date_Sent {
            get {
                return ResourceManager.GetString("Date_Sent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Separator.
        /// </summary>
        public static string Date_Separator {
            get {
                return ResourceManager.GetString("Date_Separator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DateTime Format.
        /// </summary>
        public static string DateTime_Format {
            get {
                return ResourceManager.GetString("DateTime_Format", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Day.
        /// </summary>
        public static string Day {
            get {
                return ResourceManager.GetString("Day", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Day Names.
        /// </summary>
        public static string Day_Names {
            get {
                return ResourceManager.GetString("Day_Names", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deactivate.
        /// </summary>
        public static string Deactivate {
            get {
                return ResourceManager.GetString("Deactivate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deactivate Supplier.
        /// </summary>
        public static string Deactivate_Supplier {
            get {
                return ResourceManager.GetString("Deactivate_Supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deactivated.
        /// </summary>
        public static string Deactivated {
            get {
                return ResourceManager.GetString("Deactivated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to December.
        /// </summary>
        public static string December {
            get {
                return ResourceManager.GetString("December", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Decline.
        /// </summary>
        public static string Decline {
            get {
                return ResourceManager.GetString("Decline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Decline Quota Share.
        /// </summary>
        public static string Decline_quota_share {
            get {
                return ResourceManager.GetString("Decline_quota_share", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to decline this quote?.
        /// </summary>
        public static string Decline_Quote_Confirmation {
            get {
                return ResourceManager.GetString("Decline_Quote_Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Declined.
        /// </summary>
        public static string Declined {
            get {
                return ResourceManager.GetString("Declined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Declined by.
        /// </summary>
        public static string Declined_by {
            get {
                return ResourceManager.GetString("Declined_by", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Declined by organisation.
        /// </summary>
        public static string Declined_By_Organisation {
            get {
                return ResourceManager.GetString("Declined_By_Organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Declined by underwriter.
        /// </summary>
        public static string Declined_By_Underwriter {
            get {
                return ResourceManager.GetString("Declined_By_Underwriter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This quote has been declined by the underwriter..
        /// </summary>
        public static string Declined_By_Underwriter_message {
            get {
                return ResourceManager.GetString("Declined_By_Underwriter_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Admin Credentials.
        /// </summary>
        public static string Default_Admin_Credentials {
            get {
                return ResourceManager.GetString("Default_Admin_Credentials", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default lockout time span.
        /// </summary>
        public static string Default_lockout_time_span {
            get {
                return ResourceManager.GetString("Default_lockout_time_span", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The file is not in use and will be permanently deleted. Do you want to continue?.
        /// </summary>
        public static string Delete_File_Permanently_Confirm_Message {
            get {
                return ResourceManager.GetString("Delete_File_Permanently_Confirm_Message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete My Account.
        /// </summary>
        public static string Delete_my_Account {
            get {
                return ResourceManager.GetString("Delete_my_Account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Personal Data.
        /// </summary>
        public static string Delete_Personal_Data {
            get {
                return ResourceManager.GetString("Delete_Personal_Data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleting this data will permanently remove your account, and this cannot be recovered..
        /// </summary>
        public static string Deleting_this_data_will_permanently_remove_your_account_and_this_cannot_be_recovered {
            get {
                return ResourceManager.GetString("Deleting_this_data_will_permanently_remove_your_account_and_this_cannot_be_recove" +
                        "red", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        public static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description for.
        /// </summary>
        public static string Description_for {
            get {
                return ResourceManager.GetString("Description_for", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deselect this option.
        /// </summary>
        public static string Deselect_Option {
            get {
                return ResourceManager.GetString("Deselect_Option", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you wish to deselect this option? You can reselect this option at anytime, if you change your mind..
        /// </summary>
        public static string Deselect_Option_Confirmation {
            get {
                return ResourceManager.GetString("Deselect_Option_Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Details.
        /// </summary>
        public static string Details {
            get {
                return ResourceManager.GetString("Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disable Two-Factor Authentication.
        /// </summary>
        public static string Disable_2FA {
            get {
                return ResourceManager.GetString("Disable_2FA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disable Two-Factor Authentication.
        /// </summary>
        public static string Disable_Two_Factor_Authentication {
            get {
                return ResourceManager.GetString("Disable_Two-Factor_Authentication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disabling Two-Factor Authentication does not change the keys used in authenticator apps.
        /// </summary>
        public static string Disabling_2FA_does_not_change_the_keys_used_in_authenticator_apps {
            get {
                return ResourceManager.GetString("Disabling_2FA_does_not_change_the_keys_used_in_authenticator_apps", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dismissed.
        /// </summary>
        public static string Dismissed {
            get {
                return ResourceManager.GetString("Dismissed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Display Name.
        /// </summary>
        public static string Display_Name {
            get {
                return ResourceManager.GetString("Display_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Areas of Improvement.
        /// </summary>
        public static string Dissatisfied_State_Name {
            get {
                return ResourceManager.GetString("Dissatisfied_State_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you really want to delete this record?.
        /// </summary>
        public static string Do_you_really_want_to_delete_this_record {
            get {
                return ResourceManager.GetString("Do_you_really_want_to_delete_this_record", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to remove this record?.
        /// </summary>
        public static string Do_you_want_to_remove_this_record {
            get {
                return ResourceManager.GetString("Do_you_want_to_remove_this_record", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Documents.
        /// </summary>
        public static string Documents {
            get {
                return ResourceManager.GetString("Documents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Domain Analysis.
        /// </summary>
        public static string Domain_Analysis {
            get {
                return ResourceManager.GetString("Domain_Analysis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Don&apos;t have an account?.
        /// </summary>
        public static string Dont_have_an_account {
            get {
                return ResourceManager.GetString("Dont_have_an_account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download.
        /// </summary>
        public static string Download {
            get {
                return ResourceManager.GetString("Download", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download as PDF.
        /// </summary>
        public static string Download_as_PDF {
            get {
                return ResourceManager.GetString("Download_as_PDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download Org Profile.
        /// </summary>
        public static string Download_Org_Profile {
            get {
                return ResourceManager.GetString("Download_Org_Profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download Quote Sheet.
        /// </summary>
        public static string Download_quote_sheet {
            get {
                return ResourceManager.GetString("Download_quote_sheet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Downloads.
        /// </summary>
        public static string Downloads {
            get {
                return ResourceManager.GetString("Downloads", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dubnium.
        /// </summary>
        public static string Dubnium {
            get {
                return ResourceManager.GetString("Dubnium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due By.
        /// </summary>
        public static string Due_By {
            get {
                return ResourceManager.GetString("Due_By", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate.
        /// </summary>
        public static string Duplicate {
            get {
                return ResourceManager.GetString("Duplicate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate Submission.
        /// </summary>
        public static string Duplicate_Submission {
            get {
                return ResourceManager.GetString("Duplicate_Submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to duplicate this Submission?.
        /// </summary>
        public static string Duplicate_submission_message {
            get {
                return ResourceManager.GetString("Duplicate_submission_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duration (years).
        /// </summary>
        public static string Duration_Years {
            get {
                return ResourceManager.GetString("Duration_Years", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dysprosium.
        /// </summary>
        public static string Dysprosium {
            get {
                return ResourceManager.GetString("Dysprosium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Each submission contains your.
        /// </summary>
        public static string Each_submission_contains {
            get {
                return ResourceManager.GetString("Each_submission_contains", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Earning Report.
        /// </summary>
        public static string Earning_Report {
            get {
                return ResourceManager.GetString("Earning_Report", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Earnings.
        /// </summary>
        public static string Earnings {
            get {
                return ResourceManager.GetString("Earnings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit.
        /// </summary>
        public static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Applicant.
        /// </summary>
        public static string Edit_Applicant {
            get {
                return ResourceManager.GetString("Edit_Applicant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Control Framework.
        /// </summary>
        public static string Edit_Control_Framework {
            get {
                return ResourceManager.GetString("Edit_Control_Framework", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Policy Form.
        /// </summary>
        public static string Edit_Policy_Form {
            get {
                return ResourceManager.GetString("Edit_Policy_Form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Product.
        /// </summary>
        public static string Edit_product {
            get {
                return ResourceManager.GetString("Edit_product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Reference.
        /// </summary>
        public static string Edit_Reference {
            get {
                return ResourceManager.GetString("Edit_Reference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Role.
        /// </summary>
        public static string Edit_Role {
            get {
                return ResourceManager.GetString("Edit_Role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Tenant.
        /// </summary>
        public static string Edit_Tenant {
            get {
                return ResourceManager.GetString("Edit_Tenant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit User.
        /// </summary>
        public static string Edit_User {
            get {
                return ResourceManager.GetString("Edit_User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Version.
        /// </summary>
        public static string Edit_Version {
            get {
                return ResourceManager.GetString("Edit_Version", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit/View Organisation Profile.
        /// </summary>
        public static string Edit_View_Organisation_Profile {
            get {
                return ResourceManager.GetString("Edit_View_Organisation_Profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edited.
        /// </summary>
        public static string Edited {
            get {
                return ResourceManager.GetString("Edited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Einsteinium.
        /// </summary>
        public static string Einsteinium {
            get {
                return ResourceManager.GetString("Einsteinium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Change Confirmation.
        /// </summary>
        public static string Email_Change_Confirmation {
            get {
                return ResourceManager.GetString("Email_Change_Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Change Confirmed.
        /// </summary>
        public static string Email_Change_Confirmed {
            get {
                return ResourceManager.GetString("Email_Change_Confirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Confirmation.
        /// </summary>
        public static string Email_Confirmation {
            get {
                return ResourceManager.GetString("Email_Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Confirmed.
        /// </summary>
        public static string Email_Confirmed {
            get {
                return ResourceManager.GetString("Email_Confirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email format is invalid.
        /// </summary>
        public static string Email_format_invalid {
            get {
                return ResourceManager.GetString("Email_format_invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee broke a finger while writing some code.
        /// </summary>
        public static string Employee_broke_a_finger_while_writing_some_code {
            get {
                return ResourceManager.GetString("Employee_broke_a_finger_while_writing_some_code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employees cannot be null.
        /// </summary>
        public static string Employees_cannot_be_null {
            get {
                return ResourceManager.GetString("Employees_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employees Count.
        /// </summary>
        public static string Employees_Count {
            get {
                return ResourceManager.GetString("Employees_Count", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable or disable a product from appearing your Submissions.
        /// </summary>
        public static string Enable_or_disable_product {
            get {
                return ResourceManager.GetString("Enable_or_disable_product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to enable QR code generation.
        /// </summary>
        public static string enable_QR_code_generation {
            get {
                return ResourceManager.GetString("enable_QR_code_generation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enabled.
        /// </summary>
        public static string Enabled {
            get {
                return ResourceManager.GetString("Enabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End.
        /// </summary>
        public static string End {
            get {
                return ResourceManager.GetString("End", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End date cannot be before start date.
        /// </summary>
        public static string End_date_cannot_be_before_start_date {
            get {
                return ResourceManager.GetString("End_date_cannot_be_before_start_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Slope.
        /// </summary>
        public static string End_Slope {
            get {
                return ResourceManager.GetString("End_Slope", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Endorsements &amp; Exclusions.
        /// </summary>
        public static string Endorsements_And_Exclusions {
            get {
                return ResourceManager.GetString("Endorsements_And_Exclusions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to English Name.
        /// </summary>
        public static string English_Name {
            get {
                return ResourceManager.GetString("English_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter confirmation code below.
        /// </summary>
        public static string Enter_confirmation_code_below {
            get {
                return ResourceManager.GetString("Enter_confirmation_code_below", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter max line size.
        /// </summary>
        public static string Enter_max_line_size {
            get {
                return ResourceManager.GetString("Enter_max_line_size", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter reason for declining....
        /// </summary>
        public static string Enter_reason_for_declining {
            get {
                return ResourceManager.GetString("Enter_reason_for_declining", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter subjectivity name.
        /// </summary>
        public static string Enter_subjectivity_name {
            get {
                return ResourceManager.GetString("Enter_subjectivity_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the code in the confirmation box below..
        /// </summary>
        public static string Enter_the_code_in_the_confirmation_box_below {
            get {
                return ResourceManager.GetString("Enter_the_code_in_the_confirmation_box_below", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter your credentials below.
        /// </summary>
        public static string Enter_your_credentials_below {
            get {
                return ResourceManager.GetString("Enter_your_credentials_below", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter your email..
        /// </summary>
        public static string Enter_your_email {
            get {
                return ResourceManager.GetString("Enter_your_email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to entries.
        /// </summary>
        public static string entries {
            get {
                return ResourceManager.GetString("entries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Erbium.
        /// </summary>
        public static string Erbium {
            get {
                return ResourceManager.GetString("Erbium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Pages.
        /// </summary>
        public static string Error_Pages {
            get {
                return ResourceManager.GetString("Error_Pages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Europium.
        /// </summary>
        public static string Europium {
            get {
                return ResourceManager.GetString("Europium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to — everything needed to evaluate your risk profile and program performance..
        /// </summary>
        public static string Everything_needed_to_eval {
            get {
                return ResourceManager.GetString("Everything_needed_to_eval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error while getting data.
        /// </summary>
        public static string Exception_result_default_message {
            get {
                return ResourceManager.GetString("Exception_result_default_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Excess.
        /// </summary>
        public static string Excess {
            get {
                return ResourceManager.GetString("Excess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to excess of.
        /// </summary>
        public static string Excess_of {
            get {
                return ResourceManager.GetString("Excess_of", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to existing completed submissions to reuse or refine your information..
        /// </summary>
        public static string Existing_completed_submissions {
            get {
                return ResourceManager.GetString("Existing_completed_submissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Existing Subjectivities.
        /// </summary>
        public static string Existing_Subjectivities {
            get {
                return ResourceManager.GetString("Existing_Subjectivities", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expired.
        /// </summary>
        public static string Expired {
            get {
                return ResourceManager.GetString("Expired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expired Assessments.
        /// </summary>
        public static string Expired_assessments {
            get {
                return ResourceManager.GetString("Expired_assessments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expiring Assessments.
        /// </summary>
        public static string Expiring_assessments {
            get {
                return ResourceManager.GetString("Expiring_assessments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expiring Soon.
        /// </summary>
        public static string Expiring_Soon {
            get {
                return ResourceManager.GetString("Expiring_Soon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export.
        /// </summary>
        public static string Export {
            get {
                return ResourceManager.GetString("Export", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export as PDF (Immediately).
        /// </summary>
        public static string ExportAsPdfImmediate {
            get {
                return ResourceManager.GetString("ExportAsPdfImmediate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export as PDF (Fire and forgot).
        /// </summary>
        public static string ExportAsPdfInBackground {
            get {
                return ResourceManager.GetString("ExportAsPdfInBackground", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exporting data may take a while..
        /// </summary>
        public static string Exporting_data_may_take_a_while {
            get {
                return ResourceManager.GetString("Exporting_data_may_take_a_while", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exposure Level.
        /// </summary>
        public static string Exposure_Level {
            get {
                return ResourceManager.GetString("Exposure_Level", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Extensions.
        /// </summary>
        public static string Extensions {
            get {
                return ResourceManager.GetString("Extensions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed.
        /// </summary>
        public static string Failed {
            get {
                return ResourceManager.GetString("Failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FAQ.
        /// </summary>
        public static string FAQ {
            get {
                return ResourceManager.GetString("FAQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Features.
        /// </summary>
        public static string Features {
            get {
                return ResourceManager.GetString("Features", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to February.
        /// </summary>
        public static string February {
            get {
                return ResourceManager.GetString("February", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fermium.
        /// </summary>
        public static string Fermium {
            get {
                return ResourceManager.GetString("Fermium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File is empty..
        /// </summary>
        public static string File_is_empty {
            get {
                return ResourceManager.GetString("File_is_empty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File name.
        /// </summary>
        public static string File_Name {
            get {
                return ResourceManager.GetString("File_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File Rename.
        /// </summary>
        public static string File_Rename {
            get {
                return ResourceManager.GetString("File_Rename", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File Size.
        /// </summary>
        public static string File_Size {
            get {
                return ResourceManager.GetString("File_Size", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File Type.
        /// </summary>
        public static string File_Type {
            get {
                return ResourceManager.GetString("File_Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File upload has been cancelled..
        /// </summary>
        public static string File_upload_has_been_cancelled {
            get {
                return ResourceManager.GetString("File_upload_has_been_cancelled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File Name.
        /// </summary>
        public static string FileName {
            get {
                return ResourceManager.GetString("FileName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Files.
        /// </summary>
        public static string Files {
            get {
                return ResourceManager.GetString("Files", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Financials.
        /// </summary>
        public static string Financials {
            get {
                return ResourceManager.GetString("Financials", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finish.
        /// </summary>
        public static string Finish {
            get {
                return ResourceManager.GetString("Finish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fire in reactor core.
        /// </summary>
        public static string Fire_in_reactor_core {
            get {
                return ResourceManager.GetString("Fire_in_reactor_core", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First.
        /// </summary>
        public static string First {
            get {
                return ResourceManager.GetString("First", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Day of Week.
        /// </summary>
        public static string First_Day_of_Week {
            get {
                return ResourceManager.GetString("First_Day_of_Week", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name.
        /// </summary>
        public static string First_Name {
            get {
                return ResourceManager.GetString("First_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fluent Validation.
        /// </summary>
        public static string Fluent_Validation {
            get {
                return ResourceManager.GetString("Fluent_Validation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fluorine.
        /// </summary>
        public static string Fluorine {
            get {
                return ResourceManager.GetString("Fluorine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Follow Market(s).
        /// </summary>
        public static string Follow_Markets {
            get {
                return ResourceManager.GetString("Follow_Markets", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Follower.
        /// </summary>
        public static string Follower {
            get {
                return ResourceManager.GetString("Follower", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Following.
        /// </summary>
        public static string Following {
            get {
                return ResourceManager.GetString("Following", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to for how to configure a real email sender.
        /// </summary>
        public static string for_how_to_configure_a_real_email_sender {
            get {
                return ResourceManager.GetString("for_how_to_configure_a_real_email_sender", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to for internal self-assessment to view your scores and insights..
        /// </summary>
        public static string For_internal_self_assessment {
            get {
                return ResourceManager.GetString("For_internal_self_assessment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For Organisation.
        /// </summary>
        public static string For_Organisation {
            get {
                return ResourceManager.GetString("For_Organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forename.
        /// </summary>
        public static string Forename {
            get {
                return ResourceManager.GetString("Forename", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forget this browser.
        /// </summary>
        public static string Forget_this_browser {
            get {
                return ResourceManager.GetString("Forget_this_browser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forgot password confirmation.
        /// </summary>
        public static string Forgot_password_confirmation {
            get {
                return ResourceManager.GetString("Forgot_password_confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forgot your password?.
        /// </summary>
        public static string Forgot_your_password {
            get {
                return ResourceManager.GetString("Forgot_your_password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Form Builder.
        /// </summary>
        public static string Form_Builder {
            get {
                return ResourceManager.GetString("Form_Builder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Form name.
        /// </summary>
        public static string Form_name {
            get {
                return ResourceManager.GetString("Form_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Form name is required.
        /// </summary>
        public static string Form_name_is_required {
            get {
                return ResourceManager.GetString("Form_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question page is required.
        /// </summary>
        public static string Form_page_is_required {
            get {
                return ResourceManager.GetString("Form_page_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Form page title is required.
        /// </summary>
        public static string Form_Page_title_is_required {
            get {
                return ResourceManager.GetString("Form_Page_title_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application form removed.
        /// </summary>
        public static string Form_removed {
            get {
                return ResourceManager.GetString("Form_removed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Form title is required.
        /// </summary>
        public static string Form_title_is_required {
            get {
                return ResourceManager.GetString("Form_title_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Form validation errors.
        /// </summary>
        public static string Form_validation_errors {
            get {
                return ResourceManager.GetString("Form_validation_errors", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forms.
        /// </summary>
        public static string Forms {
            get {
                return ResourceManager.GetString("Forms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forms &amp; Files.
        /// </summary>
        public static string Forms_and_files {
            get {
                return ResourceManager.GetString("Forms_and_files", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forum.
        /// </summary>
        public static string Forum {
            get {
                return ResourceManager.GetString("Forum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fossil.
        /// </summary>
        public static string Fossil {
            get {
                return ResourceManager.GetString("Fossil", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Francium.
        /// </summary>
        public static string Francium {
            get {
                return ResourceManager.GetString("Francium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Full DateTime Pattern.
        /// </summary>
        public static string Full_DateTime_Pattern {
            get {
                return ResourceManager.GetString("Full_DateTime_Pattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Full Submission.
        /// </summary>
        public static string Full_Submission {
            get {
                return ResourceManager.GetString("Full_Submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Full Name.
        /// </summary>
        public static string FullName {
            get {
                return ResourceManager.GetString("FullName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gas.
        /// </summary>
        public static string Gas {
            get {
                return ResourceManager.GetString("Gas", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gas.
        /// </summary>
        public static string Gas1 {
            get {
                return ResourceManager.GetString("Gas1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General.
        /// </summary>
        public static string General {
            get {
                return ResourceManager.GetString("General", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Generate.
        /// </summary>
        public static string Generate {
            get {
                return ResourceManager.GetString("Generate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to generate a new set of recovery codes.
        /// </summary>
        public static string generate_a_new_set_of_recovery_codes {
            get {
                return ResourceManager.GetString("generate_a_new_set_of_recovery_codes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Generate Quote Request.
        /// </summary>
        public static string Generate_Quote_Request {
            get {
                return ResourceManager.GetString("Generate_Quote_Request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Generating new recovery codes does not change the keys used in authenticator apps..
        /// </summary>
        public static string Generating_new_recovery_codes_does_not_change_the_keys_used_in_authenticator_apps {
            get {
                return ResourceManager.GetString("Generating_new_recovery_codes_does_not_change_the_keys_used_in_authenticator_apps" +
                        "", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Geothermal.
        /// </summary>
        public static string Geothermal {
            get {
                return ResourceManager.GetString("Geothermal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Germany.
        /// </summary>
        public static string Germany {
            get {
                return ResourceManager.GetString("Germany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Getting Started.
        /// </summary>
        public static string Getting_Started {
            get {
                return ResourceManager.GetString("Getting_Started", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Settings.
        /// </summary>
        public static string Global_Settings {
            get {
                return ResourceManager.GetString("Global_Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Spread.
        /// </summary>
        public static string Global_Spread {
            get {
                return ResourceManager.GetString("Global_Spread", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Variable.
        /// </summary>
        public static string Global_Variable {
            get {
                return ResourceManager.GetString("Global_Variable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Got it!.
        /// </summary>
        public static string Got_it {
            get {
                return ResourceManager.GetString("Got_it", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Graphite on roof.
        /// </summary>
        public static string Graphite_on_roof {
            get {
                return ResourceManager.GetString("Graphite_on_roof", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &gt;50th Percent.
        /// </summary>
        public static string Greater_than_top_50th_percent {
            get {
                return ResourceManager.GetString("Greater_than_top_50th_percent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to has sent you.
        /// </summary>
        public static string has_sent_you {
            get {
                return ResourceManager.GetString("has_sent_you", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to has submitted.
        /// </summary>
        public static string has_submitted {
            get {
                return ResourceManager.GetString("has_submitted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to to have them complete or market your insurance program..
        /// </summary>
        public static string Have_them_market_insurance {
            get {
                return ResourceManager.GetString("Have_them_market_insurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Heads of cover name is required.
        /// </summary>
        public static string Heads_of_cover_name_is_required {
            get {
                return ResourceManager.GetString("Heads_of_cover_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Height.
        /// </summary>
        public static string Height {
            get {
                return ResourceManager.GetString("Height", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hello.
        /// </summary>
        public static string Hello {
            get {
                return ResourceManager.GetString("Hello", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hello World.
        /// </summary>
        public static string Hello_World {
            get {
                return ResourceManager.GetString("Hello_World", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to here.
        /// </summary>
        public static string here {
            get {
                return ResourceManager.GetString("here", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string Hi {
            get {
                return ResourceManager.GetString("Hi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you wish to hide this layer from the Organisation? The Organisation will no longer be able to see the Layer or its contents. This action can be undone at anytime..
        /// </summary>
        public static string Hide_layer_modal_confirmation_text {
            get {
                return ResourceManager.GetString("Hide_layer_modal_confirmation_text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to High.
        /// </summary>
        public static string High {
            get {
                return ResourceManager.GetString("High", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to High: Access to &amp; storage of critical data. Mission critical (1min - 24hrs).
        /// </summary>
        public static string High_Criticality_Info {
            get {
                return ResourceManager.GetString("High_Criticality_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Home.
        /// </summary>
        public static string Home {
            get {
                return ResourceManager.GetString("Home", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hour.
        /// </summary>
        public static string Hour {
            get {
                return ResourceManager.GetString("Hour", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        public static string HQ_Address {
            get {
                return ResourceManager.GetString("HQ_Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to City.
        /// </summary>
        public static string HQ_City {
            get {
                return ResourceManager.GetString("HQ_City", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country.
        /// </summary>
        public static string HQ_Country {
            get {
                return ResourceManager.GetString("HQ_Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HQ Location.
        /// </summary>
        public static string HQ_Location {
            get {
                return ResourceManager.GetString("HQ_Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Postcode/Zipcode.
        /// </summary>
        public static string HQ_Postcode {
            get {
                return ResourceManager.GetString("HQ_Postcode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to hrs.
        /// </summary>
        public static string Hrs {
            get {
                return ResourceManager.GetString("Hrs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hydro.
        /// </summary>
        public static string Hydro {
            get {
                return ResourceManager.GetString("Hydro", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I can&apos;t provide this file.
        /// </summary>
        public static string I_cannot_provide_this_file {
            get {
                return ResourceManager.GetString("I_cannot_provide_this_file", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity.
        /// </summary>
        public static string Identity {
            get {
                return ResourceManager.GetString("Identity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identity Settings.
        /// </summary>
        public static string Identity_Settings {
            get {
                return ResourceManager.GetString("Identity_Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you do not complete your authenticator app configuration you may lose access to your account..
        /// </summary>
        public static string If_you_do_not_complete_your_authenticator_app_configuration_you_may_lose_access_to_your_account {
            get {
                return ResourceManager.GetString("If_you_do_not_complete_your_authenticator_app_configuration_you_may_lose_access_t" +
                        "o_your_account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you do not have access to your device, you can.
        /// </summary>
        public static string If_you_do_not_have_access_to_your_device_you_can {
            get {
                return ResourceManager.GetString("If_you_do_not_have_access_to_your_device_you_can", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you lose your device and don&apos;t have the recovery codes you will lose access to your account.
        /// </summary>
        public static string If_you_lose_your_device_and_dont_have_the_recovery_codes_you_will_lose_access_to_your_account {
            get {
                return ResourceManager.GetString("If_you_lose_your_device_and_dont_have_the_recovery_codes_you_will_lose_access_to_" +
                        "your_account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you reset your authenticator key your authenticator app will not work until you reconfigure it..
        /// </summary>
        public static string If_you_reset_your_authenticator_key_your_authenticator_app_will_not_work_until_you_reconfigure_it {
            get {
                return ResourceManager.GetString("If_you_reset_your_authenticator_key_your_authenticator_app_will_not_work_until_yo" +
                        "u_reconfigure_it", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If you wish to change the key used in an authenticator app you should.
        /// </summary>
        public static string If_you_wish_to_change_the_key_used_in_an_authenticator_app_you_should {
            get {
                return ResourceManager.GetString("If_you_wish_to_change_the_key_used_in_an_authenticator_app_you_should", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ILF.
        /// </summary>
        public static string ILF {
            get {
                return ResourceManager.GetString("ILF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I&apos;m sorry, I can&apos;t display anything until you.
        /// </summary>
        public static string Im_sorry_I_can_t_display_anything_until_you {
            get {
                return ResourceManager.GetString("Im_sorry_I_can\'t_display_anything_until_you", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Processing.
        /// </summary>
        public static string In_Progress {
            get {
                return ResourceManager.GetString("In_Progress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In the aggregate.
        /// </summary>
        public static string In_the_aggregate {
            get {
                return ResourceManager.GetString("In_the_aggregate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In Use.
        /// </summary>
        public static string In_Use {
            get {
                return ResourceManager.GetString("In_Use", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In use by your organisation.
        /// </summary>
        public static string In_use_by_your_organisation {
            get {
                return ResourceManager.GetString("In_use_by_your_organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inactive.
        /// </summary>
        public static string Inactive {
            get {
                return ResourceManager.GetString("Inactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incident and Claims Notifications.
        /// </summary>
        public static string Incident_and_Claim_Notifications {
            get {
                return ResourceManager.GetString("Incident_and_Claim_Notifications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incident in plant number 4.
        /// </summary>
        public static string Incident_in_plant_number_4 {
            get {
                return ResourceManager.GetString("Incident_in_plant_number_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incident Response Panel.
        /// </summary>
        public static string Incident_Response_Panel {
            get {
                return ResourceManager.GetString("Incident_Response_Panel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Both days inclusive at 12:01am LST (Local Standard Time) at the main address of the insured.
        /// </summary>
        public static string Inclusive_note_for_quote {
            get {
                return ResourceManager.GetString("Inclusive_note_for_quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incomplete.
        /// </summary>
        public static string Incomplete {
            get {
                return ResourceManager.GetString("Incomplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incomplete Assessment(s).
        /// </summary>
        public static string Incomplete_Assessments {
            get {
                return ResourceManager.GetString("Incomplete_Assessments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incomplete requests.
        /// </summary>
        public static string Incomplete_Requests {
            get {
                return ResourceManager.GetString("Incomplete_Requests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Increased Limit Factor.
        /// </summary>
        public static string Increased_limit_factor {
            get {
                return ResourceManager.GetString("Increased_limit_factor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Indicated.
        /// </summary>
        public static string Indicated {
            get {
                return ResourceManager.GetString("Indicated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industries.
        /// </summary>
        public static string Industries {
            get {
                return ResourceManager.GetString("Industries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry.
        /// </summary>
        public static string Industry {
            get {
                return ResourceManager.GetString("Industry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry exposure level is required.
        /// </summary>
        public static string Industry_exposure_level_is_required {
            get {
                return ResourceManager.GetString("Industry_exposure_level_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry name is required.
        /// </summary>
        public static string Industry_name_is_required {
            get {
                return ResourceManager.GetString("Industry_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry Revenue Split.
        /// </summary>
        public static string Industry_Revenue_Split {
            get {
                return ResourceManager.GetString("Industry_Revenue_Split", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry Settings.
        /// </summary>
        public static string Industry_Settings {
            get {
                return ResourceManager.GetString("Industry_Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Informational.
        /// </summary>
        public static string Informational_State_Name {
            get {
                return ResourceManager.GetString("Informational_State_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Installation.
        /// </summary>
        public static string Installation {
            get {
                return ResourceManager.GetString("Installation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insured.
        /// </summary>
        public static string Insured {
            get {
                return ResourceManager.GetString("Insured", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurer.
        /// </summary>
        public static string Insurer {
            get {
                return ResourceManager.GetString("Insurer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurer Name.
        /// </summary>
        public static string Insurer_Name {
            get {
                return ResourceManager.GetString("Insurer_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurer quotes.
        /// </summary>
        public static string Insurer_quotes {
            get {
                return ResourceManager.GetString("Insurer_quotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurers.
        /// </summary>
        public static string Insurers {
            get {
                return ResourceManager.GetString("Insurers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurers and brokers will recognize the request by this name.
        /// </summary>
        public static string Insurers_and_brokers_will_recognize_the_request_by_this_name {
            get {
                return ResourceManager.GetString("Insurers_and_brokers_will_recognize_the_request_by_this_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insuring Agreements.
        /// </summary>
        public static string Insuring_Agreements {
            get {
                return ResourceManager.GetString("Insuring_Agreements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Intents.
        /// </summary>
        public static string Intents {
            get {
                return ResourceManager.GetString("Intents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal.
        /// </summary>
        public static string Internal {
            get {
                return ResourceManager.GetString("Internal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal Assessment.
        /// </summary>
        public static string Internal_Assessment {
            get {
                return ResourceManager.GetString("Internal_Assessment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Interpolation Algorithm.
        /// </summary>
        public static string Interpolation_Algorithm {
            get {
                return ResourceManager.GetString("Interpolation_Algorithm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to into your app, spaces and casing do not matter.
        /// </summary>
        public static string into_your_app {
            get {
                return ResourceManager.GetString("into_your_app", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to into your two factor authenticator app. Spaces and casing do not matter..
        /// </summary>
        public static string into_your_two_factor_authenticator_app__Spaces_and_casing_do_not_matter {
            get {
                return ResourceManager.GetString("into_your_two_factor_authenticator_app._Spaces_and_casing_do_not_matter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Data.
        /// </summary>
        public static string Invalid_data {
            get {
                return ResourceManager.GetString("Invalid_data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid file name..
        /// </summary>
        public static string Invalid_file_name {
            get {
                return ResourceManager.GetString("Invalid_file_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Active User.
        /// </summary>
        public static string Is_Active_User {
            get {
                return ResourceManager.GetString("Is_Active_User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Default.
        /// </summary>
        public static string Is_Default {
            get {
                return ResourceManager.GetString("Is_Default", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Deleted.
        /// </summary>
        public static string Is_Deleted {
            get {
                return ResourceManager.GetString("Is_Deleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Confirmed.
        /// </summary>
        public static string Is_Email_Confirmed {
            get {
                return ResourceManager.GetString("Is_Email_Confirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Satisfied.
        /// </summary>
        public static string Is_Satisfied {
            get {
                return ResourceManager.GetString("Is_Satisfied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is shared.
        /// </summary>
        public static string Is_shared {
            get {
                return ResourceManager.GetString("Is_shared", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Static.
        /// </summary>
        public static string Is_Static {
            get {
                return ResourceManager.GetString("Is_Static", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sed vitae hendrerit justo. Aenean eu mi in urna eleifend imperdiet a vel ipsum. Morbi ac risus vel massa tincidunt lacinia. Aliquam augue libero, venenatis sit amet neque non, dignissim interdum elit. Nunc condimentum enim non ante egestas lobortis. Sed ac felis gravida nisi blandit porta. Sed ultrices finibus ullamcorper. Nullam congue malesuada rhoncus. In non varius arcu. Curabitur porta erat id neque fermentum, ut finibus dui venenatis. Fusce tristique cursus pulvinar. Nam consectetur convallis orci, a  [rest of string was truncated]&quot;;.
        /// </summary>
        public static string Ispum {
            get {
                return ResourceManager.GetString("Ispum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Issues rising.
        /// </summary>
        public static string Issues_rising {
            get {
                return ResourceManager.GetString("Issues_rising", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IT/OT Info.
        /// </summary>
        public static string IT_OT_Info {
            get {
                return ResourceManager.GetString("IT_OT_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IT/OT Infrastructure.
        /// </summary>
        public static string IT_OT_Infrastructure {
            get {
                return ResourceManager.GetString("IT_OT_Infrastructure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items per page.
        /// </summary>
        public static string Items_per_page {
            get {
                return ResourceManager.GetString("Items_per_page", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to January.
        /// </summary>
        public static string January {
            get {
                return ResourceManager.GetString("January", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Title.
        /// </summary>
        public static string Job_Title {
            get {
                return ResourceManager.GetString("Job_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Join the Military.
        /// </summary>
        public static string Join_the_Military {
            get {
                return ResourceManager.GetString("Join_the_Military", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to July.
        /// </summary>
        public static string July {
            get {
                return ResourceManager.GetString("July", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to June.
        /// </summary>
        public static string June {
            get {
                return ResourceManager.GetString("June", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Landline.
        /// </summary>
        public static string Landline {
            get {
                return ResourceManager.GetString("Landline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last.
        /// </summary>
        public static string Last {
            get {
                return ResourceManager.GetString("Last", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Modified On.
        /// </summary>
        public static string Last_Modified_On {
            get {
                return ResourceManager.GetString("Last_Modified_On", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Name.
        /// </summary>
        public static string Last_Name {
            get {
                return ResourceManager.GetString("Last_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Year Gross Profit.
        /// </summary>
        public static string Last_Year_Gross_Profit {
            get {
                return ResourceManager.GetString("Last_Year_Gross_Profit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Year Revenue.
        /// </summary>
        public static string Last_Year_Revenue {
            get {
                return ResourceManager.GetString("Last_Year_Revenue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Latest Assessment.
        /// </summary>
        public static string Latest_assessment {
            get {
                return ResourceManager.GetString("Latest_assessment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Latest Submission.
        /// </summary>
        public static string Latest_submission {
            get {
                return ResourceManager.GetString("Latest_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Layer.
        /// </summary>
        public static string Layer {
            get {
                return ResourceManager.GetString("Layer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This document outlines the details of the layer for which a quote has been requested. Please proceed with underwriting and provide your quote(s) in the Quotes tab..
        /// </summary>
        public static string Layer_info_card_subtitle {
            get {
                return ResourceManager.GetString("Layer_info_card_subtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Layer Placement.
        /// </summary>
        public static string Layer_Placement {
            get {
                return ResourceManager.GetString("Layer_Placement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Layer unassigned.
        /// </summary>
        public static string Layer_unassigned {
            get {
                return ResourceManager.GetString("Layer_unassigned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Layers.
        /// </summary>
        public static string Layers {
            get {
                return ResourceManager.GetString("Layers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lead.
        /// </summary>
        public static string Lead {
            get {
                return ResourceManager.GetString("Lead", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lead Insurer.
        /// </summary>
        public static string Lead_Insurer {
            get {
                return ResourceManager.GetString("Lead_Insurer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn how to.
        /// </summary>
        public static string Learn_how_to {
            get {
                return ResourceManager.GetString("Learn_how_to", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Left.
        /// </summary>
        public static string Left {
            get {
                return ResourceManager.GetString("Left", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Legend Position.
        /// </summary>
        public static string Legend_Position {
            get {
                return ResourceManager.GetString("Legend_Position", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Liability agreement.
        /// </summary>
        public static string Liability_agreement {
            get {
                return ResourceManager.GetString("Liability_agreement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Limit.
        /// </summary>
        public static string Limit {
            get {
                return ResourceManager.GetString("Limit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Limit of liability.
        /// </summary>
        public static string Limit_of_liability {
            get {
                return ResourceManager.GetString("Limit_of_liability", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Limits of Liability.
        /// </summary>
        public static string Limits_of_liability {
            get {
                return ResourceManager.GetString("Limits_of_liability", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line.
        /// </summary>
        public static string Line {
            get {
                return ResourceManager.GetString("Line", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line size.
        /// </summary>
        public static string Line_Size {
            get {
                return ResourceManager.GetString("Line_Size", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Size exceeds the maximum allowed limit.
        /// </summary>
        public static string Line_Size_exceeds_limit {
            get {
                return ResourceManager.GetString("Line_Size_exceeds_limit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Size is required.
        /// </summary>
        public static string Line_Size_is_required {
            get {
                return ResourceManager.GetString("Line_Size_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line size limit.
        /// </summary>
        public static string Line_size_limit {
            get {
                return ResourceManager.GetString("Line_size_limit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Size must be greater than 0.
        /// </summary>
        public static string Line_Size_must_be_greater_than_0 {
            get {
                return ResourceManager.GetString("Line_Size_must_be_greater_than_0", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Size (Orig Currency).
        /// </summary>
        public static string Line_Size_Orig_Currency {
            get {
                return ResourceManager.GetString("Line_Size_Orig_Currency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Live data powered by SignalR.
        /// </summary>
        public static string Live_data_powered_by_SignalR {
            get {
                return ResourceManager.GetString("Live_data_powered_by_SignalR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading....
        /// </summary>
        public static string Loading {
            get {
                return ResourceManager.GetString("Loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading on Demand.
        /// </summary>
        public static string Loading_On_Demand {
            get {
                return ResourceManager.GetString("Loading_On_Demand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Local Time.
        /// </summary>
        public static string Local_Time {
            get {
                return ResourceManager.GetString("Local_Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Localization.
        /// </summary>
        public static string Localization {
            get {
                return ResourceManager.GetString("Localization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Locations.
        /// </summary>
        public static string Locations {
            get {
                return ResourceManager.GetString("Locations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lockout Settings.
        /// </summary>
        public static string Lockout_Settings {
            get {
                return ResourceManager.GetString("Lockout_Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log in.
        /// </summary>
        public static string Log_in {
            get {
                return ResourceManager.GetString("Log_in", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to log in with a recovery code.
        /// </summary>
        public static string log_in_with_a_recovery_code {
            get {
                return ResourceManager.GetString("log_in_with_a_recovery_code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login.
        /// </summary>
        public static string Login {
            get {
                return ResourceManager.GetString("Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to login.
        /// </summary>
        public static string login_lowered {
            get {
                return ResourceManager.GetString("login_lowered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login out....
        /// </summary>
        public static string Login_out {
            get {
                return ResourceManager.GetString("Login_out", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login to your account.
        /// </summary>
        public static string Login_to_your_account {
            get {
                return ResourceManager.GetString("Login_to_your_account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login with Authenticator Code.
        /// </summary>
        public static string Login_With_Authenticator_Code {
            get {
                return ResourceManager.GetString("Login_With_Authenticator_Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login with Recovery Code.
        /// </summary>
        public static string Login_With_Recovery_Code {
            get {
                return ResourceManager.GetString("Login_With_Recovery_Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Logout.
        /// </summary>
        public static string Logout {
            get {
                return ResourceManager.GetString("Logout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Long Date Pattern.
        /// </summary>
        public static string Long_Date_Pattern {
            get {
                return ResourceManager.GetString("Long_Date_Pattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Long Time Pattern.
        /// </summary>
        public static string Long_Time_Pattern {
            get {
                return ResourceManager.GetString("Long_Time_Pattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mauris cursus dui in tellus gravida, ut iaculis ante mattis. Aenean blandit faucibus finibus. Aenean euismod lorem metus. Ut vitae nulla ac mauris vulputate accumsan ac non velit. Cras at tincidunt augue, quis eleifend est. Quisque magna diam, elementum ut pellentesque vitae, porttitor quis quam. Praesent vulputate varius ex et suscipit. Praesent imperdiet facilisis nisi non sodales. Nullam sit amet lorem quis nisi pellentesque ornare. Pellentesque maximus sed urna vel euismod. In egestas odio id est gravid [rest of string was truncated]&quot;;.
        /// </summary>
        public static string Lorem {
            get {
                return ResourceManager.GetString("Lorem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loss type is required.
        /// </summary>
        public static string Loss_type_is_required {
            get {
                return ResourceManager.GetString("Loss_type_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loss Types.
        /// </summary>
        public static string Loss_Types {
            get {
                return ResourceManager.GetString("Loss_Types", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Low.
        /// </summary>
        public static string Low {
            get {
                return ResourceManager.GetString("Low", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Low: No direct access to critical data. Not mission critical (5dys or more).
        /// </summary>
        public static string Low_Criticality_Info {
            get {
                return ResourceManager.GetString("Low_Criticality_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Contact.
        /// </summary>
        public static string Main_Contact {
            get {
                return ResourceManager.GetString("Main_Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Contact Email.
        /// </summary>
        public static string Main_Contact_Email {
            get {
                return ResourceManager.GetString("Main_Contact_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage Brokers.
        /// </summary>
        public static string Manage_Brokers {
            get {
                return ResourceManager.GetString("Manage_Brokers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage Documents.
        /// </summary>
        public static string Manage_Documents {
            get {
                return ResourceManager.GetString("Manage_Documents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage Products.
        /// </summary>
        public static string Manage_Products {
            get {
                return ResourceManager.GetString("Manage_Products", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage Underwriters.
        /// </summary>
        public static string Manage_Underwriters {
            get {
                return ResourceManager.GetString("Manage_Underwriters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage Users.
        /// </summary>
        public static string Manager_Users {
            get {
                return ResourceManager.GetString("Manager_Users", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to March.
        /// </summary>
        public static string March {
            get {
                return ResourceManager.GetString("March", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mark all as read.
        /// </summary>
        public static string Mark_all_as_read {
            get {
                return ResourceManager.GetString("Mark_all_as_read", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The file is in use and will be marked as deleted, but will remain in the system. Do you want to continue?.
        /// </summary>
        public static string Mark_File_Deleted_Confirm_Message {
            get {
                return ResourceManager.GetString("Mark_File_Deleted_Confirm_Message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master-Details Form.
        /// </summary>
        public static string Master_Details_Form {
            get {
                return ResourceManager.GetString("Master_Details_Form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maturity.
        /// </summary>
        public static string Maturity {
            get {
                return ResourceManager.GetString("Maturity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max file size.
        /// </summary>
        public static string Max_file_size {
            get {
                return ResourceManager.GetString("Max_file_size", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max Line Size.
        /// </summary>
        public static string Max_Line_Size {
            get {
                return ResourceManager.GetString("Max_Line_Size", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum Failed Access Attempts.
        /// </summary>
        public static string Maximum_Failed_Access_Attempts {
            get {
                return ResourceManager.GetString("Maximum_Failed_Access_Attempts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum line size the follow market can contribute.
        /// </summary>
        public static string Maximum_line_size_tooltip {
            get {
                return ResourceManager.GetString("Maximum_line_size_tooltip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to May.
        /// </summary>
        public static string May {
            get {
                return ResourceManager.GetString("May", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Medium.
        /// </summary>
        public static string Medium {
            get {
                return ResourceManager.GetString("Medium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Medium: Access to critical data. Partially mission critical (24hrs - 5dys).
        /// </summary>
        public static string Medium_Criticality_Info {
            get {
                return ResourceManager.GetString("Medium_Criticality_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Military Applicants.
        /// </summary>
        public static string Military_Applicants {
            get {
                return ResourceManager.GetString("Military_Applicants", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minor.
        /// </summary>
        public static string Minor {
            get {
                return ResourceManager.GetString("Minor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minutes.
        /// </summary>
        public static string Minute {
            get {
                return ResourceManager.GetString("Minute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mobile.
        /// </summary>
        public static string Mobile {
            get {
                return ResourceManager.GetString("Mobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Modified by Broker.
        /// </summary>
        public static string Modified_By_Broker {
            get {
                return ResourceManager.GetString("Modified_By_Broker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Modification Date.
        /// </summary>
        public static string Modified_On {
            get {
                return ResourceManager.GetString("Modified_On", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Modify Brokers and Underwriters.
        /// </summary>
        public static string Modify_Brokers_And_Underwriters {
            get {
                return ResourceManager.GetString("Modify_Brokers_And_Underwriters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Modify your MFA settings.
        /// </summary>
        public static string Modify_Your_Mfa_Settings {
            get {
                return ResourceManager.GetString("Modify_Your_Mfa_Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Month.
        /// </summary>
        public static string Month {
            get {
                return ResourceManager.GetString("Month", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Month Day Pattern.
        /// </summary>
        public static string Month_Day_Pattern {
            get {
                return ResourceManager.GetString("Month_Day_Pattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Month Genitive Names.
        /// </summary>
        public static string Month_Genitive_Names {
            get {
                return ResourceManager.GetString("Month_Genitive_Names", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Month Names.
        /// </summary>
        public static string Month_Names {
            get {
                return ResourceManager.GetString("Month_Names", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Most recent assessments to client.
        /// </summary>
        public static string Most_recent_assessment_to_client {
            get {
                return ResourceManager.GetString("Most_recent_assessment_to_client", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Most recently modified assessments.
        /// </summary>
        public static string Most_recently_modified_assessments {
            get {
                return ResourceManager.GetString("Most_recently_modified_assessments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Account.
        /// </summary>
        public static string My_Account {
            get {
                return ResourceManager.GetString("My_Account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Brokers.
        /// </summary>
        public static string My_Brokers {
            get {
                return ResourceManager.GetString("My_Brokers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Claims.
        /// </summary>
        public static string My_Claims {
            get {
                return ResourceManager.GetString("My_Claims", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Clients.
        /// </summary>
        public static string My_Clients {
            get {
                return ResourceManager.GetString("My_Clients", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Control Assessments.
        /// </summary>
        public static string My_Control_Assessments {
            get {
                return ResourceManager.GetString("My_Control_Assessments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Eyes Only.
        /// </summary>
        public static string My_Eyes_Only {
            get {
                return ResourceManager.GetString("My_Eyes_Only", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Organisations.
        /// </summary>
        public static string My_Organisations {
            get {
                return ResourceManager.GetString("My_Organisations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Permissions.
        /// </summary>
        public static string My_Permissions {
            get {
                return ResourceManager.GetString("My_Permissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Products.
        /// </summary>
        public static string My_Products {
            get {
                return ResourceManager.GetString("My_Products", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Profile.
        /// </summary>
        public static string My_Profile {
            get {
                return ResourceManager.GetString("My_Profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Profile Picture.
        /// </summary>
        public static string My_Profile_Picture {
            get {
                return ResourceManager.GetString("My_Profile_Picture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Quote Requests.
        /// </summary>
        public static string My_Quote_Requests {
            get {
                return ResourceManager.GetString("My_Quote_Requests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Submissions.
        /// </summary>
        public static string My_Submissions {
            get {
                return ResourceManager.GetString("My_Submissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Forms.
        /// </summary>
        public static string MyForms {
            get {
                return ResourceManager.GetString("MyForms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Native Calendar Name.
        /// </summary>
        public static string Native_Calendar_Name {
            get {
                return ResourceManager.GetString("Native_Calendar_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Native Name.
        /// </summary>
        public static string Native_Name {
            get {
                return ResourceManager.GetString("Native_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Natural Spline.
        /// </summary>
        public static string Natural_Spline {
            get {
                return ResourceManager.GetString("Natural_Spline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Navigate to Broker.
        /// </summary>
        public static string Navigate_to_Broker {
            get {
                return ResourceManager.GetString("Navigate_to_Broker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Email.
        /// </summary>
        public static string New_Email {
            get {
                return ResourceManager.GetString("New_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Registered Users are Active by Default.
        /// </summary>
        public static string New_Registered_Users_are_Active_by_Default {
            get {
                return ResourceManager.GetString("New_Registered_Users_are_Active_by_Default", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Request.
        /// </summary>
        public static string New_Request {
            get {
                return ResourceManager.GetString("New_Request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next.
        /// </summary>
        public static string Next {
            get {
                return ResourceManager.GetString("Next", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next Year Projected Revenue.
        /// </summary>
        public static string Next_Year_Projected_Revenue {
            get {
                return ResourceManager.GetString("Next_Year_Projected_Revenue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        public static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No agreed option found.
        /// </summary>
        public static string No_agreed_option_found {
            get {
                return ResourceManager.GetString("No_agreed_option_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No agreed option found for this layer.
        /// </summary>
        public static string No_agreed_option_found_for_this_layer {
            get {
                return ResourceManager.GetString("No_agreed_option_found_for_this_layer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No broker has access to your most recent control assessment.
        /// </summary>
        public static string No_broker_has_access_to_your_most_recent_control_assessment {
            get {
                return ResourceManager.GetString("No_broker_has_access_to_your_most_recent_control_assessment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No brokers available.
        /// </summary>
        public static string No_brokers_available {
            get {
                return ResourceManager.GetString("No_brokers_available", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No brokers selected.
        /// </summary>
        public static string No_brokers_selected {
            get {
                return ResourceManager.GetString("No_brokers_selected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No broking house selected.
        /// </summary>
        public static string No_broking_house_selected {
            get {
                return ResourceManager.GetString("No_broking_house_selected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No codes available.
        /// </summary>
        public static string No_codes_available {
            get {
                return ResourceManager.GetString("No_codes_available", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Data.
        /// </summary>
        public static string No_Data {
            get {
                return ResourceManager.GetString("No_Data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No data available..
        /// </summary>
        public static string No_data_available {
            get {
                return ResourceManager.GetString("No_data_available", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No files requested.
        /// </summary>
        public static string No_files_requested {
            get {
                return ResourceManager.GetString("No_files_requested", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No follow markets have been added yet.
        /// </summary>
        public static string No_follow_markets {
            get {
                return ResourceManager.GetString("No_follow_markets", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No form versions selected.
        /// </summary>
        public static string No_form_versions_selected {
            get {
                return ResourceManager.GetString("No_form_versions_selected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No forms requested.
        /// </summary>
        public static string No_forms_requested {
            get {
                return ResourceManager.GetString("No_forms_requested", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No, keep it.
        /// </summary>
        public static string No_keep_it {
            get {
                return ResourceManager.GetString("No_keep_it", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No notifications found.
        /// </summary>
        public static string No_notifications_found {
            get {
                return ResourceManager.GetString("No_notifications_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No. of Data Centres.
        /// </summary>
        public static string No_of_Data_Centres {
            get {
                return ResourceManager.GetString("No_of_Data_Centres", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No options selected.
        /// </summary>
        public static string No_options_selected {
            get {
                return ResourceManager.GetString("No_options_selected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No organisation selected.
        /// </summary>
        public static string No_organisation_selected {
            get {
                return ResourceManager.GetString("No_organisation_selected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No program stack data available for this submission..
        /// </summary>
        public static string No_program_stack_info {
            get {
                return ResourceManager.GetString("No_program_stack_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add a Quote Request to send to Brokers and Underwriters.
        /// </summary>
        public static string No_quote_requests_available {
            get {
                return ResourceManager.GetString("No_quote_requests_available", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No quote requests available.
        /// </summary>
        public static string No_quote_requests_available_organisation {
            get {
                return ResourceManager.GetString("No_quote_requests_available_organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No quotes available.
        /// </summary>
        public static string No_quotes_available {
            get {
                return ResourceManager.GetString("No_quotes_available", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No selected options in layer.
        /// </summary>
        public static string No_selected_options_in_layer {
            get {
                return ResourceManager.GetString("No_selected_options_in_layer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Suppliers added.
        /// </summary>
        public static string No_suppliers_added {
            get {
                return ResourceManager.GetString("No_suppliers_added", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No underwriter has access to your most recent control assessment.
        /// </summary>
        public static string No_underwriter_has_access_to_your_most_recent_control_assessment {
            get {
                return ResourceManager.GetString("No_underwriter_has_access_to_your_most_recent_control_assessment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Normally this would be emailed.
        /// </summary>
        public static string Normally_this_would_be_emailed {
            get {
                return ResourceManager.GetString("Normally_this_would_be_emailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Active.
        /// </summary>
        public static string Not_Active {
            get {
                return ResourceManager.GetString("Not_Active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to N/A.
        /// </summary>
        public static string Not_Available {
            get {
                return ResourceManager.GetString("Not_Available", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Completed.
        /// </summary>
        public static string Not_Completed {
            get {
                return ResourceManager.GetString("Not_Completed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Covered.
        /// </summary>
        public static string Not_Covered {
            get {
                return ResourceManager.GetString("Not_Covered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not in use by your organisation.
        /// </summary>
        public static string Not_in_use_by_your_organisation {
            get {
                return ResourceManager.GetString("Not_in_use_by_your_organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Registered.
        /// </summary>
        public static string Not_Registered {
            get {
                return ResourceManager.GetString("Not_Registered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Started.
        /// </summary>
        public static string Not_Started {
            get {
                return ResourceManager.GetString("Not_Started", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to N/A.
        /// </summary>
        public static string NotApplicable {
            get {
                return ResourceManager.GetString("NotApplicable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notifications.
        /// </summary>
        public static string Notifications {
            get {
                return ResourceManager.GetString("Notifications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to November.
        /// </summary>
        public static string November {
            get {
                return ResourceManager.GetString("November", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nuclear.
        /// </summary>
        public static string Nuclear {
            get {
                return ResourceManager.GetString("Nuclear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Biometric Records.
        /// </summary>
        public static string Number_of_Biometric_Records {
            get {
                return ResourceManager.GetString("Number_of_Biometric_Records", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Contracts.
        /// </summary>
        public static string Number_of_Contracts {
            get {
                return ResourceManager.GetString("Number_of_Contracts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Endpoints.
        /// </summary>
        public static string Number_of_Endpoints {
            get {
                return ResourceManager.GetString("Number_of_Endpoints", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Incomplete Submissions.
        /// </summary>
        public static string Number_of_Incomplete_Submissions {
            get {
                return ResourceManager.GetString("Number_of_Incomplete_Submissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Payment Card Information (PCI) Records.
        /// </summary>
        public static string Number_of_Payment_Card_Information_Records {
            get {
                return ResourceManager.GetString("Number_of_Payment_Card_Information_Records", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Personal Health Information (PHI) Records.
        /// </summary>
        public static string Number_of_Personal_Health_Information_Records {
            get {
                return ResourceManager.GetString("Number_of_Personal_Health_Information_Records", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Personal Identifiable Information (PII) Records.
        /// </summary>
        public static string Number_of_Personal_identifiable_information_records {
            get {
                return ResourceManager.GetString("Number_of_Personal_identifiable_information_records", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Servers.
        /// </summary>
        public static string Number_of_Servers {
            get {
                return ResourceManager.GetString("Number_of_Servers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Submissions.
        /// </summary>
        public static string Number_of_Submissions {
            get {
                return ResourceManager.GetString("Number_of_Submissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Occupational injury.
        /// </summary>
        public static string Occupational_injury {
            get {
                return ResourceManager.GetString("Occupational_injury", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to October.
        /// </summary>
        public static string October {
            get {
                return ResourceManager.GetString("October", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oil.
        /// </summary>
        public static string Oil {
            get {
                return ResourceManager.GetString("Oil", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oil.
        /// </summary>
        public static string Oil1 {
            get {
                return ResourceManager.GetString("Oil1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On-Premise File Storage.
        /// </summary>
        public static string On_Premise_File_Storage {
            get {
                return ResourceManager.GetString("On_Premise_File_Storage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Once your submission is complete, you can.
        /// </summary>
        public static string Once_submission_is_complete {
            get {
                return ResourceManager.GetString("Once_submission_is_complete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Once you have scanned the QR code or input the key above.
        /// </summary>
        public static string Once_you_have_scanned_the_QR_code_or_input_the_key_above {
            get {
                return ResourceManager.GetString("Once_you_have_scanned_the_QR_code_or_input_the_key_above", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oops... something bad happened.
        /// </summary>
        public static string Oops_something_bad_happened {
            get {
                return ResourceManager.GetString("Oops_something_bad_happened", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open.
        /// </summary>
        public static string Open {
            get {
                return ResourceManager.GetString("Open", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open and Submit.
        /// </summary>
        public static string Open_And_Submit {
            get {
                return ResourceManager.GetString("Open_And_Submit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open assessment.
        /// </summary>
        public static string Open_assessment {
            get {
                return ResourceManager.GetString("Open_assessment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Client.
        /// </summary>
        public static string Open_client {
            get {
                return ResourceManager.GetString("Open_client", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Org Requests.
        /// </summary>
        public static string Open_Organisation_Requests {
            get {
                return ResourceManager.GetString("Open_Organisation_Requests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Supplier.
        /// </summary>
        public static string Open_Supplier {
            get {
                return ResourceManager.GetString("Open_Supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Option.
        /// </summary>
        public static string Option {
            get {
                return ResourceManager.GetString("Option", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Options.
        /// </summary>
        public static string Options {
            get {
                return ResourceManager.GetString("Options", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to or.
        /// </summary>
        public static string or {
            get {
                return ResourceManager.GetString("or", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Org Admin.
        /// </summary>
        public static string Org_Admin {
            get {
                return ResourceManager.GetString("Org_Admin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Organisation Admin will be able to add other Organisation Admins and other Organisation Users. The Admin will be the first user on the organisation’s account.
        /// </summary>
        public static string Org_Admin_Explanation {
            get {
                return ResourceManager.GetString("Org_Admin_Explanation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Org Profile.
        /// </summary>
        public static string Org_Profile {
            get {
                return ResourceManager.GetString("Org_Profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation.
        /// </summary>
        public static string Organisation {
            get {
                return ResourceManager.GetString("Organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation Mgt.
        /// </summary>
        public static string Organisation_Mgt {
            get {
                return ResourceManager.GetString("Organisation_Mgt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation name.
        /// </summary>
        public static string Organisation_name {
            get {
                return ResourceManager.GetString("Organisation_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation name cannot be null.
        /// </summary>
        public static string Organisation_name_cannot_be_null {
            get {
                return ResourceManager.GetString("Organisation_name_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation Profile.
        /// </summary>
        public static string Organisation_Profile {
            get {
                return ResourceManager.GetString("Organisation_Profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation Profile Complete.
        /// </summary>
        public static string Organisation_Profile_Complete {
            get {
                return ResourceManager.GetString("Organisation_Profile_Complete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation Request.
        /// </summary>
        public static string Organisation_request {
            get {
                return ResourceManager.GetString("Organisation_request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation Requests.
        /// </summary>
        public static string Organisation_Requests {
            get {
                return ResourceManager.GetString("Organisation_Requests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation Revenue.
        /// </summary>
        public static string Organisation_Revenue {
            get {
                return ResourceManager.GetString("Organisation_Revenue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation Revenue Split.
        /// </summary>
        public static string Organisation_Revenue_Split {
            get {
                return ResourceManager.GetString("Organisation_Revenue_Split", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation Submission.
        /// </summary>
        public static string Organisation_Submission {
            get {
                return ResourceManager.GetString("Organisation_Submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation Submissions.
        /// </summary>
        public static string Organisation_Submissions {
            get {
                return ResourceManager.GetString("Organisation_Submissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Org Submissions This Month.
        /// </summary>
        public static string Organisation_Submissions_This_Month {
            get {
                return ResourceManager.GetString("Organisation_Submissions_This_Month", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unprocessed.
        /// </summary>
        public static string Organisation_Unprocessed {
            get {
                return ResourceManager.GetString("Organisation_Unprocessed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation Information.
        /// </summary>
        public static string Organisational_Information {
            get {
                return ResourceManager.GetString("Organisational_Information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisation Name.
        /// </summary>
        public static string OrganisationName {
            get {
                return ResourceManager.GetString("OrganisationName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Organisations.
        /// </summary>
        public static string Organisations {
            get {
                return ResourceManager.GetString("Organisations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to out of.
        /// </summary>
        public static string out_of {
            get {
                return ResourceManager.GetString("out_of", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Overview.
        /// </summary>
        public static string Overview {
            get {
                return ResourceManager.GetString("Overview", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part.
        /// </summary>
        public static string Part {
            get {
                return ResourceManager.GetString("Part", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password.
        /// </summary>
        public static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password Settings.
        /// </summary>
        public static string Password_Settings {
            get {
                return ResourceManager.GetString("Password_Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pending.
        /// </summary>
        public static string Pending {
            get {
                return ResourceManager.GetString("Pending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pending Broker Request(s).
        /// </summary>
        public static string Pending_Broker_Requests {
            get {
                return ResourceManager.GetString("Pending_Broker_Requests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Showing your 25 requests with the nearest due dates across all organisations that are still awaiting a response.
        /// </summary>
        public static string Pending_quotes_card_subtitle {
            get {
                return ResourceManager.GetString("Pending_quotes_card_subtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Percentage.
        /// </summary>
        public static string Percent_Invalid {
            get {
                return ResourceManager.GetString("Percent_Invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Periodic.
        /// </summary>
        public static string Periodic {
            get {
                return ResourceManager.GetString("Periodic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Permissions.
        /// </summary>
        public static string Permissions {
            get {
                return ResourceManager.GetString("Permissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Personal Data.
        /// </summary>
        public static string Personal_Data {
            get {
                return ResourceManager.GetString("Personal_Data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone Number.
        /// </summary>
        public static string Phone_Number {
            get {
                return ResourceManager.GetString("Phone_Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone number is invalid.
        /// </summary>
        public static string Phone_number_is_invalid {
            get {
                return ResourceManager.GetString("Phone_number_is_invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Phone number is required..
        /// </summary>
        public static string Phone_number_is_required {
            get {
                return ResourceManager.GetString("Phone_number_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Placed.
        /// </summary>
        public static string Placed {
            get {
                return ResourceManager.GetString("Placed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Play.
        /// </summary>
        public static string Play {
            get {
                return ResourceManager.GetString("Play", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please.
        /// </summary>
        public static string Please {
            get {
                return ResourceManager.GetString("Please", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please add a page to the form.
        /// </summary>
        public static string Please_add_a_page_to_the_form {
            get {
                return ResourceManager.GetString("Please_add_a_page_to_the_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please add a question to your form.
        /// </summary>
        public static string Please_add_a_question {
            get {
                return ResourceManager.GetString("Please_add_a_question", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please add insurers and brokers.
        /// </summary>
        public static string Please_add_insurers_and_brokers {
            get {
                return ResourceManager.GetString("Please_add_insurers_and_brokers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please check your email to confirm your account..
        /// </summary>
        public static string Please_check_your_email_to_confirm_your_account {
            get {
                return ResourceManager.GetString("Please_check_your_email_to_confirm_your_account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please check your email to reset your password.
        /// </summary>
        public static string Please_check_your_email_to_reset_your_password {
            get {
                return ResourceManager.GetString("Please_check_your_email_to_reset_your_password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter Submission name.
        /// </summary>
        public static string Please_enter_submission_name {
            get {
                return ResourceManager.GetString("Please_enter_submission_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please fill in all the information.
        /// </summary>
        public static string Please_fill_in_all_information {
            get {
                return ResourceManager.GetString("Please_fill_in_all_information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please make sure all options are added to choice array.
        /// </summary>
        public static string Please_make_sure_all_options_are_added_to_choice_array {
            get {
                return ResourceManager.GetString("Please_make_sure_all_options_are_added_to_choice_array", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please make sure your organisation&apos;s profile is up-to-date before signing off on your submission..
        /// </summary>
        public static string Please_make_sure_your_Organisation_profile_is_up_to_date {
            get {
                return ResourceManager.GetString("Please_make_sure_your_Organisation_profile_is_up_to_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please provide a reason for declining this quota share..
        /// </summary>
        public static string Please_provide_a_reason_for_declining_this_quota_share {
            get {
                return ResourceManager.GetString("Please_provide_a_reason_for_declining_this_quota_share", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please provide a value greater than 0.
        /// </summary>
        public static string Please_provide_a_value_greater_than_zero {
            get {
                return ResourceManager.GetString("Please_provide_a_value_greater_than_zero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please provide new name for the file or leave it as is..
        /// </summary>
        public static string Please_provide_new_name_for_the_file_or_leave_it_as_is {
            get {
                return ResourceManager.GetString("Please_provide_new_name_for_the_file_or_leave_it_as_is", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please provide a product name.
        /// </summary>
        public static string Please_provide_product_name {
            get {
                return ResourceManager.GetString("Please_provide_product_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please provide a version or model number.
        /// </summary>
        public static string Please_provide_verison_number {
            get {
                return ResourceManager.GetString("Please_provide_verison_number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review and complete all required forms, upload supporting documents and add supplier details as needed..
        /// </summary>
        public static string Please_review_and_complete_all_required_data {
            get {
                return ResourceManager.GetString("Please_review_and_complete_all_required_data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review and complete all required forms, upload supporting documents, and add supplier details as needed.
        ///Once your submission is complete, you can:
        ///Submit to a Broker - for them to review and finalise your insurance program.
        ///Submit for internal use - to view your scores and insights. You can also duplicate the submission later if you want to send a refined version to a Broker..
        /// </summary>
        public static string Please_review_and_complete_submission_details {
            get {
                return ResourceManager.GetString("Please_review_and_complete_submission_details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a domain.
        /// </summary>
        public static string Please_select_a_domain {
            get {
                return ResourceManager.GetString("Please_select_a_domain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a role.
        /// </summary>
        public static string Please_select_a_role {
            get {
                return ResourceManager.GetString("Please_select_a_role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select an application form.
        /// </summary>
        public static string Please_select_an_application_form {
            get {
                return ResourceManager.GetString("Please_select_an_application_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select an organisation.
        /// </summary>
        public static string Please_select_an_organisation {
            get {
                return ResourceManager.GetString("Please_select_an_organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select at least one application form.
        /// </summary>
        public static string Please_select_at_least_one_application_form {
            get {
                return ResourceManager.GetString("Please_select_at_least_one_application_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select products that the Supplier has added.
        /// </summary>
        public static string Please_select_products_for_supplier {
            get {
                return ResourceManager.GetString("Please_select_products_for_supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PM Designator.
        /// </summary>
        public static string PM_Designator {
            get {
                return ResourceManager.GetString("PM_Designator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policies.
        /// </summary>
        public static string Policies {
            get {
                return ResourceManager.GetString("Policies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy Aggregate Limit of Liability.
        /// </summary>
        public static string Policy_aggregate_limit {
            get {
                return ResourceManager.GetString("Policy_aggregate_limit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy Aggregate Limit of Liability.
        /// </summary>
        public static string Policy_Aggregate_Limit_Of_Liability {
            get {
                return ResourceManager.GetString("Policy_Aggregate_Limit_Of_Liability", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy End.
        /// </summary>
        public static string Policy_End {
            get {
                return ResourceManager.GetString("Policy_End", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy form name is required.
        /// </summary>
        public static string Policy_form_name_is_required {
            get {
                return ResourceManager.GetString("Policy_form_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy forms.
        /// </summary>
        public static string Policy_forms {
            get {
                return ResourceManager.GetString("Policy_forms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy Intents.
        /// </summary>
        public static string Policy_Intents {
            get {
                return ResourceManager.GetString("Policy_Intents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy Period.
        /// </summary>
        public static string Policy_Period {
            get {
                return ResourceManager.GetString("Policy_Period", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy Start.
        /// </summary>
        public static string Policy_Start {
            get {
                return ResourceManager.GetString("Policy_Start", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy Wording.
        /// </summary>
        public static string Policy_Wording {
            get {
                return ResourceManager.GetString("Policy_Wording", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to change the Policy Wording? Any options created will be lost.
        /// </summary>
        public static string Policy_wording_change_confirmation_text {
            get {
                return ResourceManager.GetString("Policy_wording_change_confirmation_text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy wording is required.
        /// </summary>
        public static string Policy_wording_is_required {
            get {
                return ResourceManager.GetString("Policy_wording_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Policy wording not selected.
        /// </summary>
        public static string Policy_wording_not_selected {
            get {
                return ResourceManager.GetString("Policy_wording_not_selected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Position.
        /// </summary>
        public static string Position {
            get {
                return ResourceManager.GetString("Position", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Premium.
        /// </summary>
        public static string Premium {
            get {
                return ResourceManager.GetString("Premium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Premiums (Orig Currency).
        /// </summary>
        public static string Premiums_Orig_Currency {
            get {
                return ResourceManager.GetString("Premiums_Orig_Currency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Previous.
        /// </summary>
        public static string Previous {
            get {
                return ResourceManager.GetString("Previous", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Primary.
        /// </summary>
        public static string Primary {
            get {
                return ResourceManager.GetString("Primary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Primary Broker.
        /// </summary>
        public static string Primary_Broker {
            get {
                return ResourceManager.GetString("Primary_Broker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Primary Lead.
        /// </summary>
        public static string Primary_Lead {
            get {
                return ResourceManager.GetString("Primary_Lead", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private.
        /// </summary>
        public static string Private {
            get {
                return ResourceManager.GetString("Private", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Processed.
        /// </summary>
        public static string Processed {
            get {
                return ResourceManager.GetString("Processed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The product is in use and cannot be modified.
        /// </summary>
        public static string Product_in_use {
            get {
                return ResourceManager.GetString("Product_in_use", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product is in use and cannot be modified.
        /// </summary>
        public static string Product_in_use_cannot_be_modified {
            get {
                return ResourceManager.GetString("Product_in_use_cannot_be_modified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Name.
        /// </summary>
        public static string Product_name {
            get {
                return ResourceManager.GetString("Product_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Product name and version is already in use.
        /// </summary>
        public static string Product_name_and_version_in_use {
            get {
                return ResourceManager.GetString("Product_name_and_version_in_use", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product/Service.
        /// </summary>
        public static string Product_Service {
            get {
                return ResourceManager.GetString("Product_Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product/Services.
        /// </summary>
        public static string Product_Services {
            get {
                return ResourceManager.GetString("Product_Services", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Products.
        /// </summary>
        public static string Products {
            get {
                return ResourceManager.GetString("Products", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profile.
        /// </summary>
        public static string Profile {
            get {
                return ResourceManager.GetString("Profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profile Picture.
        /// </summary>
        public static string Profile_Picture {
            get {
                return ResourceManager.GetString("Profile_Picture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Program Stack.
        /// </summary>
        public static string Program_Stack {
            get {
                return ResourceManager.GetString("Program_Stack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Progress.
        /// </summary>
        public static string Progress {
            get {
                return ResourceManager.GetString("Progress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Proof of Concepts.
        /// </summary>
        public static string Proof_of_Concepts {
            get {
                return ResourceManager.GetString("Proof_of_Concepts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Properties.
        /// </summary>
        public static string Properties {
            get {
                return ResourceManager.GetString("Properties", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Property.
        /// </summary>
        public static string Property {
            get {
                return ResourceManager.GetString("Property", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Public.
        /// </summary>
        public static string Public {
            get {
                return ResourceManager.GetString("Public", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Q&amp;A.
        /// </summary>
        public static string QA {
            get {
                return ResourceManager.GetString("QA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search Criteria.
        /// </summary>
        public static string QueryString {
            get {
                return ResourceManager.GetString("QueryString", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question is required.
        /// </summary>
        public static string Question_is_required {
            get {
                return ResourceManager.GetString("Question_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question is waiting for approval.
        /// </summary>
        public static string Question_is_waiting_for_approval {
            get {
                return ResourceManager.GetString("Question_is_waiting_for_approval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question Name.
        /// </summary>
        public static string Question_Name {
            get {
                return ResourceManager.GetString("Question_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question title is required.
        /// </summary>
        public static string Question_title_is_required {
            get {
                return ResourceManager.GetString("Question_title_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question type is required.
        /// </summary>
        public static string Question_type_is_required {
            get {
                return ResourceManager.GetString("Question_type_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quick Search.
        /// </summary>
        public static string Quick_Search {
            get {
                return ResourceManager.GetString("Quick_Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quota Share.
        /// </summary>
        public static string Quota_Share {
            get {
                return ResourceManager.GetString("Quota_Share", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quota Share Requests.
        /// </summary>
        public static string Quota_Share_Requests {
            get {
                return ResourceManager.GetString("Quota_Share_Requests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote.
        /// </summary>
        public static string Quote {
            get {
                return ResourceManager.GetString("Quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Agreed.
        /// </summary>
        public static string Quote_Agreed {
            get {
                return ResourceManager.GetString("Quote_Agreed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Date.
        /// </summary>
        public static string Quote_Date {
            get {
                return ResourceManager.GetString("Quote_Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a Policy Wording to start your quote.
        /// </summary>
        public static string Quote_policy_wording_not_selected_info {
            get {
                return ResourceManager.GetString("Quote_policy_wording_not_selected_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you wish to remove this option?.
        /// </summary>
        public static string Quote_remove_option_confirmation_text {
            get {
                return ResourceManager.GetString("Quote_remove_option_confirmation_text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Request.
        /// </summary>
        public static string Quote_Request {
            get {
                return ResourceManager.GetString("Quote_Request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Request Name.
        /// </summary>
        public static string Quote_Request_Name {
            get {
                return ResourceManager.GetString("Quote_Request_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can complete application form(s) on behalf of the organisation before sending requests..
        /// </summary>
        public static string Quote_Request_Request_Fill_Info {
            get {
                return ResourceManager.GetString("Quote_Request_Request_Fill_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Request Split.
        /// </summary>
        public static string Quote_Request_Split {
            get {
                return ResourceManager.GetString("Quote_Request_Split", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Requests.
        /// </summary>
        public static string Quote_Requests {
            get {
                return ResourceManager.GetString("Quote_Requests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Requests Sent.
        /// </summary>
        public static string Quote_Requests_Sent {
            get {
                return ResourceManager.GetString("Quote_Requests_Sent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Valid Days.
        /// </summary>
        public static string Quote_Valid_Days {
            get {
                return ResourceManager.GetString("Quote_Valid_Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotes.
        /// </summary>
        public static string Quotes {
            get {
                return ResourceManager.GetString("Quotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Randomize.
        /// </summary>
        public static string Randomize {
            get {
                return ResourceManager.GetString("Randomize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rate Per Million.
        /// </summary>
        public static string Rate_per_million {
            get {
                return ResourceManager.GetString("Rate_per_million", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reactivate.
        /// </summary>
        public static string Reactivate {
            get {
                return ResourceManager.GetString("Reactivate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reactivate Supplier.
        /// </summary>
        public static string Reactivate_Supplier {
            get {
                return ResourceManager.GetString("Reactivate_Supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read Application Form.
        /// </summary>
        public static string Read_Application_Form {
            get {
                return ResourceManager.GetString("Read_Application_Form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ready.
        /// </summary>
        public static string Ready {
            get {
                return ResourceManager.GetString("Ready", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Realtime Data.
        /// </summary>
        public static string Realtime_Data {
            get {
                return ResourceManager.GetString("Realtime_Data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason.
        /// </summary>
        public static string Reason {
            get {
                return ResourceManager.GetString("Reason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recent incidents.
        /// </summary>
        public static string Recent_incidents {
            get {
                return ResourceManager.GetString("Recent_incidents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recently Modified.
        /// </summary>
        public static string Recently_modified {
            get {
                return ResourceManager.GetString("Recently_modified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Showing the 25 most recently modified requests across all organisations.
        /// </summary>
        public static string Recently_modified_quotes_card_subtitle {
            get {
                return ResourceManager.GetString("Recently_modified_quotes_card_subtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recipient.
        /// </summary>
        public static string Recipient {
            get {
                return ResourceManager.GetString("Recipient", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recovery Code.
        /// </summary>
        public static string Recovery_Code {
            get {
                return ResourceManager.GetString("Recovery_Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recovery Codes.
        /// </summary>
        public static string Recovery_Codes {
            get {
                return ResourceManager.GetString("Recovery_Codes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference.
        /// </summary>
        public static string Reference {
            get {
                return ResourceManager.GetString("Reference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference name is required..
        /// </summary>
        public static string Reference_name_is_required {
            get {
                return ResourceManager.GetString("Reference_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to References.
        /// </summary>
        public static string References {
            get {
                return ResourceManager.GetString("References", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please refresh the page or contact support if the problem persists.
        /// </summary>
        public static string Refresh_page_or_contact_support {
            get {
                return ResourceManager.GetString("Refresh_page_or_contact_support", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh Token Timespan.
        /// </summary>
        public static string Refresh_Token_TimeSpan {
            get {
                return ResourceManager.GetString("Refresh_Token_TimeSpan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region.
        /// </summary>
        public static string Region {
            get {
                return ResourceManager.GetString("Region", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region exposure level is required.
        /// </summary>
        public static string Region_exposure_level_is_required {
            get {
                return ResourceManager.GetString("Region_exposure_level_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region name is required.
        /// </summary>
        public static string Region_name_is_required {
            get {
                return ResourceManager.GetString("Region_name_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region Settings.
        /// </summary>
        public static string Region_Settings {
            get {
                return ResourceManager.GetString("Region_Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Regions.
        /// </summary>
        public static string Regions {
            get {
                return ResourceManager.GetString("Regions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Register.
        /// </summary>
        public static string Register {
            get {
                return ResourceManager.GetString("Register", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Register as a new user.
        /// </summary>
        public static string Register_as_a_new_user {
            get {
                return ResourceManager.GetString("Register_as_a_new_user", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Register Confirmation.
        /// </summary>
        public static string Register_Confirmation {
            get {
                return ResourceManager.GetString("Register_Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registered.
        /// </summary>
        public static string Registered {
            get {
                return ResourceManager.GetString("Registered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registered with Assessment.
        /// </summary>
        public static string Registered_with_Assessment {
            get {
                return ResourceManager.GetString("Registered_with_Assessment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Registered with No Assessment.
        /// </summary>
        public static string Registered_with_No_Assessment {
            get {
                return ResourceManager.GetString("Registered_with_No_Assessment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reinstate.
        /// </summary>
        public static string Reinstate {
            get {
                return ResourceManager.GetString("Reinstate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to reinstate this quote? The quote will no longer be marked as declined or withdrawn..
        /// </summary>
        public static string Reinstate_Quote_Message {
            get {
                return ResourceManager.GetString("Reinstate_Quote_Message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to reinstate this Wholesale Quote Request?.
        /// </summary>
        public static string Reinstate_wholesale_quote {
            get {
                return ResourceManager.GetString("Reinstate_wholesale_quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Relevant Suppliers.
        /// </summary>
        public static string Relevant_Suppliers {
            get {
                return ResourceManager.GetString("Relevant_Suppliers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remember this machine.
        /// </summary>
        public static string Remember_this_machine {
            get {
                return ResourceManager.GetString("Remember_this_machine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove.
        /// </summary>
        public static string Remove {
            get {
                return ResourceManager.GetString("Remove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to remove this application form? Once removed, it cannot be edited.
        /// </summary>
        public static string Remove_application_form {
            get {
                return ResourceManager.GetString("Remove_application_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove Association.
        /// </summary>
        public static string Remove_Association {
            get {
                return ResourceManager.GetString("Remove_Association", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to remove an association with this supplier? The supplier will be removed from all unsubmitted submissions..
        /// </summary>
        public static string Remove_Association_Confirmation_Question {
            get {
                return ResourceManager.GetString("Remove_Association_Confirmation_Question", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove from Quota Share.
        /// </summary>
        public static string Remove_from_quota_share {
            get {
                return ResourceManager.GetString("Remove_from_quota_share", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove Product.
        /// </summary>
        public static string Remove_product {
            get {
                return ResourceManager.GetString("Remove_product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove wholesale broker placements from program.
        /// </summary>
        public static string Remove_wholesale_broker_placements_from_program {
            get {
                return ResourceManager.GetString("Remove_wholesale_broker_placements_from_program", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Removed.
        /// </summary>
        public static string Removed {
            get {
                return ResourceManager.GetString("Removed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Renewal Date.
        /// </summary>
        public static string Renewal_Date {
            get {
                return ResourceManager.GetString("Renewal_Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report Details.
        /// </summary>
        public static string Report_Details {
            get {
                return ResourceManager.GetString("Report_Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reporting.
        /// </summary>
        public static string Reporting {
            get {
                return ResourceManager.GetString("Reporting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reporting Currency.
        /// </summary>
        public static string Reporting_Currency {
            get {
                return ResourceManager.GetString("Reporting_Currency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reports.
        /// </summary>
        public static string Reports {
            get {
                return ResourceManager.GetString("Reports", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Broker.
        /// </summary>
        public static string Request_Broker {
            get {
                return ResourceManager.GetString("Request_Broker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request does not contain any form(s) or file(s).
        /// </summary>
        public static string Request_does_not_contain_any_form_s__or_file_s_ {
            get {
                return ResourceManager.GetString("Request_does_not_contain_any_form_s__or_file_s_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Form.
        /// </summary>
        public static string Request_Form {
            get {
                return ResourceManager.GetString("Request_Form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request is taking longer than expected....
        /// </summary>
        public static string Request_is_taking_longer_than_expected {
            get {
                return ResourceManager.GetString("Request_is_taking_longer_than_expected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Assessment.
        /// </summary>
        public static string Request_new_assessment {
            get {
                return ResourceManager.GetString("Request_new_assessment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request New Submission.
        /// </summary>
        public static string Request_New_Submission {
            get {
                return ResourceManager.GetString("Request_New_Submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Submission.
        /// </summary>
        public static string Request_Submission {
            get {
                return ResourceManager.GetString("Request_Submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requested On.
        /// </summary>
        public static string Requested_On {
            get {
                return ResourceManager.GetString("Requested_On", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requested By.
        /// </summary>
        public static string RequestedBy {
            get {
                return ResourceManager.GetString("RequestedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requests.
        /// </summary>
        public static string Requests {
            get {
                return ResourceManager.GetString("Requests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Require Confirmed Account.
        /// </summary>
        public static string Require_Confirmed_Account {
            get {
                return ResourceManager.GetString("Require_Confirmed_Account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Require Confirmed Email.
        /// </summary>
        public static string Require_Confirmed_Email {
            get {
                return ResourceManager.GetString("Require_Confirmed_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Require Confirmed Phone Number.
        /// </summary>
        public static string Require_Confirmed_Phone_Number {
            get {
                return ResourceManager.GetString("Require_Confirmed_Phone_Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Require Digits.
        /// </summary>
        public static string Require_Digits {
            get {
                return ResourceManager.GetString("Require_Digits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Require Lowercase Characters.
        /// </summary>
        public static string Require_Lowercase_Characters {
            get {
                return ResourceManager.GetString("Require_Lowercase_Characters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Require Non-Alphanumeric.
        /// </summary>
        public static string Require_Non_Alphanumeric {
            get {
                return ResourceManager.GetString("Require_Non_Alphanumeric", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Require Uppercase Characters.
        /// </summary>
        public static string Require_Uppercase_Characters {
            get {
                return ResourceManager.GetString("Require_Uppercase_Characters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required Files.
        /// </summary>
        public static string Required_Files {
            get {
                return ResourceManager.GetString("Required_Files", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter additional documents you would want the Supplier to provide as part of their submission. Click the &apos;Add File&apos; button or press Enter to ensure they are added..
        /// </summary>
        public static string Required_Files_Supplementary_Text {
            get {
                return ResourceManager.GetString("Required_Files_Supplementary_Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required forms.
        /// </summary>
        public static string Required_Forms {
            get {
                return ResourceManager.GetString("Required_Forms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select assessment forms to be completed by Supplier.
        /// </summary>
        public static string Required_Forms_Supplementary_Text {
            get {
                return ResourceManager.GetString("Required_Forms_Supplementary_Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required Length.
        /// </summary>
        public static string Required_Length {
            get {
                return ResourceManager.GetString("Required_Length", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required Unique Characters.
        /// </summary>
        public static string Required_Unique_Characters {
            get {
                return ResourceManager.GetString("Required_Unique_Characters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requirements.
        /// </summary>
        public static string Requirements {
            get {
                return ResourceManager.GetString("Requirements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resend.
        /// </summary>
        public static string Resend {
            get {
                return ResourceManager.GetString("Resend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot resend again, as this user is already on the system.
        /// </summary>
        public static string Resend_disabled_information {
            get {
                return ResourceManager.GetString("Resend_disabled_information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resend Email Confirmation.
        /// </summary>
        public static string Resend_Email_Confirmation {
            get {
                return ResourceManager.GetString("Resend_Email_Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset.
        /// </summary>
        public static string Reset {
            get {
                return ResourceManager.GetString("Reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Two-Factor Authentication App.
        /// </summary>
        public static string Reset_2FA_App {
            get {
                return ResourceManager.GetString("Reset_2FA_App", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Two-Factor Authentication Recovery Codes.
        /// </summary>
        public static string Reset_2FA_Recovery_Codes {
            get {
                return ResourceManager.GetString("Reset_2FA_Recovery_Codes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Authenticator App.
        /// </summary>
        public static string Reset_Authenticator_App {
            get {
                return ResourceManager.GetString("Reset_Authenticator_App", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Authenticator Key.
        /// </summary>
        public static string Reset_Authenticator_Key {
            get {
                return ResourceManager.GetString("Reset_Authenticator_Key", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Password.
        /// </summary>
        public static string Reset_Password {
            get {
                return ResourceManager.GetString("Reset_Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Password Confirmation.
        /// </summary>
        public static string Reset_Password_Confirmation {
            get {
                return ResourceManager.GetString("Reset_Password_Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Recovery Codes.
        /// </summary>
        public static string Reset_Recovery_Codes {
            get {
                return ResourceManager.GetString("Reset_Recovery_Codes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to reset your authenticator keys.
        /// </summary>
        public static string reset_your_authenticator_keys {
            get {
                return ResourceManager.GetString("reset_your_authenticator_keys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset your password..
        /// </summary>
        public static string Reset_your_password {
            get {
                return ResourceManager.GetString("Reset_your_password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource.Issues is almost reaching 100.
        /// </summary>
        public static string Resource_Issues_is_almost_reaching_100 {
            get {
                return ResourceManager.GetString("Resource.Issues_is_almost_reaching_100", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resources.
        /// </summary>
        public static string Resources {
            get {
                return ResourceManager.GetString("Resources", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Respond to Quota Share.
        /// </summary>
        public static string Respond_to_quota_share {
            get {
                return ResourceManager.GetString("Respond_to_quota_share", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Response Analysis.
        /// </summary>
        public static string Response_Analysis {
            get {
                return ResourceManager.GetString("Response_Analysis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Retail Broker Quote Requests.
        /// </summary>
        public static string Retail_Broker_Quote_Requests {
            get {
                return ResourceManager.GetString("Retail_Broker_Quote_Requests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Retention (Any one claim).
        /// </summary>
        public static string Retention {
            get {
                return ResourceManager.GetString("Retention", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Retention/Waiting Period (Any one claim).
        /// </summary>
        public static string Retention_waiting_period {
            get {
                return ResourceManager.GetString("Retention_waiting_period", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Retroactive Date.
        /// </summary>
        public static string Retroactive_Date {
            get {
                return ResourceManager.GetString("Retroactive_Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Retroactive date cannot be in the future.
        /// </summary>
        public static string Retroactive_date_cannot_be_in_the_future {
            get {
                return ResourceManager.GetString("Retroactive_date_cannot_be_in_the_future", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Retroactive date is invalid.
        /// </summary>
        public static string Retroactive_date_is_invalid {
            get {
                return ResourceManager.GetString("Retroactive_date_is_invalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Retroactive type.
        /// </summary>
        public static string Retroactive_type {
            get {
                return ResourceManager.GetString("Retroactive_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Return.
        /// </summary>
        public static string Return {
            get {
                return ResourceManager.GetString("Return", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revenue.
        /// </summary>
        public static string Revenue {
            get {
                return ResourceManager.GetString("Revenue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revenue %.
        /// </summary>
        public static string Revenue_Percent {
            get {
                return ResourceManager.GetString("Revenue_Percent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revenues should add up to 100%.
        /// </summary>
        public static string Revenues_Add_Up_To_100 {
            get {
                return ResourceManager.GetString("Revenues_Add_Up_To_100", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RFC1123 Pattern.
        /// </summary>
        public static string RFC1123_Pattern {
            get {
                return ResourceManager.GetString("RFC1123_Pattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Right.
        /// </summary>
        public static string Right {
            get {
                return ResourceManager.GetString("Right", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Roentgen.
        /// </summary>
        public static string Roentgen {
            get {
                return ResourceManager.GetString("Roentgen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role.
        /// </summary>
        public static string Role {
            get {
                return ResourceManager.GetString("Role", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Role Name.
        /// </summary>
        public static string Role_Name {
            get {
                return ResourceManager.GetString("Role_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Roles.
        /// </summary>
        public static string Roles {
            get {
                return ResourceManager.GetString("Roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rows per page:.
        /// </summary>
        public static string Rows_Per_Page {
            get {
                return ResourceManager.GetString("Rows_Per_Page", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RPM.
        /// </summary>
        public static string RPM {
            get {
                return ResourceManager.GetString("RPM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RPM is the premium divided by the layer limit (in millions). It shows the cost of coverage per $1M insured..
        /// </summary>
        public static string RPM_info {
            get {
                return ResourceManager.GetString("RPM_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Satisfied.
        /// </summary>
        public static string Satisfied {
            get {
                return ResourceManager.GetString("Satisfied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Positives.
        /// </summary>
        public static string Satisfied_State_Name {
            get {
                return ResourceManager.GetString("Satisfied_State_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Application Form.
        /// </summary>
        public static string Save_ApplicationForm {
            get {
                return ResourceManager.GetString("Save_ApplicationForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save product.
        /// </summary>
        public static string Save_product {
            get {
                return ResourceManager.GetString("Save_product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Progress.
        /// </summary>
        public static string Save_progress {
            get {
                return ResourceManager.GetString("Save_progress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scan the QR Code or enter this key:.
        /// </summary>
        public static string Scan_the_QR_code {
            get {
                return ResourceManager.GetString("Scan_the_QR_code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scheduled.
        /// </summary>
        public static string Scheduled {
            get {
                return ResourceManager.GetString("Scheduled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Score.
        /// </summary>
        public static string Score {
            get {
                return ResourceManager.GetString("Score", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        public static string Search {
            get {
                return ResourceManager.GetString("Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search by Roles.
        /// </summary>
        public static string Search_by_Roles {
            get {
                return ResourceManager.GetString("Search_by_Roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Control Framework.
        /// </summary>
        public static string Security_Control_Framework {
            get {
                return ResourceManager.GetString("Security_Control_Framework", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to see.
        /// </summary>
        public static string see {
            get {
                return ResourceManager.GetString("see", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select.
        /// </summary>
        public static string Select {
            get {
                return ResourceManager.GetString("Select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a supplier....
        /// </summary>
        public static string Select_a_supplier {
            get {
                return ResourceManager.GetString("Select_a_supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select brokers to associate with the organisation.
        /// </summary>
        public static string Select_brokers_to_associate_with_the_organisation {
            get {
                return ResourceManager.GetString("Select_brokers_to_associate_with_the_organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select file....
        /// </summary>
        public static string Select_File {
            get {
                return ResourceManager.GetString("Select_File", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to -Select-.
        /// </summary>
        public static string select_for_dropdown {
            get {
                return ResourceManager.GetString("select_for_dropdown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select this option.
        /// </summary>
        public static string Select_Option {
            get {
                return ResourceManager.GetString("Select_Option", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you wish to select this option? This represents an informal agreement on the cover provided and will set the status of this layer as &apos;Quote Agreed&apos;. You can deselect this option at anytime, if you change your mind..
        /// </summary>
        public static string Select_Option_Confirmation {
            get {
                return ResourceManager.GetString("Select_Option_Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Roles.
        /// </summary>
        public static string Select_Roles {
            get {
                return ResourceManager.GetString("Select_Roles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selectable Application Forms.
        /// </summary>
        public static string Selectable_Application_Forms {
            get {
                return ResourceManager.GetString("Selectable_Application_Forms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected.
        /// </summary>
        public static string Selected {
            get {
                return ResourceManager.GetString("Selected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have selected the same file multiple times.
        /// </summary>
        public static string Selected_duplicate_files {
            get {
                return ResourceManager.GetString("Selected_duplicate_files", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected Suppliers.
        /// </summary>
        public static string Selected_suppliers {
            get {
                return ResourceManager.GetString("Selected_suppliers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Self Assessment.
        /// </summary>
        public static string Self_Assessment {
            get {
                return ResourceManager.GetString("Self_Assessment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Self-assessments.
        /// </summary>
        public static string Self_assessments {
            get {
                return ResourceManager.GetString("Self_assessments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send.
        /// </summary>
        public static string Send {
            get {
                return ResourceManager.GetString("Send", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send Change Password Email.
        /// </summary>
        public static string Send_a_change_password_email {
            get {
                return ResourceManager.GetString("Send_a_change_password_email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send Activation Email.
        /// </summary>
        public static string Send_Activation_Email {
            get {
                return ResourceManager.GetString("Send_Activation_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send submission to the client for them to complete.
        /// </summary>
        public static string Send_submission {
            get {
                return ResourceManager.GetString("Send_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send to Broker.
        /// </summary>
        public static string Send_to_broker {
            get {
                return ResourceManager.GetString("Send_to_broker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send to Brokers and Underwriters.
        /// </summary>
        public static string Send_to_Underwriters_and_Brokers {
            get {
                return ResourceManager.GetString("Send_to_Underwriters_and_Brokers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sent.
        /// </summary>
        public static string Sent {
            get {
                return ResourceManager.GetString("Sent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Septemper.
        /// </summary>
        public static string Septemper {
            get {
                return ResourceManager.GetString("Septemper", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Series 1.
        /// </summary>
        public static string Series_1 {
            get {
                return ResourceManager.GetString("Series_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Series 2.
        /// </summary>
        public static string Series_2 {
            get {
                return ResourceManager.GetString("Series_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Server-Side Authorization.
        /// </summary>
        public static string Server_Side_Authorization {
            get {
                return ResourceManager.GetString("Server_Side_Authorization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Server Side Validation.
        /// </summary>
        public static string Server_Side_Validation {
            get {
                return ResourceManager.GetString("Server_Side_Validation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Name.
        /// </summary>
        public static string Service_Name {
            get {
                return ResourceManager.GetString("Service_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Services.
        /// </summary>
        public static string Services {
            get {
                return ResourceManager.GetString("Services", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Set Random Password.
        /// </summary>
        public static string Set_Random_Password {
            get {
                return ResourceManager.GetString("Set_Random_Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings.
        /// </summary>
        public static string Settings {
            get {
                return ResourceManager.GetString("Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Authenticator App.
        /// </summary>
        public static string Setup_Authenticator_App {
            get {
                return ResourceManager.GetString("Setup_Authenticator_App", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Share.
        /// </summary>
        public static string Share {
            get {
                return ResourceManager.GetString("Share", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you wish to show this layer to the Organisation? The Organisation will be able to see the layer and its contents. This action can be undone at anytime..
        /// </summary>
        public static string Share_layer_modal_confirmation_text {
            get {
                return ResourceManager.GetString("Share_layer_modal_confirmation_text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Share submissions with a Broker.
        /// </summary>
        public static string Share_submission_with_a_broker {
            get {
                return ResourceManager.GetString("Share_submission_with_a_broker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Share with Brokers.
        /// </summary>
        public static string Share_With_Brokers {
            get {
                return ResourceManager.GetString("Share_With_Brokers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Share with Underwriters.
        /// </summary>
        public static string Share_With_Underwriters {
            get {
                return ResourceManager.GetString("Share_With_Underwriters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Date Pattern.
        /// </summary>
        public static string Short_Date_Pattern {
            get {
                return ResourceManager.GetString("Short_Date_Pattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Time Pattern.
        /// </summary>
        public static string Short_Time_Pattern {
            get {
                return ResourceManager.GetString("Short_Time_Pattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shortest Day Names.
        /// </summary>
        public static string Shortest_Day_Names {
            get {
                return ResourceManager.GetString("Shortest_Day_Names", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shortest Day Names.
        /// </summary>
        public static string Shortest_Day_Names1 {
            get {
                return ResourceManager.GetString("Shortest_Day_Names1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show.
        /// </summary>
        public static string Show {
            get {
                return ResourceManager.GetString("Show", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Awaiting Approval Only.
        /// </summary>
        public static string Show_Awaiting_Approval_Only {
            get {
                return ResourceManager.GetString("Show_Awaiting_Approval_Only", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Inactive.
        /// </summary>
        public static string Show_inactive {
            get {
                return ResourceManager.GetString("Show_inactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Org Profile.
        /// </summary>
        public static string Show_Org_Profile {
            get {
                return ResourceManager.GetString("Show_Org_Profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Underlying.
        /// </summary>
        public static string Show_Underlying {
            get {
                return ResourceManager.GetString("Show_Underlying", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Showing.
        /// </summary>
        public static string Showing {
            get {
                return ResourceManager.GetString("Showing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Side Authorization.
        /// </summary>
        public static string Side_Authorization {
            get {
                return ResourceManager.GetString("Side_Authorization", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sign in.
        /// </summary>
        public static string Sign_in {
            get {
                return ResourceManager.GetString("Sign_in", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sign-in failed.
        /// </summary>
        public static string Sign_in_failed {
            get {
                return ResourceManager.GetString("Sign-in_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sign in Settings.
        /// </summary>
        public static string Sign_in_Settings {
            get {
                return ResourceManager.GetString("Sign_in_Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sign-out failed.
        /// </summary>
        public static string Sign_out_failed {
            get {
                return ResourceManager.GetString("Sign-out_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sign-out succeeded.
        /// </summary>
        public static string Sign_out_succeeded {
            get {
                return ResourceManager.GetString("Sign-out_succeeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sign up.
        /// </summary>
        public static string Sign_up {
            get {
                return ResourceManager.GetString("Sign_up", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Signed Off By.
        /// </summary>
        public static string Signed_Off_By {
            get {
                return ResourceManager.GetString("Signed_Off_By", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Signing in....
        /// </summary>
        public static string Signing_in {
            get {
                return ResourceManager.GetString("Signing_in", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Signing out....
        /// </summary>
        public static string Signing_out {
            get {
                return ResourceManager.GetString("Signing_out", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Drag and drop file here to upload.
        /// </summary>
        public static string Single_File_Dropzone_Hint {
            get {
                return ResourceManager.GetString("Single_File_Dropzone_Hint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Snapshot cannot be null.
        /// </summary>
        public static string Snapshot_cannot_be_null {
            get {
                return ResourceManager.GetString("Snapshot_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Solar.
        /// </summary>
        public static string Solar {
            get {
                return ResourceManager.GetString("Solar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Something went wrong!.
        /// </summary>
        public static string Something_went_wrong {
            get {
                return ResourceManager.GetString("Something_went_wrong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, there&apos;s nothing at this address..
        /// </summary>
        public static string Sorry_there_s_nothing_at_this_address {
            get {
                return ResourceManager.GetString("Sorry_there\'s_nothing_at_this_address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sortable DateTime Pattern.
        /// </summary>
        public static string Sortable_DateTime_Pattern {
            get {
                return ResourceManager.GetString("Sortable_DateTime_Pattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Social Security Number.
        /// </summary>
        public static string SSN {
            get {
                return ResourceManager.GetString("SSN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SSN.
        /// </summary>
        public static string SSNShort {
            get {
                return ResourceManager.GetString("SSNShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start.
        /// </summary>
        public static string Start {
            get {
                return ResourceManager.GetString("Start", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start date cannot be after end date.
        /// </summary>
        public static string Start_date_cannot_be_after_start_date {
            get {
                return ResourceManager.GetString("Start_date_cannot_be_after_start_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start SignalR Connection.
        /// </summary>
        public static string Start_SignalR_Connection {
            get {
                return ResourceManager.GetString("Start_SignalR_Connection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State.
        /// </summary>
        public static string State {
            get {
                return ResourceManager.GetString("State", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Statistics.
        /// </summary>
        public static string Statistics {
            get {
                return ResourceManager.GetString("Statistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        public static string Status {
            get {
                return ResourceManager.GetString("Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stop.
        /// </summary>
        public static string Stop {
            get {
                return ResourceManager.GetString("Stop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Storage type is.
        /// </summary>
        public static string Storage_type_is {
            get {
                return ResourceManager.GetString("Storage_type_is", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Store these codes in a safe place.
        /// </summary>
        public static string Store_these_codes_in_a_safe_place {
            get {
                return ResourceManager.GetString("Store_these_codes_in_a_safe_place", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stored Data.
        /// </summary>
        public static string StoredData {
            get {
                return ResourceManager.GetString("StoredData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string String {
            get {
                return ResourceManager.GetString("String", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sub-limits of Liability.
        /// </summary>
        public static string Sub_limits_of_liability {
            get {
                return ResourceManager.GetString("Sub_limits_of_liability", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subdomain.
        /// </summary>
        public static string Subdomain {
            get {
                return ResourceManager.GetString("Subdomain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subjectivities.
        /// </summary>
        public static string Subjectivities {
            get {
                return ResourceManager.GetString("Subjectivities", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submission.
        /// </summary>
        public static string Submission {
            get {
                return ResourceManager.GetString("Submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submission cannot be null.
        /// </summary>
        public static string Submission_cannot_be_null {
            get {
                return ResourceManager.GetString("Submission_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submission Date.
        /// </summary>
        public static string Submission_Date {
            get {
                return ResourceManager.GetString("Submission_Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submission Details.
        /// </summary>
        public static string Submission_Details {
            get {
                return ResourceManager.GetString("Submission_Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submission Information.
        /// </summary>
        public static string Submission_information {
            get {
                return ResourceManager.GetString("Submission_information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submission Name.
        /// </summary>
        public static string Submission_Name {
            get {
                return ResourceManager.GetString("Submission_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submission name cannot be null.
        /// </summary>
        public static string Submission_Name_Cannot_be_Null {
            get {
                return ResourceManager.GetString("Submission_Name_Cannot_be_Null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to is your Organisation’s central record of information used to assess, manage, or market your insurance program..
        /// </summary>
        public static string Submission_page_card_header {
            get {
                return ResourceManager.GetString("Submission_page_card_header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submission Progress.
        /// </summary>
        public static string Submission_Progress {
            get {
                return ResourceManager.GetString("Submission_Progress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submission Requests.
        /// </summary>
        public static string Submission_Requests {
            get {
                return ResourceManager.GetString("Submission_Requests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submission Status.
        /// </summary>
        public static string Submission_Status {
            get {
                return ResourceManager.GetString("Submission_Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please make sure your Organisation&apos;s profile is up-to-date before signing off on your Submission.
        /// </summary>
        public static string Submission_View_Organisation_Profile_Information {
            get {
                return ResourceManager.GetString("Submission_View_Organisation_Profile_Information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submissions.
        /// </summary>
        public static string Submissions {
            get {
                return ResourceManager.GetString("Submissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submissions Received By Team in {0}.
        /// </summary>
        public static string Submissions_received_by_team_in {
            get {
                return ResourceManager.GetString("Submissions_received_by_team_in", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submit.
        /// </summary>
        public static string Submit {
            get {
                return ResourceManager.GetString("Submit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submit for internal use - to view your scores and insights.
        /// </summary>
        public static string Submit_for_internal_use {
            get {
                return ResourceManager.GetString("Submit_for_internal_use", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submit to a broker - for them to review and finalise your insurance program..
        /// </summary>
        public static string Submit_to_a_broker {
            get {
                return ResourceManager.GetString("Submit_to_a_broker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submitted.
        /// </summary>
        public static string Submitted {
            get {
                return ResourceManager.GetString("Submitted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submitted By.
        /// </summary>
        public static string Submitted_By {
            get {
                return ResourceManager.GetString("Submitted_By", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submitted On.
        /// </summary>
        public static string Submitted_On {
            get {
                return ResourceManager.GetString("Submitted_On", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Once shared, this Quote Request can be viewed by others and cannot be modified you can modify who it has been shared to at anytime. Once submitted you will be taken back automatically when the analysis completes.
        /// </summary>
        public static string Submitting_submission {
            get {
                return ResourceManager.GetString("Submitting_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Succeeded.
        /// </summary>
        public static string Succeeded {
            get {
                return ResourceManager.GetString("Succeeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary.
        /// </summary>
        public static string Summary {
            get {
                return ResourceManager.GetString("Summary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Details.
        /// </summary>
        public static string Summary_Details {
            get {
                return ResourceManager.GetString("Summary_Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier.
        /// </summary>
        public static string Supplier {
            get {
                return ResourceManager.GetString("Supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No application forms.
        /// </summary>
        public static string Supplier_application_forms_contains_no_data {
            get {
                return ResourceManager.GetString("Supplier_application_forms_contains_no_data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Assessments.
        /// </summary>
        public static string Supplier_Assessments {
            get {
                return ResourceManager.GetString("Supplier_Assessments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier contact.
        /// </summary>
        public static string Supplier_contact {
            get {
                return ResourceManager.GetString("Supplier_contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to supplier details.
        /// </summary>
        public static string Supplier_details {
            get {
                return ResourceManager.GetString("Supplier_details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier did not provide file.
        /// </summary>
        public static string Supplier_did_not_provide_file {
            get {
                return ResourceManager.GetString("Supplier_did_not_provide_file", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier email.
        /// </summary>
        public static string Supplier_email {
            get {
                return ResourceManager.GetString("Supplier_email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum file size: 5MB. Allowed extensions: .pdf, .docx, .doc, .xlsx, .xls, .pptx, .ppt, .jpeg, .jpg, .png.
        /// </summary>
        public static string Supplier_File_Upload_Note_Text {
            get {
                return ResourceManager.GetString("Supplier_File_Upload_Note_Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Files.
        /// </summary>
        public static string Supplier_Files {
            get {
                return ResourceManager.GetString("Supplier_Files", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No additional files.
        /// </summary>
        public static string Supplier_files_contains_no_data {
            get {
                return ResourceManager.GetString("Supplier_files_contains_no_data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Name.
        /// </summary>
        public static string Supplier_Name {
            get {
                return ResourceManager.GetString("Supplier_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Products.
        /// </summary>
        public static string Supplier_Products {
            get {
                return ResourceManager.GetString("Supplier_Products", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Products marked as `In use` will appear in your organisation&apos;s submission as part of the tools you currently use.
        /// </summary>
        public static string Supplier_products_subtitle {
            get {
                return ResourceManager.GetString("Supplier_products_subtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Profile.
        /// </summary>
        public static string Supplier_profile {
            get {
                return ResourceManager.GetString("Supplier_profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Service Not Found.
        /// </summary>
        public static string Supplier_provided_service_not_found {
            get {
                return ResourceManager.GetString("Supplier_provided_service_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Services.
        /// </summary>
        public static string Supplier_Services {
            get {
                return ResourceManager.GetString("Supplier_Services", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Type.
        /// </summary>
        public static string Supplier_Type {
            get {
                return ResourceManager.GetString("Supplier_Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suppliers.
        /// </summary>
        public static string Suppliers {
            get {
                return ResourceManager.GetString("Suppliers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suppliers Page..
        /// </summary>
        public static string Suppliers_page {
            get {
                return ResourceManager.GetString("Suppliers_page", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Support.
        /// </summary>
        public static string Support {
            get {
                return ResourceManager.GetString("Support", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Surname.
        /// </summary>
        public static string Surname {
            get {
                return ResourceManager.GetString("Surname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Survey cannot be null.
        /// </summary>
        public static string Survey_cannot_be_null {
            get {
                return ResourceManager.GetString("Survey_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Survey Created.
        /// </summary>
        public static string Survey_Created {
            get {
                return ResourceManager.GetString("Survey_Created", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suspend.
        /// </summary>
        public static string Suspend {
            get {
                return ResourceManager.GetString("Suspend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suspended.
        /// </summary>
        public static string Suspended {
            get {
                return ResourceManager.GetString("Suspended", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sweden.
        /// </summary>
        public static string Sweden {
            get {
                return ResourceManager.GetString("Sweden", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Synchronise.
        /// </summary>
        public static string Synchronise {
            get {
                return ResourceManager.GetString("Synchronise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Target Premium.
        /// </summary>
        public static string Target_Premium {
            get {
                return ResourceManager.GetString("Target_Premium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tenants.
        /// </summary>
        public static string Tenants {
            get {
                return ResourceManager.GetString("Tenants", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text cannot be just spaces.
        /// </summary>
        public static string Text_cannot_be_just_spaces {
            get {
                return ResourceManager.GetString("Text_cannot_be_just_spaces", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you for confirming your email..
        /// </summary>
        public static string Thank_you_for_confirming_your_email {
            get {
                return ResourceManager.GetString("Thank_you_for_confirming_your_email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The layer is currently not visible to the organisation.
        /// </summary>
        public static string The_layer_is_currently_not_visible_to_the_Organisation {
            get {
                return ResourceManager.GetString("The_layer_is_currently_not_visible_to_the_Organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The layer is currently visible to the organisation.
        /// </summary>
        public static string The_layer_is_currently_visible_to_the_Organisation {
            get {
                return ResourceManager.GetString("The_layer_is_currently_visible_to_the_Organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The page you were looking for doesn&apos;t exist..
        /// </summary>
        public static string The_page_you_were_looking_for_doesnt_exist {
            get {
                return ResourceManager.GetString("The_page_you_were_looking_for_doesnt_exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The selected file exceeds the allowed maximum size limit..
        /// </summary>
        public static string The_selected_file_exceeds_the_allowed_maximum_size_limit {
            get {
                return ResourceManager.GetString("The_selected_file_exceeds_the_allowed_maximum_size_limit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The selected file extension is not allowed..
        /// </summary>
        public static string The_selected_file_extension_is_not_allowed {
            get {
                return ResourceManager.GetString("The_selected_file_extension_is_not_allowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to These are the underwriters invited to the quota share..
        /// </summary>
        public static string These_are_the_underwriters_that_youve_invited_to_your_quota_share {
            get {
                return ResourceManager.GetString("These_are_the_underwriters_that_youve_invited_to_your_quota_share", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to these docs.
        /// </summary>
        public static string these_docs {
            get {
                return ResourceManager.GetString("these_docs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This action only disables Two-Factor Authentication..
        /// </summary>
        public static string This_action_only_disables_2FA {
            get {
                return ResourceManager.GetString("This_action_only_disables_2FA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This app does not currently have a real email sender registered.
        /// </summary>
        public static string This_app_does_not_currently_have_a_real_email_sender_registered {
            get {
                return ResourceManager.GetString("This_app_does_not_currently_have_a_real_email_sender_registered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This month.
        /// </summary>
        public static string This_Month {
            get {
                return ResourceManager.GetString("This_Month", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This page allows you to download or delete that data..
        /// </summary>
        public static string This_page_allows_you_to_download_or_delete_that_data {
            get {
                return ResourceManager.GetString("This_page_allows_you_to_download_or_delete_that_data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This process disables Two-Factor Authentication until you verify your authenticator app..
        /// </summary>
        public static string This_process_disables_2FA_until_you_verify_your_authenticator_app {
            get {
                return ResourceManager.GetString("This_process_disables_2FA_until_you_verify_your_authenticator_app", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This submission was created by.
        /// </summary>
        public static string This_submission_was_created_by {
            get {
                return ResourceManager.GetString("This_submission_was_created_by", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This view presents the program put together by the Wholesale Broker to fulfil the placement requirements outlined below. It reflects the underwriting engagement carried out on your behalf..
        /// </summary>
        public static string This_view_presents_wholesale_to_retail {
            get {
                return ResourceManager.GetString("This_view_presents_wholesale_to_retail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This will give the broker access to your security information..
        /// </summary>
        public static string This_Will_Give_Broker_Access_To_Info {
            get {
                return ResourceManager.GetString("This_Will_Give_Broker_Access_To_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time Separator.
        /// </summary>
        public static string Time_Separator {
            get {
                return ResourceManager.GetString("Time_Separator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time Zone.
        /// </summary>
        public static string Time_Zone {
            get {
                return ResourceManager.GetString("Time_Zone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time Zone Info.
        /// </summary>
        public static string Time_Zone_Info {
            get {
                return ResourceManager.GetString("Time_Zone_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time Zone OffSet.
        /// </summary>
        public static string Time_Zone_OffSet {
            get {
                return ResourceManager.GetString("Time_Zone_OffSet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tips.
        /// </summary>
        public static string Tips {
            get {
                return ResourceManager.GetString("Tips", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Title.
        /// </summary>
        public static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to to confirm your account.
        /// </summary>
        public static string to_confirm_your_account {
            get {
                return ResourceManager.GetString("to_confirm_your_account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to to login..
        /// </summary>
        public static string to_login {
            get {
                return ResourceManager.GetString("to_login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to to understand your risk profile or program readiness. Complete the forms, upload any relevant documents, and select the recipient. You can also submit your forms for analysis as part of an.
        /// </summary>
        public static string to_understand_risk_profile {
            get {
                return ResourceManager.GetString("to_understand_risk_profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to to review your results before deciding whether to send it to a Broker..
        /// </summary>
        public static string to_view_your_results_before_a_broker {
            get {
                return ResourceManager.GetString("to_view_your_results_before_a_broker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Token Info.
        /// </summary>
        public static string Token_Info {
            get {
                return ResourceManager.GetString("Token_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Token Settings.
        /// </summary>
        public static string Token_Settings {
            get {
                return ResourceManager.GetString("Token_Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duplicate the submission.
        /// </summary>
        public static string Tooltip_can_duplicate {
            get {
                return ResourceManager.GetString("Tooltip_can_duplicate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open form in read-only.
        /// </summary>
        public static string Tooltip_can_open_in_ready_only {
            get {
                return ResourceManager.GetString("Tooltip_can_open_in_ready_only", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can open submissiom in your dashboard.
        /// </summary>
        public static string Tooltip_can_open_submission_in_dashboard {
            get {
                return ResourceManager.GetString("Tooltip_can_open_submission_in_dashboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can open Supplier.
        /// </summary>
        public static string Tooltip_can_open_supplier_page {
            get {
                return ResourceManager.GetString("Tooltip_can_open_supplier_page", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can remove Supplier.
        /// </summary>
        public static string Tooltip_can_remove_supplier {
            get {
                return ResourceManager.GetString("Tooltip_can_remove_supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The submission can be sent to the client.
        /// </summary>
        public static string Tooltip_can_send_submission {
            get {
                return ResourceManager.GetString("Tooltip_can_send_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send submission to underwriter.
        /// </summary>
        public static string Tooltip_can_send_to_underwriter {
            get {
                return ResourceManager.GetString("Tooltip_can_send_to_underwriter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can share Submission to Broking House.
        /// </summary>
        public static string Tooltip_can_share_submission_to_broking_house {
            get {
                return ResourceManager.GetString("Tooltip_can_share_submission_to_broking_house", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Share submission to Underwriters and Broking Houses.
        /// </summary>
        public static string Tooltip_can_share_submission_to_others {
            get {
                return ResourceManager.GetString("Tooltip_can_share_submission_to_others", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sends out your Submission/Control Assessment to be reviewed.
        /// </summary>
        public static string Tooltip_can_submit_submission {
            get {
                return ResourceManager.GetString("Tooltip_can_submit_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Void the submission and all forms beneath it.
        /// </summary>
        public static string Tooltip_can_void_submission {
            get {
                return ResourceManager.GetString("Tooltip_can_void_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to open application form.
        /// </summary>
        public static string Tooltip_cannot_open_application_form {
            get {
                return ResourceManager.GetString("Tooltip_cannot_open_application_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to open submission.
        /// </summary>
        public static string Tooltip_cannot_open_submission {
            get {
                return ResourceManager.GetString("Tooltip_cannot_open_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot open this submission in your dashboard.
        /// </summary>
        public static string Tooltip_cannot_open_submission_in_dashboard {
            get {
                return ResourceManager.GetString("Tooltip_cannot_open_submission_in_dashboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Form cannot be removed.
        /// </summary>
        public static string Tooltip_cannot_remove_application_form {
            get {
                return ResourceManager.GetString("Tooltip_cannot_remove_application_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot remove file you do not own.
        /// </summary>
        public static string Tooltip_cannot_remove_file {
            get {
                return ResourceManager.GetString("Tooltip_cannot_remove_file", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot send submission when it has already beeen sent.
        /// </summary>
        public static string Tooltip_cannot_send_submission {
            get {
                return ResourceManager.GetString("Tooltip_cannot_send_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot send submission to underwriter.
        /// </summary>
        public static string Tooltip_cannot_send_submission_to_underwriter {
            get {
                return ResourceManager.GetString("Tooltip_cannot_send_submission_to_underwriter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot send voided submission.
        /// </summary>
        public static string Tooltip_cannot_send_voided_submission {
            get {
                return ResourceManager.GetString("Tooltip_cannot_send_voided_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot submit and already submitted form.
        /// </summary>
        public static string Tooltip_cannot_submit_already_submitted {
            get {
                return ResourceManager.GetString("Tooltip_cannot_submit_already_submitted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submission incomplete.
        /// </summary>
        public static string Tooltip_cannot_submit_submission {
            get {
                return ResourceManager.GetString("Tooltip_cannot_submit_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot submit submission to yourself.
        /// </summary>
        public static string Tooltip_cannot_submit_submission_to_self {
            get {
                return ResourceManager.GetString("Tooltip_cannot_submit_submission_to_self", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot view application form.
        /// </summary>
        public static string Tooltip_cannot_view_form {
            get {
                return ResourceManager.GetString("Tooltip_cannot_view_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cannot void the Submission once it has either been completed or voided.
        /// </summary>
        public static string Tooltip_cannot_void_submission {
            get {
                return ResourceManager.GetString("Tooltip_cannot_void_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download file.
        /// </summary>
        public static string Tooltip_download_file {
            get {
                return ResourceManager.GetString("Tooltip_download_file", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit application form.
        /// </summary>
        public static string Tooltip_edit_form {
            get {
                return ResourceManager.GetString("Tooltip_edit_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open analysis.
        /// </summary>
        public static string Tooltip_open_analysis {
            get {
                return ResourceManager.GetString("Tooltip_open_analysis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open application form.
        /// </summary>
        public static string Tooltip_open_application_form {
            get {
                return ResourceManager.GetString("Tooltip_open_application_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open submission to view forms.
        /// </summary>
        public static string Tooltip_open_submission {
            get {
                return ResourceManager.GetString("Tooltip_open_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open submission dashboard.
        /// </summary>
        public static string Tooltip_open_submission_in_dashboard {
            get {
                return ResourceManager.GetString("Tooltip_open_submission_in_dashboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open supplier.
        /// </summary>
        public static string Tooltip_open_supplier_page {
            get {
                return ResourceManager.GetString("Tooltip_open_supplier_page", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove from the submission.
        /// </summary>
        public static string Tooltip_remove_application_form {
            get {
                return ResourceManager.GetString("Tooltip_remove_application_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove supplier.
        /// </summary>
        public static string Tooltip_remove_supplier {
            get {
                return ResourceManager.GetString("Tooltip_remove_supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Broker Quote Request.
        /// </summary>
        public static string Tooltip_view_broker_quote_request {
            get {
                return ResourceManager.GetString("Tooltip_view_broker_quote_request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View analysis.
        /// </summary>
        public static string Tooltip_view_form {
            get {
                return ResourceManager.GetString("Tooltip_view_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application form incomplete.
        /// </summary>
        public static string Tooltip_view_form_disabled {
            get {
                return ResourceManager.GetString("Tooltip_view_form_disabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Quote Request.
        /// </summary>
        public static string Tooltip_view_quote_request {
            get {
                return ResourceManager.GetString("Tooltip_view_quote_request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top.
        /// </summary>
        public static string Top {
            get {
                return ResourceManager.GetString("Top", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top 10th Percent.
        /// </summary>
        public static string Top_10_Percent {
            get {
                return ResourceManager.GetString("Top_10_Percent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top 20th Percent.
        /// </summary>
        public static string Top_20_Percent {
            get {
                return ResourceManager.GetString("Top_20_Percent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top 3 Countries.
        /// </summary>
        public static string Top_3_Countries {
            get {
                return ResourceManager.GetString("Top_3_Countries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top 3 Industries.
        /// </summary>
        public static string Top_3_Industries {
            get {
                return ResourceManager.GetString("Top_3_Industries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top 5th Percent.
        /// </summary>
        public static string Top_5_Percent {
            get {
                return ResourceManager.GetString("Top_5_Percent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top 50th Percent.
        /// </summary>
        public static string Top_50th_Percent {
            get {
                return ResourceManager.GetString("Top_50th_Percent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top 10 Areas of Improvement.
        /// </summary>
        public static string Top_Areas_of_Improvement {
            get {
                return ResourceManager.GetString("Top_Areas_of_Improvement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top Five Contracts.
        /// </summary>
        public static string Top_Five_Contracts {
            get {
                return ResourceManager.GetString("Top_Five_Contracts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Cost.
        /// </summary>
        public static string Total_Cost {
            get {
                return ResourceManager.GetString("Total_Cost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Placed.
        /// </summary>
        public static string Total_Placed {
            get {
                return ResourceManager.GetString("Total_Placed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Premium.
        /// </summary>
        public static string Total_Premium {
            get {
                return ResourceManager.GetString("Total_Premium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Two-Factor Authentication.
        /// </summary>
        public static string Two_Factor_Authentication {
            get {
                return ResourceManager.GetString("Two_Factor_Authentication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UI Culture Code.
        /// </summary>
        public static string UI_Culture_Code {
            get {
                return ResourceManager.GetString("UI_Culture_Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to find wholesale submission for this option.
        /// </summary>
        public static string Unable_to_find_wholesale_submission_for_this_option {
            get {
                return ResourceManager.GetString("Unable_to_find_wholesale_submission_for_this_option", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unable to upload an empty file..
        /// </summary>
        public static string Unable_to_upload_an_empty_file {
            get {
                return ResourceManager.GetString("Unable_to_upload_an_empty_file", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unanswered Forum Question(s).
        /// </summary>
        public static string Unanswered_Forum_Questions {
            get {
                return ResourceManager.GetString("Unanswered_Forum_Questions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unanswered questions.
        /// </summary>
        public static string Unanswered_Questions {
            get {
                return ResourceManager.GetString("Unanswered_Questions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unanswered.
        /// </summary>
        public static string Unanswered_State_Name {
            get {
                return ResourceManager.GetString("Unanswered_State_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unapprove.
        /// </summary>
        public static string Unapprove {
            get {
                return ResourceManager.GetString("Unapprove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unassigned.
        /// </summary>
        public static string Unassigned {
            get {
                return ResourceManager.GetString("Unassigned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unauthorised.
        /// </summary>
        public static string Unauthorised {
            get {
                return ResourceManager.GetString("Unauthorised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unselected forms will no longer be available for use in your quote request.
        /// </summary>
        public static string Unchecking_Form_Warning_Info {
            get {
                return ResourceManager.GetString("Unchecking_Form_Warning_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Underlying.
        /// </summary>
        public static string Underlying {
            get {
                return ResourceManager.GetString("Underlying", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Underwriter.
        /// </summary>
        public static string Underwriter {
            get {
                return ResourceManager.GetString("Underwriter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Underwriters.
        /// </summary>
        public static string Underwriters {
            get {
                return ResourceManager.GetString("Underwriters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Underwriters that have access to your most recent submission.
        /// </summary>
        public static string Underwriters_that_have_access_to_your_most_recent_submission {
            get {
                return ResourceManager.GetString("Underwriters_that_have_access_to_your_most_recent_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Undo Cancellation.
        /// </summary>
        public static string Undo_Cancellation {
            get {
                return ResourceManager.GetString("Undo_Cancellation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unexpected Error.
        /// </summary>
        public static string Unexpected_Error {
            get {
                return ResourceManager.GetString("Unexpected_Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to United States.
        /// </summary>
        public static string United_States {
            get {
                return ResourceManager.GetString("United_States", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Units of Time.
        /// </summary>
        public static string Units_of_Time {
            get {
                return ResourceManager.GetString("Units_of_Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Universal Sortable DateTime Pattern.
        /// </summary>
        public static string Universal_Sortable_DateTime_Pattern {
            get {
                return ResourceManager.GetString("Universal_Sortable_DateTime_Pattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unopened Org Submissions.
        /// </summary>
        public static string Unopened_Organisation_Submissions {
            get {
                return ResourceManager.GetString("Unopened_Organisation_Submissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unopened Quote Requests.
        /// </summary>
        public static string Unopened_Quote_Requests {
            get {
                return ResourceManager.GetString("Unopened_Quote_Requests", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unplaced.
        /// </summary>
        public static string Unplaced {
            get {
                return ResourceManager.GetString("Unplaced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unprocessed.
        /// </summary>
        public static string Unprocessed {
            get {
                return ResourceManager.GetString("Unprocessed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unread Submissions.
        /// </summary>
        public static string Unread_Submissions {
            get {
                return ResourceManager.GetString("Unread_Submissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unsent.
        /// </summary>
        public static string Unsent {
            get {
                return ResourceManager.GetString("Unsent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unsubmitted.
        /// </summary>
        public static string Unsubmitted {
            get {
                return ResourceManager.GetString("Unsubmitted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Up to date.
        /// </summary>
        public static string Up_To_Date {
            get {
                return ResourceManager.GetString("Up_To_Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update.
        /// </summary>
        public static string Update {
            get {
                return ResourceManager.GetString("Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Application Form.
        /// </summary>
        public static string Update_ApplicationForm {
            get {
                return ResourceManager.GetString("Update_ApplicationForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Loss Type.
        /// </summary>
        public static string Update_Loss_Type {
            get {
                return ResourceManager.GetString("Update_Loss_Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Updated Successfully.
        /// </summary>
        public static string Updated_Successfully {
            get {
                return ResourceManager.GetString("Updated_Successfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload.
        /// </summary>
        public static string Upload {
            get {
                return ResourceManager.GetString("Upload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload Avatar.
        /// </summary>
        public static string Upload_Avatar {
            get {
                return ResourceManager.GetString("Upload_Avatar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload Documents.
        /// </summary>
        public static string Upload_Documents {
            get {
                return ResourceManager.GetString("Upload_Documents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload any supporting files related to your submission — such as certificates, reports, or other relevant documentation. These files will stay linked to this submission and can be updated or replaced at any time..
        /// </summary>
        public static string Upload_your_documents_here {
            get {
                return ResourceManager.GetString("Upload_your_documents_here", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use.
        /// </summary>
        public static string Use {
            get {
                return ResourceManager.GetString("Use", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use a local account to log in..
        /// </summary>
        public static string Use_a_local_account_to_log_in {
            get {
                return ResourceManager.GetString("Use_a_local_account_to_log_in", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Use this page to manage all your submissions — whether for internal review or collaboration with a Broker. .
        /// </summary>
        public static string Use_this_page_to_manage_your_submissions {
            get {
                return ResourceManager.GetString("Use_this_page_to_manage_your_submissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Account.
        /// </summary>
        public static string User_Account {
            get {
                return ResourceManager.GetString("User_Account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Documents.
        /// </summary>
        public static string User_Documents {
            get {
                return ResourceManager.GetString("User_Documents", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User is not found..
        /// </summary>
        public static string User_is_not_found {
            get {
                return ResourceManager.GetString("User_is_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Profile.
        /// </summary>
        public static string User_Profile {
            get {
                return ResourceManager.GetString("User_Profile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Settings.
        /// </summary>
        public static string User_Settings {
            get {
                return ResourceManager.GetString("User_Settings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username.
        /// </summary>
        public static string Username {
            get {
                return ResourceManager.GetString("Username", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Users.
        /// </summary>
        public static string Users {
            get {
                return ResourceManager.GetString("Users", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Users Management.
        /// </summary>
        public static string Users_Management {
            get {
                return ResourceManager.GetString("Users_Management", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Validation of the code did not succeed.
        /// </summary>
        public static string Validation_of_the_code_did_not_succeed {
            get {
                return ResourceManager.GetString("Validation_of_the_code_did_not_succeed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Validation Summary.
        /// </summary>
        public static string Validation_Summary {
            get {
                return ResourceManager.GetString("Validation_Summary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value.
        /// </summary>
        public static string Value {
            get {
                return ResourceManager.GetString("Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value cannot be null.
        /// </summary>
        public static string Value_cannot_be_null {
            get {
                return ResourceManager.GetString("Value_cannot_be_null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendors.
        /// </summary>
        public static string Vendors {
            get {
                return ResourceManager.GetString("Vendors", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Verify.
        /// </summary>
        public static string Verify {
            get {
                return ResourceManager.GetString("Verify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version.
        /// </summary>
        public static string Version {
            get {
                return ResourceManager.GetString("Version", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version/Model.
        /// </summary>
        public static string Version_Model {
            get {
                return ResourceManager.GetString("Version_Model", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version Number.
        /// </summary>
        public static string Version_Number {
            get {
                return ResourceManager.GetString("Version_Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Very High.
        /// </summary>
        public static string Very_High {
            get {
                return ResourceManager.GetString("Very_High", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Very Low.
        /// </summary>
        public static string Very_Low {
            get {
                return ResourceManager.GetString("Very_Low", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View.
        /// </summary>
        public static string View {
            get {
                return ResourceManager.GetString("View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Applicant.
        /// </summary>
        public static string View_Applicant {
            get {
                return ResourceManager.GetString("View_Applicant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Application Form.
        /// </summary>
        public static string View_Application_Form {
            get {
                return ResourceManager.GetString("View_Application_Form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Assessment Request.
        /// </summary>
        public static string View_Assessment_Request {
            get {
                return ResourceManager.GetString("View_Assessment_Request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Control Framework Domain.
        /// </summary>
        public static string View_Control_Framework_Domain {
            get {
                return ResourceManager.GetString("View_Control_Framework_Domain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Full Submission.
        /// </summary>
        public static string View_Full_Submission {
            get {
                return ResourceManager.GetString("View_Full_Submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Option.
        /// </summary>
        public static string View_Option {
            get {
                return ResourceManager.GetString("View_Option", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View option details.
        /// </summary>
        public static string View_option_details {
            get {
                return ResourceManager.GetString("View_option_details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Organisation.
        /// </summary>
        public static string View_Organisation {
            get {
                return ResourceManager.GetString("View_Organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Quote.
        /// </summary>
        public static string View_Quote {
            get {
                return ResourceManager.GetString("View_Quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Submission.
        /// </summary>
        public static string View_Submission {
            get {
                return ResourceManager.GetString("View_Submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View submission request.
        /// </summary>
        public static string View_Submission_Request {
            get {
                return ResourceManager.GetString("View_Submission_Request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View wholesale broker section.
        /// </summary>
        public static string View_wholesale_broker_section {
            get {
                return ResourceManager.GetString("View_wholesale_broker_section", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Visibility.
        /// </summary>
        public static string Visibility {
            get {
                return ResourceManager.GetString("Visibility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Visible to Organisation.
        /// </summary>
        public static string Visible_to_organisation {
            get {
                return ResourceManager.GetString("Visible_to_organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Visible to the organisation.
        /// </summary>
        public static string Visible_to_the_organisation {
            get {
                return ResourceManager.GetString("Visible_to_the_organisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Visit Website.
        /// </summary>
        public static string Visit_Website {
            get {
                return ResourceManager.GetString("Visit_Website", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Void.
        /// </summary>
        public static string Void {
            get {
                return ResourceManager.GetString("Void", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to void this Submission? Once voided, it cannot be edited.
        /// </summary>
        public static string Void_submission {
            get {
                return ResourceManager.GetString("Void_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to void this supplier assessment?.
        /// </summary>
        public static string Void_supplier_submission_confirmation_question {
            get {
                return ResourceManager.GetString("Void_supplier_submission_confirmation_question", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Voided.
        /// </summary>
        public static string Voided {
            get {
                return ResourceManager.GetString("Voided", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Waiting Period.
        /// </summary>
        public static string Waiting_period {
            get {
                return ResourceManager.GetString("Waiting_period", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Website.
        /// </summary>
        public static string Website {
            get {
                return ResourceManager.GetString("Website", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Weight.
        /// </summary>
        public static string Weight {
            get {
                return ResourceManager.GetString("Weight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to When enabled, the primary broker can view the full submission details. You can disable access at any time.
        /// </summary>
        public static string When_enabled_wholesale_submission {
            get {
                return ResourceManager.GetString("When_enabled_wholesale_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wholesale broker section on.
        /// </summary>
        public static string Wholesale_broker_section_on {
            get {
                return ResourceManager.GetString("Wholesale_broker_section_on", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wholesale Broker Submissions.
        /// </summary>
        public static string Wholesale_Broker_Submissions {
            get {
                return ResourceManager.GetString("Wholesale_Broker_Submissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This document outlines the insurance requirements as relayed by the originating broker for onward placement with underwriters. Once underwriting is complete, you can use the toggle to grant the primary broker access to the submission and allow them to continue the placement process. Please proceed with underwriting engagement based on the details below..
        /// </summary>
        public static string Wholesale_quote_request_info_text {
            get {
                return ResourceManager.GetString("Wholesale_quote_request_info_text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select at least one option to be able to share the submission to the primary broker.
        /// </summary>
        public static string Wholesale_unshareable_info {
            get {
                return ResourceManager.GetString("Wholesale_unshareable_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wind.
        /// </summary>
        public static string Wind {
            get {
                return ResourceManager.GetString("Wind", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Withdraw.
        /// </summary>
        public static string Withdraw {
            get {
                return ResourceManager.GetString("Withdraw", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to withdraw this quote?.
        /// </summary>
        public static string Withdraw_Quote_Confirmation {
            get {
                return ResourceManager.GetString("Withdraw_Quote_Confirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Withdraw Quote Request.
        /// </summary>
        public static string Withdraw_Quote_Request {
            get {
                return ResourceManager.GetString("Withdraw_Quote_Request", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Withdrawn by.
        /// </summary>
        public static string Withdrawn_by {
            get {
                return ResourceManager.GetString("Withdrawn_by", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Withdrawn by broker.
        /// </summary>
        public static string Withdrawn_By_Broker {
            get {
                return ResourceManager.GetString("Withdrawn_By_Broker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This quote has been withdrawn by the broker..
        /// </summary>
        public static string Withdrawn_by_broker_message {
            get {
                return ResourceManager.GetString("Withdrawn_by_broker_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Withdrawn by underwriter.
        /// </summary>
        public static string Withdrawn_By_Underwriter {
            get {
                return ResourceManager.GetString("Withdrawn_By_Underwriter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This quote has been withdrawn by the underwriter..
        /// </summary>
        public static string Withdrawn_by_underwriter_message {
            get {
                return ResourceManager.GetString("Withdrawn_by_underwriter_message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Year Established.
        /// </summary>
        public static string Year_Established {
            get {
                return ResourceManager.GetString("Year_Established", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Year Month Pattern.
        /// </summary>
        public static string Year_Month_Pattern {
            get {
                return ResourceManager.GetString("Year_Month_Pattern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Year Started.
        /// </summary>
        public static string Year_Started {
            get {
                return ResourceManager.GetString("Year_Started", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes, delete it.
        /// </summary>
        public static string Yes_delete_it {
            get {
                return ResourceManager.GetString("Yes_delete_it", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes, remove it.
        /// </summary>
        public static string Yes_remove_it {
            get {
                return ResourceManager.GetString("Yes_remove_it", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes, rename it.
        /// </summary>
        public static string Yes_rename_it {
            get {
                return ResourceManager.GetString("Yes_rename_it", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are not authenticated to export this report..
        /// </summary>
        public static string You_are_not_authenticated_to_export_this_report {
            get {
                return ResourceManager.GetString("You_are_not_authenticated_to_export_this_report", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are not authorized to access this page.
        /// </summary>
        public static string You_are_not_authorized_to_access_this_page {
            get {
                return ResourceManager.GetString("You_are_not_authorized_to_access_this_page", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can.
        /// </summary>
        public static string You_can {
            get {
                return ResourceManager.GetString("You_can", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can also duplicate the submission later if you want to send a refined version to a broker..
        /// </summary>
        public static string You_can_also_duplicate_this_submission {
            get {
                return ResourceManager.GetString("You_can_also_duplicate_this_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You cannot modify a product a Supplier has added.
        /// </summary>
        public static string You_cannot_modify_a_product_you_did_not_add {
            get {
                return ResourceManager.GetString("You_cannot_modify_a_product_you_did_not_add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have no recovery codes left..
        /// </summary>
        public static string You_have_no_recovery_codes_left {
            get {
                return ResourceManager.GetString("You_have_no_recovery_codes_left", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have {0} recovery codes left..
        /// </summary>
        public static string You_have_num_recovery_codes_left {
            get {
                return ResourceManager.GetString("You_have_num_recovery_codes_left", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have one recovery code left..
        /// </summary>
        public static string You_have_one_recovery_code_left {
            get {
                return ResourceManager.GetString("You_have_one_recovery_code_left", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have to pick at least one form.
        /// </summary>
        public static string You_have_to_select_at_least_one_form {
            get {
                return ResourceManager.GetString("You_have_to_select_at_least_one_form", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You must.
        /// </summary>
        public static string You_must {
            get {
                return ResourceManager.GetString("You_must", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You need &lt;strong&gt;Microsoft Authenticator&lt;/strong&gt; or &lt;strong&gt;Google Authenticator&lt;/strong&gt; on Android or iOS installed.
        /// </summary>
        public static string You_need_authenticator_QR {
            get {
                return ResourceManager.GetString("You_need_authenticator_QR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You should.
        /// </summary>
        public static string You_should {
            get {
                return ResourceManager.GetString("You_should", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your account contains personal data that you have given us..
        /// </summary>
        public static string Your_account_contains_personal_data_that_you_have_given_us {
            get {
                return ResourceManager.GetString("Your_account_contains_personal_data_that_you_have_given_us", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your Information.
        /// </summary>
        public static string Your_information {
            get {
                return ResourceManager.GetString("Your_information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your password has been reset.
        /// </summary>
        public static string Your_password_has_been_reset {
            get {
                return ResourceManager.GetString("Your_password_has_been_reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your report {0} is ready to download..
        /// </summary>
        public static string Your_report__0__is_ready_to_download_ {
            get {
                return ResourceManager.GetString("Your_report__0__is_ready_to_download\\", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your report {0} is ready to download..
        /// </summary>
        public static string Your_report_0_is_ready_to_download {
            get {
                return ResourceManager.GetString("Your_report_0_is_ready_to_download", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your report generation has failed..
        /// </summary>
        public static string Your_report_generation_has_failed {
            get {
                return ResourceManager.GetString("Your_report_generation_has_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your report is being generated..
        /// </summary>
        public static string Your_report_is_being_generated {
            get {
                return ResourceManager.GetString("Your_report_is_being_generated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your report is being initiated..
        /// </summary>
        public static string Your_report_is_being_initiated {
            get {
                return ResourceManager.GetString("Your_report_is_being_initiated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your report is ready to download..
        /// </summary>
        public static string Your_report_is_ready_to_download {
            get {
                return ResourceManager.GetString("Your_report_is_ready_to_download", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your submission.
        /// </summary>
        public static string Your_submission {
            get {
                return ResourceManager.GetString("Your_submission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to your two factor authentication app will provide you with a unique code..
        /// </summary>
        public static string your_two_factor_authentication_app_will_provide_you_with_a_unique_code {
            get {
                return ResourceManager.GetString("your_two_factor_authentication_app_will_provide_you_with_a_unique_code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yourself.
        /// </summary>
        public static string Yourself {
            get {
                return ResourceManager.GetString("Yourself", resourceCulture);
            }
        }
    }
}
