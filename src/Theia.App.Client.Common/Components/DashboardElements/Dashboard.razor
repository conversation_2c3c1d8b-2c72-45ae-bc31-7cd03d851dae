@using Theia.App.Shared.Enums
@using Theia.App.Client.Common.Components.Layers
@using Theia.App.Client.Common.Components.Layers.ProgramStacks
@using Theia.App.Client.Common.Components.Submissions.Wholesale
@using Theia.Domain.Common.Enums
@using Theia.Http.Services
@using Theia.Infrastructure.Common.Defaults

<PageHeader
    Header="@(isLoading ? Resource.Loading : dashboardData?.OrganisationInformation.OrganisationName ?? Resource.No_Data)"
    ToolbarContentClass="card-background">
</PageHeader>

@if (dashboardData?.OrganisationInformation.SubmissionType is SubmissionType.WholesaleBrokerSubmission && wholesaleSubmissionInfo is not null)
{
    <TelerikStackLayout Class="k-mb-3" Orientation="StackLayoutOrientation.Vertical" Spacing="1em">
        <WholesaleSubmissionInfo WholesaleSubmissionInfoType="@wholesaleSubmissionInfo.WholesaleSubmissionInfoType" WholesaleVm="@wholesaleSubmissionInfo" UrlSafeSubmissionId="@UrlSafeSubmissionId"/>
        @if (wholesaleSubmissionInfo.ShowUnderlying && !wholesaleSubmissionInfo.IsUserPartOfRequestingTenant)
        {
            <Underlyings WholesaleSubmissionId="@SubmissionId" LayerId="@((WebSafeGuid)wholesaleSubmissionInfo.LayerId)" IndicationRequestId="@((WebSafeGuid)wholesaleSubmissionInfo.IndicationRequestId)"/>
        }
    </TelerikStackLayout>
}

<!-- Checking to make sure user is viewing the submission as either the Organisation User (if it is an Organisation Submission) or they're a Wholesale Broker -->
@if (dashboardData?.OrganisationInformation.SubmissionType is SubmissionType.OrganisationSubmission
     || (dashboardData?.OrganisationInformation.SubmissionType is SubmissionType.WholesaleBrokerSubmission
         && !(wholesaleSubmissionInfo?.IsUserPartOfRequestingTenant ?? true)
         && !(wholesaleSubmissionInfo?.IsUserPartOfOrganisation ?? true)))
{
    <TelerikTabStrip>
        <TabStripTab Title="@Resource.Submission">
            @if (ShowBasicSubmission)
            {
                <BasicSubmission
                    _cts="@cts">
                </BasicSubmission>
            }
            else
            {
                <div class="section k-mb-0">
                    <TelerikStackLayout Orientation="StackLayoutOrientation.Horizontal">
                        <TelerikCard
                            Class="k-mr-2"
                            Orientation="CardOrientation.Vertical">
                            <CardHeader>
                                <CardTitle>@Resource.Submission_Details</CardTitle>
                            </CardHeader>
                            <CardBody>
                                <p>@Resource.This_submission_was_created_by @dashboardData.BasicSubmissionInformationModel.CreatedByTenantName - @dashboardData.BasicSubmissionInformationModel.CreatedByUserFullName</p>
                                <p>@Resource.Please_review_and_complete_submission_details</p>
                                <p>@Resource.Requested_On: @dashboardData.BasicSubmissionInformationModel.RequestOnDate.ToString(DefaultSettings.DateFormatConstants.DateFormat)</p>
                                <p>@Resource.Due_By: @dashboardData.BasicSubmissionInformationModel.DueBy.ToString(DefaultSettings.DateFormatConstants.DateFormat)</p>
                            </CardBody>
                        </TelerikCard>
                        <OrganisationInformation
                            OrganisationData="@dashboardData?.OrganisationInformation"
                            UrlSafeSubmissionId="@UrlSafeSubmissionId"
                            NotificationHelper="@NotificationHelper">
                        </OrganisationInformation>
                    </TelerikStackLayout> 
                </div>
                <TelerikTabStrip>
                    <TabStripTab Title="@Resource.Summary">
                        <Content>
                            <div class="section">
                                <Summary
                                    Class="width-25 summary-margin-right"
                                    UrlSafeSubmissionId="@UrlSafeSubmissionId"
                                    Cts="@cts">
                                </Summary>
                                <ControlFrameworks
                                    Class="width-100"
                                    ControlFrameworksToDisplay="@dashboardData?.ControlFrameworkInformation"
                                    UrlSafeSubmissionId="@UrlSafeSubmissionId"
                                    _cts="@cts"
                                    IsLoading="@isLoading"
                                    PercentilePositionText="@percentilePosition">
                                </ControlFrameworks>
                            </div>
                        </Content>
                    </TabStripTab>
                    <TabStripTab Title="@Resource.ApplicationForms">
                        <Content>
                            <div class="section height-override">
                                <ApplicationForms
                                    SubmissionId="@UrlSafeSubmissionId"
                                    UrlSafeBrokerSubmissionId="@UrlSafeBrokerSubmissionId"
                                    OrganisationId="@UrlSafeOrganisationId"
                                    Cts="@cts">
                                </ApplicationForms>
                            </div>
                        </Content>
                    </TabStripTab>
                    <TabStripTab Title="@Resource.Suppliers">
                        <Content>
                            <div class="section">
                                <DashboardSuppliers SubmissionId="@UrlSafeSubmissionId" />
                            </div>
                        </Content>
                    </TabStripTab>
                    <TabStripTab Title="@Resource.Files">
                        <Content>
                            <div class="section">
                                <SubmissionFiles
                                    Width="100%"
                                    SubmissionId="@UrlSafeSubmissionId"
                                    UrlSafeOrganisationId="@dashboardData?.OrganisationInformation.OrganisationId"
                                    IsAllowedToModifyFiles="@FileModificationStates.OnlyYourTenantsFiles"/>
                            </div>
                        </Content>
                    </TabStripTab>
                </TelerikTabStrip>
            }
        </TabStripTab>
        <TabStripTab Title="@Resource.Layers">
            <SubmissionLayers SubmissionId="@UrlSafeSubmissionId" OrganisationId="@UrlSafeOrganisationId"/>
        </TabStripTab>
        <TabStripTab Title="@Resource.Program_Stack">
            <ProgramStack OrganisationId="@UrlSafeOrganisationId" SubmissionId="@SubmissionId"/>
        </TabStripTab>
    </TelerikTabStrip>
}
else if (dashboardData?.OrganisationInformation.SubmissionType is SubmissionType.WholesaleBrokerSubmission
         && ((wholesaleSubmissionInfo?.IsUserPartOfRequestingTenant ?? false) || (wholesaleSubmissionInfo?.IsUserPartOfOrganisation ?? false)))
{
    <TelerikTabStrip>
        <TabStripTab Title="@Resource.Layers">
            <SubmissionLayers SubmissionId="@UrlSafeSubmissionId" OrganisationId="@UrlSafeOrganisationId"/>
        </TabStripTab>
        <TabStripTab Title="@Resource.Program_Stack">
            <ProgramStack OrganisationId="@UrlSafeOrganisationId" SubmissionId="@SubmissionId"/>
        </TabStripTab>
    </TelerikTabStrip>
}

<style>
    .section {
        max-width: 100%;
        min-width: 100%;
        height: auto;
        margin-bottom: 12px;
        display: flex;
        flex-direction: row;
    }
    
    .vh-40 {
        min-height: 40vh;
        height: auto;
    }
    
    .height-override {
        height: auto !important;
    }
    
    .flex-container p.right-align {
        margin-right: auto;
    }
</style>

@code {
    
}