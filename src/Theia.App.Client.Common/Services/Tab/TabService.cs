using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace Theia.App.Client.Common.Services.Tab;

public class TabService(NavigationManager navigationManager, IJSRuntime jsRuntime) : ITabService
{
    public async Task SetActiveTabAsync(string tabName, bool addToHistory)
    {
        string normalizedTab = NormalizeTabName(tabName);
        string basePath = GetBasePath();
        string newUrl = $"{basePath}/{normalizedTab}";
        
        string stateName = addToHistory ? "pushState" : "replaceState";
        await jsRuntime.InvokeVoidAsync($"history.{stateName}", null, "", newUrl);
    }

    public string GetActiveTabFromUrl()
    {
        Uri currentUri = new(navigationManager.Uri);
        string[] segments = currentUri.AbsolutePath.Split('/', StringSplitOptions.RemoveEmptyEntries);
        
        return segments.Length > 0 ? segments[^1] : string.Empty;
    }

    public string NormalizeTabName(string tabName) =>
        tabName.Replace(" ", "").Trim();
    
    private string GetBasePath()
    {
        Uri currentUri = new(navigationManager.Uri);
        string[] segments = currentUri.AbsolutePath.Split('/', StringSplitOptions.RemoveEmptyEntries);

        // Build the base path from segments
        string basePath;

        // Remove the last segment if it looks like a normalized tab name
        // This handles cases where we're already on a tab route
        if (segments.Length <= 1)
        {
            basePath = "/" + string.Join("/", segments);
        }
        else
        {
            string lastSegment = segments[^1];
            // Check if the last segment matches any common tab naming patterns
            if (!IsLikelyTabSegment(lastSegment))
            {
                basePath = "/" + string.Join("/", segments);
            }
            else
            {
                string[] baseSegments = segments[..^1];
                basePath = "/" + string.Join("/", baseSegments);
            }
        }

        // Add query string parameters (excluding 'tab' parameter)
        string queryString = GetQueryStringWithoutTabParameter(currentUri);
        if (!string.IsNullOrEmpty(queryString))
        {
            basePath += "?" + queryString;
        }

        return basePath;
    }

    private string GetQueryStringWithoutTabParameter(Uri uri)
    {
        if (string.IsNullOrEmpty(uri.Query))
            return string.Empty;

        // Parse query string and filter out 'tab' parameter
        string query = uri.Query.TrimStart('?');
        string[] parameters = query.Split('&', StringSplitOptions.RemoveEmptyEntries);

        List<string> filteredParameters = new();
        foreach (string parameter in parameters)
        {
            string[] keyValue = parameter.Split('=', 2);
            if (keyValue.Length > 0 && !keyValue[0].Equals("tab", StringComparison.OrdinalIgnoreCase))
            {
                filteredParameters.Add(parameter);
            }
        }

        return string.Join("&", filteredParameters);
    }
    
    private bool IsLikelyTabSegment(string segment)
    {
        // Common patterns for tab names (no spaces, CamelCase, etc.)
        return !segment.Contains('-') && 
               !segment.Contains('_') && 
               char.IsUpper(segment[0]) &&
               segment.Length > 2;
    }
}