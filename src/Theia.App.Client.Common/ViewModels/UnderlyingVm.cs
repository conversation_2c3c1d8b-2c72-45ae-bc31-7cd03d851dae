using Telerik.Blazor;
using Theia.App.Shared.Extensions;
using Theia.App.Shared.Layers;
using Theia.Domain.Common.Enums;
using Theia.FrontendResources;

namespace Theia.App.Client.Common.ViewModels;

public record UnderlyingVm
{
    public required string Layer { get; init; }
    public required decimal Limit { get; init; }
    public required decimal? Excess { get; init; }
    public required decimal Premium { get; init; }
    public required UnderlyingStatus Status { get; init; }
    public required string Insurer { get; init; }
    public required UnderlyingPartVm[] Parts { get; init; } = [];
    public required decimal? RPM { get; init; }
    public required decimal? ILF { get; init; }
    public required bool IsWholesale { get; init; }

    public string LimitDisplay => Limit.ConvertToAppFormat();
    public string ExcessDisplay => Excess.ConvertToAppFormat();
    public string PremiumDisplay => Premium.ConvertToAppFormat();
    public string RPMDisplay => RPM.ConvertToAppFormat();
    public string ILFDisplay => ILF.ConvertToAppFormat();

    public bool IsPartPlacement => Parts.Length > 0;
    
    public string StatusText => Status switch
    {
        UnderlyingStatus.Incomplete => Resource.Incomplete,
        UnderlyingStatus.Completed => Resource.Completed,
        _ => Resource.NotApplicable
    };
    
    public string StatusChipColor => Status switch
    {
        UnderlyingStatus.Incomplete => ThemeConstants.Chip.ThemeColor.Warning,
        UnderlyingStatus.Completed => ThemeConstants.Chip.ThemeColor.Success,
        _ => ThemeConstants.Chip.ThemeColor.Base
    };

    public static UnderlyingVm FromResponse(GetUnderlyingsResponseItem response)
    {
        return new UnderlyingVm
        {
            Layer = response.Layer,
            Limit = response.Limit,
            Excess = response.Excess,
            Premium = response.Premium,
            Status = response.Status,
            Insurer = response.Insurer,
            ILF = response.ILF,
            RPM = response.RPM,
            Parts = response.Parts.Select(UnderlyingPartVm.FromResponse).ToArray(),
            IsWholesale = response.IsWholesale
        };
    }
}

public record UnderlyingPartVm
{
    public required int Part { get; init; }
    public required decimal LineSize { get; init; }
    public required decimal Premium { get; init; }
    public required string Insurer { get; init; }
    public required bool IsQuotaShare { get; init; }

    public string LineSizeDisplay => LineSize.ConvertToAppFormat();
    public string PremiumDisplay => Premium.ConvertToAppFormat();

    public static UnderlyingPartVm FromResponse(GetUnderlyingsResponsePart response)
    {
        return new UnderlyingPartVm
        {
            Part = response.Part,
            LineSize = response.LineSize,
            Premium = response.Premium,
            Insurer = response.Insurer,
            IsQuotaShare = response.IsQuotaShare
        };
    }
}
